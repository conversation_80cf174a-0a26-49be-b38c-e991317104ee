{"name": "Clean HTTP Test", "nodes": [{"parameters": {}, "id": "clean-manual-trigger", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"method": "POST", "url": "http://host.docker.internal:1234/v1/chat/completions", "authentication": "none", "requestFormat": "json", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "body": "{\n  \"model\": \"mimo-vl-7b-rl@q8_k_xl\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"Hello test message\"\n    }\n  ],\n  \"temperature\": 0.1,\n  \"max_tokens\": 10\n}"}, "id": "clean-http-request", "name": "Clean HTTP Request", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [500, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Clean HTTP Request", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}