#!/usr/bin/env python3
"""Analyze all the workflow log files to trace execution"""

import json
import os
from pathlib import Path

def analyze_logs():
    """Analyze all log files to trace workflow execution."""
    print("🔍 FULL WORKFLOW EXECUTION ANALYSIS")
    print("=" * 60)
    
    log_dir = Path("C:/Docker_Share/N8N/")
    
    # Check for each expected log file
    expected_logs = [
        ("1_config_log.json", "Load Configuration"),
        ("2_scan_log.json", "Scan Folder for Videos"),
        ("3_process_log.json", "Process File List"),
        ("4_thumbnail_log.json", "Extract Video Thumbnail"),
        ("5_ai_log.json", "AI Video Analysis")
    ]
    
    found_logs = []
    last_successful_step = 0
    
    for i, (log_file, step_name) in enumerate(expected_logs, 1):
        log_path = log_dir / log_file
        
        if log_path.exists():
            found_logs.append((i, log_file, step_name, log_path))
            last_successful_step = i
            print(f"✅ Step {i}: {step_name} - LOG FOUND")
        else:
            print(f"❌ Step {i}: {step_name} - LOG MISSING (workflow stopped before this)")
            break
    
    print(f"\n🎯 WORKFLOW PROGRESSION:")
    print(f"   Last successful step: {last_successful_step}/5")
    
    if last_successful_step == 0:
        print("   ❌ Workflow failed immediately - no logs created")
        return
    elif last_successful_step == 5:
        print("   ✅ Workflow completed all major steps!")
    else:
        print(f"   ⚠️  Workflow stopped after step {last_successful_step}")
    
    # Analyze each found log
    print(f"\n📋 DETAILED LOG ANALYSIS:")
    print("-" * 40)
    
    for step_num, log_file, step_name, log_path in found_logs:
        print(f"\n{step_num}. {step_name.upper()}")
        print("-" * 30)
        
        try:
            with open(log_path, 'r', encoding='utf-8') as f:
                log_data = json.load(f)
            
            if step_num == 1:  # Config log
                print(f"   Has config: {log_data.get('has_config', False)}")
                print(f"   Config keys: {log_data.get('config_keys', [])}")
                print(f"   Video folder: {log_data.get('video_folder', 'missing')}")
                
            elif step_num == 2:  # Scan log
                print(f"   Exit code: {log_data.get('exit_code', 'unknown')}")
                print(f"   Files found: {log_data.get('files_found', 0)}")
                print(f"   Stdout length: {log_data.get('stdout_length', 0)}")
                if log_data.get('stdout_preview'):
                    print(f"   Files preview: {log_data['stdout_preview'][:100]}...")
                
            elif step_num == 3:  # Process log
                print(f"   Total inputs: {log_data.get('total_inputs', 0)}")
                files = log_data.get('files', [])
                for file_info in files[:3]:  # Show first 3
                    print(f"   File {file_info.get('index', '?')}: {file_info.get('filename', 'unknown')}")
                if len(files) > 3:
                    print(f"   ... and {len(files) - 3} more files")
                    
            elif step_num == 4:  # Thumbnail log
                print(f"   Total inputs: {log_data.get('total_inputs', 0)}")
                thumbnails = log_data.get('thumbnails', [])
                mock_count = sum(1 for t in thumbnails if t.get('isMock', False))
                print(f"   Mock thumbnails: {mock_count}/{len(thumbnails)}")
                
            elif step_num == 5:  # AI log
                print(f"   Total inputs: {log_data.get('total_inputs', 0)}")
                responses = log_data.get('ai_responses', [])
                success_count = sum(1 for r in responses if r.get('hasResponse', False))
                print(f"   Successful AI calls: {success_count}/{len(responses)}")
                
        except Exception as e:
            print(f"   ❌ Error reading log: {e}")
    
    # Summary and next steps
    print(f"\n🎯 SUMMARY:")
    print("-" * 20)
    
    if last_successful_step == 1:
        print("✅ Configuration loaded successfully")
        print("❌ Scan step failed - check file paths and permissions")
    elif last_successful_step == 2:
        print("✅ File scanning worked")
        print("❌ Process File List failed - check data format")
    elif last_successful_step == 3:
        print("✅ File processing worked")
        print("❌ Thumbnail extraction failed")
    elif last_successful_step == 4:
        print("✅ Thumbnail extraction worked")
        print("❌ AI analysis failed - check LM Studio connection")
    elif last_successful_step == 5:
        print("✅ All steps completed successfully!")
        print("🎉 Workflow is working end-to-end!")

if __name__ == "__main__":
    analyze_logs()
