#!/usr/bin/env python3
"""
Test the file-based FFmpeg processing system.
Creates a test request and verifies the processor handles it correctly.
"""

import os
import json
import time
import sys
from datetime import datetime

# Configuration
SHARED_FOLDER = "C:/Docker_Share/N8N"
FFMPEG_REQUESTS_FOLDER = os.path.join(SHARED_FOLDER, "ffmpeg_requests")
FFMPEG_RESULTS_FOLDER = os.path.join(SHARED_FOLDER, "ffmpeg_results")

def create_test_request():
    """Create a test FFmpeg request."""
    print("🧪 Creating test FFmpeg request...")
    
    # Ensure directories exist
    os.makedirs(FFMPEG_REQUESTS_FOLDER, exist_ok=True)
    os.makedirs(FFMPEG_RESULTS_FOLDER, exist_ok=True)
    
    # Generate test request
    request_id = f"test_{int(time.time())}"
    
    request_data = {
        "request_id": request_id,
        "filename": "20250406_110016_1.mp4",
        "full_path": "/home/<USER>/shared/kung_fu_videos/20250406_110016_1.mp4",
        "created_at": datetime.now().isoformat(),
        "processing_index": 1,
        "total_videos": 1
    }
    
    # Write request file
    request_file = os.path.join(FFMPEG_REQUESTS_FOLDER, f"request_{request_id}.json")
    
    with open(request_file, 'w', encoding='utf-8') as f:
        json.dump(request_data, f, indent=2)
    
    print(f"✅ Test request created: {request_file}")
    print(f"📋 Request ID: {request_id}")
    print(f"🎬 Video: {request_data['filename']}")
    
    return request_id

def wait_for_result(request_id, timeout=30):
    """Wait for the FFmpeg processor to create a result."""
    print(f"⏳ Waiting for result (timeout: {timeout}s)...")
    
    result_file = os.path.join(FFMPEG_RESULTS_FOLDER, f"result_{request_id}.json")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        if os.path.exists(result_file):
            print(f"✅ Result file found: {result_file}")
            return result_file
        
        time.sleep(1)
        print(".", end="", flush=True)
    
    print(f"\n❌ Timeout: No result after {timeout} seconds")
    return None

def analyze_result(result_file):
    """Analyze the FFmpeg processing result."""
    print(f"\n📊 Analyzing result: {result_file}")
    
    try:
        with open(result_file, 'r', encoding='utf-8') as f:
            result_data = json.load(f)
        
        print("📋 Result Summary:")
        print(f"  - Success: {result_data.get('success', False)}")
        print(f"  - Filename: {result_data.get('filename', 'unknown')}")
        print(f"  - Request ID: {result_data.get('request_id', 'unknown')}")
        
        if result_data.get('success'):
            print(f"  - Thumbnail size: {result_data.get('thumbnail_size_kb', 0)}KB")
            print(f"  - Extraction method: {result_data.get('extraction_method', 'unknown')}")
            print(f"  - Processed at: {result_data.get('processed_at', 'unknown')}")
            
            # Check thumbnail data
            thumbnail_b64 = result_data.get('thumbnail_base64', '')
            if thumbnail_b64:
                print(f"  - Base64 length: {len(thumbnail_b64)} characters")
                print(f"  - Starts with PNG signature: {thumbnail_b64.startswith('iVBORw0KGgo')}")
            else:
                print("  - ❌ No thumbnail data found")
        else:
            print(f"  - Error: {result_data.get('error', 'Unknown error')}")
            print(f"  - Exit code: {result_data.get('exit_code', 'unknown')}")
        
        return result_data.get('success', False)
        
    except Exception as e:
        print(f"❌ Error analyzing result: {e}")
        return False

def check_processor_running():
    """Check if the FFmpeg processor is running."""
    print("🔍 Checking if FFmpeg processor is running...")
    
    # Check if there are any request files that haven't been processed
    if os.path.exists(FFMPEG_REQUESTS_FOLDER):
        request_files = [f for f in os.listdir(FFMPEG_REQUESTS_FOLDER) 
                        if f.startswith('request_') and f.endswith('.json')]
        
        if request_files:
            print(f"⚠️  Found {len(request_files)} unprocessed requests")
            print("💡 Make sure the FFmpeg processor is running:")
            print("   python file_ffmpeg_processor.py")
            return False
    
    print("✅ No pending requests found")
    return True

def main():
    """Main test function."""
    print("🧪 File-Based FFmpeg Processing Test")
    print("=" * 40)
    
    # Check if processor might be running
    check_processor_running()
    
    # Create test request
    request_id = create_test_request()
    
    # Wait for result
    result_file = wait_for_result(request_id)
    
    if result_file:
        # Analyze result
        success = analyze_result(result_file)
        
        if success:
            print("\n🎉 TEST PASSED: File-based FFmpeg processing works!")
            print("✅ The system successfully:")
            print("   - Created FFmpeg request file")
            print("   - Processed video with FFmpeg")
            print("   - Extracted thumbnail as base64")
            print("   - Returned valid result")
        else:
            print("\n❌ TEST FAILED: FFmpeg processing failed")
            return 1
    else:
        print("\n❌ TEST FAILED: No result received")
        print("💡 Make sure:")
        print("   1. FFmpeg processor is running: python file_ffmpeg_processor.py")
        print("   2. Video file exists: C:/Docker_Share/N8N/kung_fu_videos/20250406_110016_1.mp4")
        print("   3. FFmpeg is installed and accessible")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
