{"name": "Kung Fu Video Detector - Hardcoded Test", "nodes": [{"parameters": {}, "id": "ed85e2e5-dc5e-4575-88eb-afd38180a7c8", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"jsCode": "// Load configuration for kung fu video detection\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',\n  recursive_search: true,\n  file_extensions: ['.mp4', '.avi', '.mov', '.mkv']\n};\n\nconsole.log('=== CONFIGURATION LOADED ===');\nconsole.log('Video folder:', config.video_folder);\n\nreturn [{ json: { config: config } }];"}, "id": "dee3c1e8-483c-4376-a78a-381b062af29f", "name": "Load Configuration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [500, 300]}, {"parameters": {"command": "find /home/<USER>/shared/kung_fu_videos -type f -iname \"*.mp4\" | head -1"}, "id": "05a21cf5-9a6b-4b45-a985-afad83287fac", "name": "Find First Video", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [750, 300]}, {"parameters": {"command": "ffmpeg -i /home/<USER>/shared/kung_fu_videos/20250406_110016_1.mp4 -ss 00:00:10 -vframes 1 -f image2pipe -vcodec png - | wc -c"}, "id": "hardcoded-ffmpeg-test", "name": "Test FFmpeg Hardcoded", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1000, 300]}, {"parameters": {"jsCode": "// Process test results\nconst input = $input.first().json;\nconst output = input.stdout || '';\nconst exitCode = input.exitCode || 0;\n\nconsole.log('=== TEST RESULTS ===');\nconsole.log('Exit code:', exitCode);\nconsole.log('Output:', output);\n\nreturn [{ json: { exitCode: exitCode, output: output, success: exitCode === 0 } }];"}, "id": "process-test-results", "name": "Process Test Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1250, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Load Configuration", "type": "main", "index": 0}]]}, "Load Configuration": {"main": [[{"node": "Find First Video", "type": "main", "index": 0}]]}, "Find First Video": {"main": [[{"node": "Test FFmpeg Hardcoded", "type": "main", "index": 0}]]}, "Test FFmpeg Hardcoded": {"main": [[{"node": "Process Test Results", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}