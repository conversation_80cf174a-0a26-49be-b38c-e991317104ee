# N8N HTTP Request Node POST Body Issue - Need Solution

## Problem Summary
N8N version 1.109.2 HTTP Request node (typeVersion 4) consistently fails to send POST request bodies correctly, resulting in "'messages' field is required" errors from the target API despite properly configured JSON payloads.

## Environment Details
- **N8N Version**: 1.109.2 (Self Hosted, Docker)
- **HTTP Request Node**: typeVersion 4 (latest available: 4.2)
- **Target API**: LM Studio vision API (localhost:1234/v1/chat/completions)
- **Deployment**: Docker Desktop on Windows 11
- **Use Case**: AI vision analysis workflow requiring base64 image data in JSON payload

## Detailed Issue Description

### Expected Behavior
HTTP Request nodes configured with POST method should send the configured JSON body to the target endpoint.

### Actual Behavior
HTTP Request nodes send empty or malformed request bodies, confirmed by:
1. **API error responses**: "'messages' field is required" 
2. **N8N error details**: `"body": { "": "" }` (empty body object)
3. **Consistent failure** across multiple configuration approaches

### Configurations Tested (All Failed)

#### Configuration 1: Standard JSON Body
```json
{
  "type": "n8n-nodes-base.httpRequest",
  "typeVersion": 4,
  "parameters": {
    "method": "POST",
    "url": "http://host.docker.internal:1234/v1/chat/completions",
    "sendHeaders": true,
    "headerParameters": {
      "parameters": [{"name": "Content-Type", "value": "application/json"}]
    },
    "sendBody": true,
    "jsonBody": "{\"model\":\"mimo-vl-7b-rl@q8_k_xl\",\"messages\":[{\"role\":\"user\",\"content\":\"Hello test\"}],\"temperature\":0.1,\"max_tokens\":5}"
  }
}
```

#### Configuration 2: Raw Body Content
```json
{
  "parameters": {
    "method": "POST",
    "url": "http://host.docker.internal:1234/v1/chat/completions",
    "sendHeaders": true,
    "headerParameters": {
      "parameters": [{"name": "Content-Type", "value": "application/json"}]
    },
    "sendBody": true,
    "bodyContentType": "raw",
    "body": "{\"model\":\"mimo-vl-7b-rl@q8_k_xl\",\"messages\":[{\"role\":\"user\",\"content\":\"Hello\"}],\"temperature\":0.1,\"max_tokens\":5}"
  }
}
```

#### Configuration 3: Expression-Based Body
```json
{
  "parameters": {
    "method": "POST",
    "url": "http://host.docker.internal:1234/v1/chat/completions",
    "sendHeaders": true,
    "headerParameters": {
      "parameters": [{"name": "Content-Type", "value": "application/json"}]
    },
    "sendBody": true,
    "body": "={{ JSON.stringify($json.requestData) }}"
  }
}
```

#### Configuration 4: Code Node + HTTP Request
```json
{
  "codeNode": {
    "jsCode": "const requestData = {model: 'mimo-vl-7b-rl@q8_k_xl', messages: [{role: 'user', content: 'Hello'}], temperature: 0.1, max_tokens: 5}; return [{json: requestData}];"
  },
  "httpRequestNode": {
    "body": "={{ JSON.stringify($json) }}"
  }
}
```

### Consistent Error Pattern
All configurations produce the same error:
```json
{
  "errorMessage": "Bad request - please check your parameters",
  "errorDescription": "'messages' field is required",
  "errorDetails": {
    "rawErrorMessage": ["400 - \"{\\\"error\\\":\\\"'messages' field is required\\\"}\""],
    "httpCode": "400"
  }
}
```

**N8N Request Debug Info:**
```json
{
  "body": { "": "" },
  "headers": { "content-type": "application/json" },
  "method": "POST",
  "uri": "http://host.docker.internal:1234/v1/chat/completions"
}
```

## Verification Tests Performed

### Network Connectivity (✅ Working)
- **GET requests work**: `GET /v1/models` returns proper JSON response
- **Network access confirmed**: `ping host.docker.internal` successful
- **Port accessibility**: `nc -zv host.docker.internal 1234` shows port open
- **External tools work**: `curl` and `Invoke-WebRequest` successfully POST to same endpoint

### API Endpoint Validation (✅ Working)
```powershell
# This works perfectly from host system:
$body = @{
    model = "mimo-vl-7b-rl@q8_k_xl"
    messages = @(@{role = "user"; content = "Hello"})
    temperature = 0.1
    max_tokens = 5
} | ConvertTo-Json -Depth 3

Invoke-WebRequest -Uri "http://localhost:1234/v1/chat/completions" -Method POST -Body $body -ContentType "application/json"
# Returns: 200 OK with proper AI response
```

### N8N Basic Functionality (✅ Working)
- **GET requests work**: HTTP Request node successfully retrieves data
- **Network connectivity**: N8N container can reach target endpoint
- **JSON parsing**: N8N properly handles response JSON from GET requests

## Critical Use Case Impact

### Workflow Requirements
Our workflow requires:
1. **Dynamic base64 image injection**: `"url": "data:image/png;base64,{{ $json.thumbnailBase64 }}"`
2. **Vision API calls**: Multimodal content (text + image) for AI analysis
3. **Large payloads**: Base64 images can be 50KB+ in JSON body
4. **Templating support**: Dynamic data injection from previous workflow nodes

### Current Blockers
- **HTTP Request node POST broken**: Cannot send any POST request bodies
- **Code nodes limited**: No HTTP request capabilities (`fetch` not available)
- **executeCommand limitations**: Cannot handle templating or large base64 data
- **Workflow unusable**: Core functionality completely blocked

## Questions for Community

1. **Is this a known bug** in N8N 1.109.2 HTTP Request node typeVersion 4?
2. **Are there workarounds** for POST request body issues in latest N8N versions?
3. **Should we downgrade** to an earlier N8N version where HTTP Request works?
4. **Alternative approaches** for making HTTP POST requests with JSON bodies in N8N?
5. **Configuration errors** we might be missing in the HTTP Request node setup?

## Additional Context

### Previous N8N Version Behavior
- **N8N 1.99.0**: Had different issues (POST→GET method conversion) but was upgradeable
- **Current 1.109.2**: POST method works but body content is completely broken
- **Upgrade impact**: Issue only appeared after upgrading to resolve previous HTTP method bug

### Workaround Attempts
- **External proxy server**: Considering Python Flask proxy to handle HTTP requests
- **File-based communication**: Write JSON to shared folder, external script processes
- **API alternatives**: Different endpoints or request formats

### Business Impact
- **Workflow completely blocked**: Cannot proceed with AI vision analysis
- **Development stalled**: Core N8N functionality appears broken
- **Time investment lost**: Significant effort spent on troubleshooting

## Request for Help

Looking for:
1. **Confirmed working configuration** for HTTP Request node POST with JSON body
2. **Known issues documentation** for N8N 1.109.2 HTTP Request node
3. **Alternative approaches** within N8N for making HTTP POST requests
4. **Version recommendations** if downgrade is necessary
5. **Community experiences** with similar HTTP Request node issues

Any guidance would be greatly appreciated as this is blocking critical workflow functionality.
