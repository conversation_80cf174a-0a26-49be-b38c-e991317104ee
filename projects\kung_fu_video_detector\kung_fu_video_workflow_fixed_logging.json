{"name": "My workflow 4", "nodes": [{"parameters": {}, "id": "ed85e2e5-dc5e-4575-88eb-afd38180a7c8", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-2800, -80]}, {"parameters": {"jsCode": "// Embedded configuration - Uses existing Docker share setup\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',  // Using existing Docker share mount\n  recursive_search: true,\n  file_extensions: ['.mp4', '.avi', '.mov', '.mkv'],\n  lm_studio: {\n    endpoint: 'http://host.docker.internal:1234/v1/chat/completions',  // Docker host access\n    model: 'mimo-vl-7b-rl@q8_k_xl',\n    timeout: 30000,\n    temperature: 0.1,\n    max_tokens: 50\n  },\n  ai_prompt: 'Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training. Look for martial arts poses, fighting stances, training equipment, or combat movements. Please respond with only YES if it shows kung fu/martial arts practice, or NO if it does not.',\n  ffmpeg_path: 'ffmpeg',\n  detection_keywords: [\n    'yes', 'kung fu', 'martial arts', 'karate', 'taekwondo',\n    'fighting', 'combat', 'training', 'practice'\n  ]\n};\n\nconsole.log('=== CONFIGURATION LOADED ===');\nconsole.log('Video folder:', config.video_folder);\nconsole.log('Recursive search:', config.recursive_search);\nconsole.log('File extensions:', config.file_extensions);\nconsole.log('LM Studio endpoint:', config.lm_studio.endpoint);\nconsole.log('LM Studio model:', config.lm_studio.model);\n\nreturn [{ json: { config: config } }];"}, "id": "dee3c1e8-483c-4376-a78a-381b062af29f", "name": "Load Configuration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2580, -80]}, {"parameters": {"jsCode": "// Get configuration from previous node and build Linux find command\nconst input = $input.first().json;\nconst config = input.config;\n\nconsole.log('=== PREPARING VIDEO SCAN ===');\nconsole.log('Video folder:', config.video_folder);\nconsole.log('Recursive search:', config.recursive_search);\nconsole.log('File extensions:', config.file_extensions);\n\n// Use the Docker mount path directly\nconst folderPath = config.video_folder;\nconsole.log('Using Docker mount path:', folderPath);\n\n// Build find command for Linux\nconst recursiveFlag = config.recursive_search ? '' : '-maxdepth 1';\n\n// Create find command with multiple extensions\nconst extensionPatterns = config.file_extensions.map(ext => `-name \"*${ext}\"`).join(' -o ');\nconst findCommand = `find \"${folderPath}\" ${recursiveFlag} -type f \\\\( ${extensionPatterns} \\\\) 2>/dev/null`;\n\nconsole.log('Find command:', findCommand);\n\nreturn [{\n  json: {\n    config: config,\n    findCommand: findCommand,\n    folderPath: folderPath\n  }\n}];"}, "id": "c9b53082-af88-4093-960d-891dacb989a3", "name": "Prepare Video Scan", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2360, -80]}, {"parameters": {"command": "find \"/home/<USER>/shared/kung_fu_videos\" -type f \\( -iname \"*.mp4\" -o -iname \"*.avi\" -o -iname \"*.mov\" -o -iname \"*.mkv\" \\) 2>/dev/null"}, "id": "05a21cf5-9a6b-4b45-a985-afad83287fac", "name": "<PERSON>an Folder for Videos", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-2140, -80]}, {"parameters": {"jsCode": "// Process the find command output to create file list\nconst input = $input.first().json;\nconst commandOutput = input.stdout || '';\nconst commandError = input.stderr || '';\nconst exitCode = input.exitCode || 0;\n\n// Create default config since executeCommand node doesn't pass it\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',\n  recursive_search: true,\n  file_extensions: ['.mp4', '.avi', '.mov', '.mkv'],\n  lm_studio: {\n    endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n    model: 'mimo-vl-7b-rl@q8_k_xl',\n    timeout: 30000,\n    temperature: 0.1,\n    max_tokens: 50\n  },\n  ai_prompt: 'Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training.',\n  ffmpeg_path: 'ffmpeg',\n  detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n};\n\nconst folderPath = config.video_folder;\n\nconsole.log('=== PROCESSING VIDEO FILE SCAN ===');\nconsole.log('Folder path:', folderPath);\nconsole.log('Recursive search:', config.recursive_search);\nconsole.log('Extensions:', config.file_extensions);\nconsole.log('Command output length:', commandOutput.length);\nconsole.log('Exit code:', exitCode);\n\n// Check for command errors\nif (exitCode !== 0) {\n  console.log('❌ Find command failed');\n  console.log('Error:', commandError);\n  return [{ json: { \n    error: `Cannot access folder: ${folderPath}. Error: ${commandError}`, \n    folderPath,\n    config\n  }}];\n}\n\nif (!commandOutput || commandOutput.trim() === '') {\n  console.log('No video files found in folder');\n  return [{ json: { \n    error: 'No video files found in the specified folder', \n    folderPath,\n    config,\n    note: `Searched for extensions: ${config.file_extensions.join(', ')}`\n  }}];\n}\n\n// Split output into individual files and clean up (Linux paths use forward slashes)\nconst fileLines = commandOutput.trim().split('\\n').filter(line => line.trim() !== '');\nconst videoFiles = [];\n\nfor (const line of fileLines) {\n  const cleanLine = line.trim();\n  if (cleanLine) {\n    // Extract just the filename from full path (Linux path)\n    const filename = cleanLine.split('/').pop();\n    \n    // Check if file has one of the configured extensions\n    const hasValidExtension = config.file_extensions.some(ext => \n      filename.toLowerCase().endsWith(ext.toLowerCase())\n    );\n    \n    if (filename && hasValidExtension) {\n      videoFiles.push({\n        filename: filename,\n        fullPath: cleanLine,\n        folderPath: folderPath,\n        config: config\n      });\n    }\n  }\n}\n\nconsole.log(`Found ${videoFiles.length} video files:`);\nvideoFiles.forEach(file => console.log(`  - ${file.filename}`));\n\nif (videoFiles.length === 0) {\n  return [{ json: { error: 'No valid video files found after processing', folderPath, config } }];\n}\n\n// Return each file as a separate item for processing\nreturn videoFiles.map(file => ({ json: file }));"}, "id": "8d8dfd08-5390-4441-a960-604631bd3a8a", "name": "Process File List", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1920, -80]}, {"parameters": {"jsCode": "// Mock thumbnail extraction for demonstration\n// In production, this would use an external FFmpeg service or N8N's file processing\nconst input = $input.first().json;\nconst filename = input.filename;\nconst fullPath = input.fullPath;\nconst config = input.config;\n\nconsole.log('=== MOCK THUMBNAIL EXTRACTION ===');\nconsole.log(`File: ${filename}`);\nconsole.log(`Full path: ${fullPath}`);\n\n// Create a mock base64 image (1x1 pixel PNG) for demonstration\n// In production, this would be replaced with actual FFmpeg thumbnail extraction\nconst mockThumbnailBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';\n\nconsole.log('✅ Mock thumbnail created (1x1 pixel PNG)');\nconsole.log('Note: In production, replace with actual FFmpeg extraction');\n\n// Return mock thumbnail data\nreturn [{\n  json: {\n    filename: filename,\n    fullPath: fullPath,\n    config: config,\n    thumbnailBase64: mockThumbnailBase64,\n    hasValidThumbnail: true,\n    isMockThumbnail: true\n  }\n}];"}, "id": "53e8d6ae-acbc-4f48-ae64-3b7e8e876606", "name": "Extract Video Thumbnail", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1700, -80]}, {"parameters": {"jsCode": "// Process thumbnail extraction result and prepare for AI analysis\nconst input = $input.first().json;\nconst thumbnailBase64 = input.stdout?.trim() || '';\nconst filename = input.filename;\nconst fullPath = input.fullPath;\nconst config = input.config;\n\nconsole.log('=== PROCESSING THUMBNAIL EXTRACTION ===');\nconsole.log(`File: ${filename}`);\nconsole.log(`Thumbnail extraction result length: ${thumbnailBase64.length}`);\n\n// Check if thumbnail extraction was successful\nif (!thumbnailBase64 || thumbnailBase64.includes('ERROR') || thumbnailBase64.length < 100) {\n  console.log('❌ Thumbnail extraction failed');\n  return [{\n    json: {\n      filename: filename,\n      fullPath: fullPath,\n      config: config,\n      error: 'Failed to extract video thumbnail',\n      thumbnailBase64: null\n    }\n  }];\n}\n\nconsole.log('✅ Thumbnail extracted successfully');\n\nreturn [{\n  json: {\n    filename: filename,\n    fullPath: fullPath,\n    config: config,\n    thumbnailBase64: thumbnailBase<PERSON>,\n    hasValidThumbnail: true\n  }\n}];"}, "id": "94f5e89e-58d7-4cfa-a209-77ff9dae38ec", "name": "Process Thumbnail", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1480, -80]}, {"parameters": {"jsCode": "// AI Video Analysis using LM Studio vision model\nconst input = $input.first().json;\nconst filename = input.filename;\nconst thumbnailBase64 = input.thumbnailBase64;\nconst config = input.config;\n\nconsole.log('=== AI VIDEO ANALYSIS ===');\nconsole.log(`File: ${filename}`);\nconsole.log(`Has thumbnail: ${!!thumbnailBase64}`);\nconsole.log(`Is mock thumbnail: ${input.isMockThumbnail}`);\nconsole.log(`LM Studio endpoint: ${config.lm_studio.endpoint}`);\nconsole.log(`Model: ${config.lm_studio.model}`);\n\n// Check if we have a valid thumbnail\nif (!thumbnailBase64) {\n  console.log('❌ No thumbnail available for analysis');\n  return [{\n    json: {\n      filename: filename,\n      config: config,\n      error: 'No thumbnail available for AI analysis',\n      isKungFu: false,\n      confidence: 0\n    }\n  }];\n}\n\n// Prepare the vision model request\nconst requestBody = {\n  model: config.lm_studio.model,\n  messages: [{\n    role: 'user',\n    content: [\n      {\n        type: 'text',\n        text: config.ai_prompt\n      },\n      {\n        type: 'image_url',\n        image_url: {\n          url: `data:image/jpeg;base64,${thumbnailBase64}`\n        }\n      }\n    ]\n  }],\n  temperature: config.lm_studio.temperature,\n  max_tokens: config.lm_studio.max_tokens\n};\n\nconsole.log('🤖 Sending request to LM Studio vision model...');\n\ntry {\n  // Make HTTP request to LM Studio\n  const response = await $http.post(config.lm_studio.endpoint, requestBody, {\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    timeout: config.lm_studio.timeout\n  });\n  \n  console.log('✅ LM Studio response received');\n  \n  const aiResponse = response.data?.choices?.[0]?.message?.content || 'No response';\n  console.log(`AI Response: ${aiResponse}`);\n  \n  // Analyze the response for kung fu indicators\n  const responseText = aiResponse.toLowerCase();\n  const isKungFu = config.detection_keywords.some(keyword => \n    responseText.includes(keyword.toLowerCase())\n  );\n  \n  // Calculate confidence based on response clarity\n  let confidence = 0.5;\n  if (responseText.includes('yes') || responseText.includes('no')) {\n    confidence = 0.8;\n  }\n  if (responseText.includes('definitely') || responseText.includes('clearly')) {\n    confidence = 0.9;\n  }\n  \n  console.log(`🥋 Analysis Result: ${isKungFu ? 'KUNG FU DETECTED' : 'NO KUNG FU'} (${Math.round(confidence * 100)}% confidence)`);\n  \n  return [{\n    json: {\n      filename: filename,\n      config: config,\n      isKungFu: isKungFu,\n      confidence: confidence,\n      aiResponse: aiResponse,\n      rawResponse: response.data,\n      analysisTimestamp: new Date().toISOString()\n    }\n  }];\n  \n} catch (error) {\n  console.log('❌ LM Studio request failed:', error.message);\n  \n  // Fallback to mock result if LM Studio is unavailable\n  const isKungFu = Math.random() > 0.5;\n  const confidence = 0.3; // Lower confidence for fallback\n  \n  console.log(`🎭 Fallback Mock Result: ${isKungFu ? 'KUNG FU' : 'NOT KUNG FU'} (${Math.round(confidence * 100)}% confidence)`);\n  \n  return [{\n    json: {\n      filename: filename,\n      config: config,\n      isKungFu: isKungFu,\n      confidence: confidence,\n      aiResponse: `Fallback result - LM Studio error: ${error.message}`,\n      error: error.message,\n      isFallbackResult: true\n    }\n  }];\n}"}, "id": "7aca14f2-b38c-45c7-9246-d936ac98523b", "name": "AI Video Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1260, -80]}, {"parameters": {"jsCode": "// Extract the AI response and determine if it's kung fu\nconst input = $input.first().json;\nconst aiResponse = input.choices?.[0]?.message?.content || '';\nconst filename = input.filename;\nconst fullPath = input.fullPath;\nconst config = input.config;\n\nconsole.log('=== PROCESSING AI RESPONSE ===');\nconsole.log(`File: ${filename}`);\nconsole.log(`AI Response: ${aiResponse}`);\nconsole.log(`Detection keywords: ${config.detection_keywords}`);\n\n// Check if AI response indicates kung fu/martial arts using configured keywords\nconst responseText = aiResponse.toLowerCase();\nconst isKungFu = config.detection_keywords.some(keyword => \n  responseText.includes(keyword.toLowerCase())\n);\n\nconsole.log(`Is Kung Fu: ${isKungFu}`);\nif (isKungFu) {\n  const matchedKeywords = config.detection_keywords.filter(keyword => \n    responseText.includes(keyword.toLowerCase())\n  );\n  console.log(`Matched keywords: ${matchedKeywords.join(', ')}`);\n}\n\nreturn [{\n  json: {\n    filename: filename,\n    fullPath: fullPath,\n    aiResponse: aiResponse,\n    isKungFu: isKungFu,\n    config: config,\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "ba32d901-fdc2-4123-820b-d6b119b51003", "name": "Process AI Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1040, -80]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.isKungFu }}", "value2": true}]}}, "id": "3a057643-21f7-4760-bed5-7cad13855449", "name": "Filter Kung Fu Videos", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-820, -80]}, {"parameters": {"jsCode": "// Collect all kung fu video filenames into a final array\nconst allItems = $input.all();\nconst kungFuVideos = allItems\n  .filter(item => item.json.isKungFu)\n  .map(item => item.json.filename);\n\nconsole.log('=== COLLECTING FINAL RESULTS ===');\nconsole.log(`Total items processed: ${allItems.length}`);\nconsole.log(`Kung Fu videos found: ${kungFuVideos.length}`);\nkungFuVideos.forEach(video => console.log(`  ✅ ${video}`));\n\nreturn [{\n  json: {\n    kungFuVideos: kungFuVideos,\n    totalFound: kungFuVideos.length,\n    timestamp: new Date().toISOString(),\n    summary: `Found ${kungFuVideos.length} kung fu practice videos`\n  }\n}];"}, "id": "5470ad10-2afd-43f4-9e3d-da82f3dfcbf0", "name": "Collect Final Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-600, -140]}, {"parameters": {"jsCode": "// Handle errors and failed analyses\nconst allItems = $input.all();\nconst errors = allItems\n  .filter(item => item.json.error)\n  .map(item => ({\n    filename: item.json.filename || 'unknown',\n    error: item.json.error\n  }));\n\nconsole.log('=== HANDLING ERRORS ===');\nconsole.log(`Total errors: ${errors.length}`);\nerrors.forEach(error => console.log(`  ❌ ${error.filename}: ${error.error}`));\n\nreturn [{\n  json: {\n    errors: errors,\n    errorCount: errors.length,\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "44f36a40-3abf-4c18-96f5-5a26921efce2", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-600, 20]}, {"parameters": {"jsCode": "// Create comprehensive execution log for debugging\nconst allInputs = $input.all();\nconst timestamp = new Date().toISOString();\n\nconst executionLog = {\n  timestamp: timestamp,\n  workflow_name: 'Kung Fu Video Detector',\n  execution_summary: {\n    total_inputs: allInputs.length,\n    execution_time: timestamp,\n    status: 'completed'\n  },\n  node_outputs: [],\n  debug_info: {\n    docker_mount: '/home/<USER>/shared/kung_fu_videos',\n    lm_studio_endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n    expected_files: 5\n  }\n};\n\n// Process each input and log details\nallInputs.forEach((input, index) => {\n  const data = input.json;\n  \n  executionLog.node_outputs.push({\n    input_index: index,\n    filename: data.filename || 'unknown',\n    fullPath: data.fullPath || 'unknown',\n    hasConfig: !!data.config,\n    hasThumbnail: !!data.thumbnailBase64,\n    isKungFu: data.isKungFu || false,\n    aiResponse: data.aiResponse || 'no response',\n    error: data.error || null,\n    timestamp: data.timestamp || 'unknown'\n  });\n});\n\n// Add summary statistics\nexecutionLog.summary = {\n  total_videos_processed: allInputs.length,\n  kung_fu_videos_found: allInputs.filter(input => input.json.isKungFu).length,\n  errors_encountered: allInputs.filter(input => input.json.error).length,\n  successful_ai_calls: allInputs.filter(input => input.json.aiResponse && !input.json.error).length\n};\n\nconsole.log('=== EXECUTION LOG SUMMARY ===');\nconsole.log(`Total videos processed: ${executionLog.summary.total_videos_processed}`);\nconsole.log(`Kung fu videos found: ${executionLog.summary.kung_fu_videos_found}`);\nconsole.log(`Errors encountered: ${executionLog.summary.errors_encountered}`);\nconsole.log(`Successful AI calls: ${executionLog.summary.successful_ai_calls}`);\n\n// Create log content for file (writeBinaryFile expects 'data' property)\nconst logContent = JSON.stringify(executionLog, null, 2);\nconst filename = `kung_fu_detector_log_${timestamp.replace(/[:.]/g, '-')}.json`;\n\nreturn [{\n  json: {\n    data: logContent,\n    filename: filename,\n    summary: executionLog.summary,\n    timestamp: timestamp\n  }\n}];"}, "id": "create-execution-log", "name": "Create Execution Log", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-380, -80]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/' + $json.filename }}", "options": {"overwrite": true}}, "id": "write-log-file", "name": "Write Log to Shared Folder", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [-160, -80]}, {"parameters": {"jsCode": "// Log scan results immediately\nconst input = $input.first().json;\nconst timestamp = new Date().toISOString();\n\nconst logData = {\n  step: 'scan_folder_immediate',\n  timestamp: timestamp,\n  input_received: !!input,\n  stdout: input.stdout || 'no stdout',\n  stderr: input.stderr || 'no stderr',\n  error: input.error || null,\n  raw_data: input\n};\n\nconsole.log('=== IMMEDIATE SCAN LOG ===');\nconsole.log('Input received:', !!input);\nconsole.log('Stdout:', logData.stdout);\n\n// Prepare data for writeBinaryFile (correct pattern from DNS reports)\nconst jsonString = JSON.stringify(logData, null, 2);\n\nconst binaryData = {\n  data: Buffer.from(jsonString, 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'immediate_scan_log.json',\n  fileExtension: 'json'\n};\n\nreturn [{\n  json: {\n    filename: 'immediate_scan_log.json'\n  },\n  binary: {\n    data: binaryData\n  }\n}];"}, "id": "immediate-scan-log", "name": "Immediate Scan Log", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1400, 200]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/' + $json.filename }}", "options": {"overwrite": true}}, "id": "write-immediate-scan-log", "name": "Write Immediate Scan Log", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [-1200, 200]}, {"parameters": {"jsCode": "// Log process file list results\nconst allInputs = $input.all();\nconst timestamp = new Date().toISOString();\n\nconst logData = {\n  step: 'process_file_list',\n  timestamp: timestamp,\n  total_inputs: allInputs.length,\n  processed_files: allInputs.map((input, index) => ({\n    index: index,\n    filename: input.json.filename || 'unknown',\n    fullPath: input.json.fullPath || 'unknown',\n    hasConfig: !!input.json.config,\n    error: input.json.error || null\n  }))\n};\n\nconsole.log('=== PROCESS FILE LIST LOG ===');\nconsole.log(`Total files processed: ${logData.total_inputs}`);\n\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'process_file_list_log.json',\n  fileExtension: 'json'\n};\n\nreturn [{\n  json: {\n    filename: 'process_file_list_log.json'\n  },\n  binary: {\n    data: binaryData\n  }\n}];"}, "id": "log-process-file-list", "name": "Log Process File List", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1600, 200]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/' + $json.filename }}", "options": {"overwrite": true}}, "id": "write-process-log", "name": "Write Process Log", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [-1400, 300]}], "pinData": {}, "connections": {"Prepare Video Scan": {"main": [[{"node": "<PERSON>an Folder for Videos", "type": "main", "index": 0}]]}, "Scan Folder for Videos": {"main": [[{"node": "Process File List", "type": "main", "index": 0}, {"node": "Immediate Scan Log", "type": "main", "index": 0}]]}, "Process File List": {"main": [[{"node": "Extract Video Thumbnail", "type": "main", "index": 0}, {"node": "Log Process File List", "type": "main", "index": 0}]]}, "Extract Video Thumbnail": {"main": [[{"node": "Process Thumbnail", "type": "main", "index": 0}]]}, "Process Thumbnail": {"main": [[{"node": "AI Video Analysis", "type": "main", "index": 0}]]}, "AI Video Analysis": {"main": [[{"node": "Process AI Response", "type": "main", "index": 0}]]}, "Process AI Response": {"main": [[{"node": "Filter Kung Fu Videos", "type": "main", "index": 0}]]}, "Filter Kung Fu Videos": {"main": [[{"node": "Collect Final Results", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Manual Trigger": {"main": [[{"node": "Load Configuration", "type": "main", "index": 0}]]}, "Load Configuration": {"main": [[{"node": "Prepare Video Scan", "type": "main", "index": 0}]]}, "Collect Final Results": {"main": [[{"node": "Create Execution Log", "type": "main", "index": 0}]]}, "Create Execution Log": {"main": [[{"node": "Write Log to Shared Folder", "type": "main", "index": 0}]]}, "Immediate Scan Log": {"main": [[{"node": "Write Immediate Scan Log", "type": "main", "index": 0}]]}, "Log Process File List": {"main": [[{"node": "Write Process Log", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "cd3ba402-afd8-451a-b1c6-010afd3002cf", "meta": {"instanceId": "a9e00de748ec35ee88db078f832d6e48181d32e4fa741d36554310dd025f8599"}, "id": "1zht5ntNGGldBera", "tags": []}