#!/usr/bin/env python3
"""
Validate N8N workflow connections to ensure no disconnected nodes.
This script analyzes the workflow JSON to verify all connections are proper.
"""

import json

def validate_workflow_connections(workflow_file):
    """Validate that all nodes in the workflow are properly connected."""
    print(f"=== VALIDATING WORKFLOW CONNECTIONS: {workflow_file} ===")
    
    try:
        with open(workflow_file, 'r') as f:
            workflow = json.load(f)
    except Exception as e:
        print(f"❌ Error loading workflow file: {e}")
        return False
    
    nodes = workflow.get('nodes', [])
    connections = workflow.get('connections', {})
    
    print(f"Nodes: {len(nodes)}")
    print(f"Connection groups: {len(connections)}")
    
    # Create a map of node names to IDs
    node_names = {}
    node_ids = {}
    for node in nodes:
        name = node.get('name', '')
        node_id = node.get('id', '')
        node_type = node.get('type', '')
        node_names[name] = {
            'id': node_id,
            'type': node_type,
            'position': node.get('position', [0, 0])
        }
        node_ids[node_id] = name
    
    print("\n=== NODE INVENTORY ===")
    for i, (name, info) in enumerate(node_names.items(), 1):
        print(f"  {i}. {name} ({info['type']}) - ID: {info['id']} - Position: {info['position']}")
    
    # Validate connections
    print("\n=== CONNECTION ANALYSIS ===")
    
    connected_nodes = set()
    connection_errors = []
    
    # Check each connection
    for source_name, connection_data in connections.items():
        if source_name not in node_names:
            connection_errors.append(f"Source node '{source_name}' not found in nodes list")
            continue
        
        connected_nodes.add(source_name)
        
        main_connections = connection_data.get('main', [])
        for connection_group in main_connections:
            for connection in connection_group:
                target_name = connection.get('node', '')
                connection_type = connection.get('type', '')
                connection_index = connection.get('index', 0)
                
                if target_name not in node_names:
                    connection_errors.append(f"Target node '{target_name}' not found in nodes list")
                    continue
                
                connected_nodes.add(target_name)
                print(f"  ✅ {source_name} → {target_name} (type: {connection_type}, index: {connection_index})")
    
    # Find disconnected nodes
    all_node_names = set(node_names.keys())
    disconnected_nodes = all_node_names - connected_nodes
    
    # Check for trigger nodes (they don't need incoming connections)
    trigger_types = ['n8n-nodes-base.manualTrigger', 'n8n-nodes-base.cronTrigger', 'n8n-nodes-base.webhook']
    trigger_nodes = set()
    for name, info in node_names.items():
        if info['type'] in trigger_types:
            trigger_nodes.add(name)
    
    # Remove trigger nodes from disconnected list (they don't need incoming connections)
    truly_disconnected = disconnected_nodes - trigger_nodes
    
    print(f"\n=== CONNECTION SUMMARY ===")
    print(f"Total nodes: {len(all_node_names)}")
    print(f"Connected nodes: {len(connected_nodes)}")
    print(f"Trigger nodes: {len(trigger_nodes)} - {list(trigger_nodes)}")
    print(f"Disconnected nodes: {len(truly_disconnected)}")
    
    if truly_disconnected:
        print(f"❌ DISCONNECTED NODES FOUND:")
        for node in truly_disconnected:
            print(f"   - {node} ({node_names[node]['type']})")
    
    if connection_errors:
        print(f"❌ CONNECTION ERRORS:")
        for error in connection_errors:
            print(f"   - {error}")
    
    # Validate connection chain
    print(f"\n=== CONNECTION CHAIN VALIDATION ===")
    
    # Find the trigger node (starting point)
    trigger_node = None
    for name, info in node_names.items():
        if info['type'] in trigger_types:
            trigger_node = name
            break
    
    if not trigger_node:
        print("❌ No trigger node found")
        return False
    
    print(f"Starting from trigger: {trigger_node}")
    
    # Trace the connection chain
    visited = set()
    chain = []
    current = trigger_node
    
    while current and current not in visited:
        visited.add(current)
        chain.append(current)
        
        # Find next node
        next_node = None
        if current in connections:
            main_connections = connections[current].get('main', [])
            if main_connections and main_connections[0]:
                next_node = main_connections[0][0].get('node')
        
        current = next_node
    
    print(f"Connection chain length: {len(chain)}")
    print(f"Chain: {' → '.join(chain)}")
    
    # Check if all nodes are in the chain
    nodes_in_chain = set(chain)
    nodes_not_in_chain = all_node_names - nodes_in_chain
    
    if nodes_not_in_chain:
        print(f"❌ NODES NOT IN MAIN CHAIN:")
        for node in nodes_not_in_chain:
            print(f"   - {node} ({node_names[node]['type']})")
    
    # Final validation
    success = (
        len(connection_errors) == 0 and
        len(truly_disconnected) == 0 and
        len(nodes_not_in_chain) == 0
    )
    
    print(f"\n=== VALIDATION RESULT ===")
    if success:
        print("✅ WORKFLOW CONNECTIONS ARE VALID!")
        print("   - All nodes are properly connected")
        print("   - No connection errors found")
        print("   - Complete connection chain established")
        return True
    else:
        print("❌ WORKFLOW CONNECTIONS HAVE ISSUES!")
        print("   - Check the errors listed above")
        print("   - Fix disconnected nodes")
        print("   - Verify connection chain completeness")
        return False

def main():
    """Main function to validate workflow connections."""
    workflows_to_check = [
        "kung_fu_video_workflow_clean_ffmpeg.json"
    ]
    
    for workflow_file in workflows_to_check:
        try:
            print(f"\n{'='*60}")
            result = validate_workflow_connections(workflow_file)
            print(f"Result for {workflow_file}: {'PASS' if result else 'FAIL'}")
        except FileNotFoundError:
            print(f"❌ File not found: {workflow_file}")
        except Exception as e:
            print(f"❌ Error validating {workflow_file}: {e}")

if __name__ == "__main__":
    main()
