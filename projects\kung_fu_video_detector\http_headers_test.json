{"name": "HTTP Headers Proxy Test", "nodes": [{"parameters": {}, "id": "manual-trigger-headers-test", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"jsCode": "// Create test data with a small valid base64 image\n// This is a 1x1 red pixel PNG\nconst testBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';\n\nconst testData = {\n  thumbnailBase64: testBase64,\n  filename: 'test_video_headers.mp4'\n};\n\nconsole.log('=== HTTP HEADERS TEST DATA ===');\nconsole.log('Thumbnail length:', testData.thumbnailBase64.length);\nconsole.log('Filename:', testData.filename);\nconsole.log('Base64 sample:', testData.thumbnailBase64.substring(0, 50) + '...');\n\nreturn [{ json: testData }];"}, "id": "create-test-data-headers", "name": "Create Test Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [450, 300]}, {"parameters": {"url": "http://host.docker.internal:8081/analyze-video-thumbnail", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-<PERSON><PERSON><PERSON><PERSON>", "value": "={{ $json.thumbnailBase64 }}"}, {"name": "X-Filename", "value": "={{ $json.filename }}"}, {"name": "X-Prompt", "value": "Test prompt: Is this kung fu? Please respond with YES or NO."}]}, "options": {"timeout": 30000}}, "id": "http-headers-request", "name": "HTTP Headers Request", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [650, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Create Test Data", "type": "main", "index": 0}]]}, "Create Test Data": {"main": [[{"node": "HTTP Headers Request", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}