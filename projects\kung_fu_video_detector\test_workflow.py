#!/usr/bin/env python3
"""
Test script for the Kung Fu Video Detector workflow
Validates the workflow JSON structure and tests LM Studio connectivity
"""

import json
import requests
import os
from pathlib import Path

def load_workflow():
    """Load and validate the workflow JSON file."""
    workflow_path = Path(__file__).parent / "kung_fu_video_workflow.json"
    
    try:
        with open(workflow_path, 'r', encoding='utf-8') as f:
            workflow = json.load(f)
        print("✅ Workflow JSON loaded successfully")
        return workflow
    except Exception as e:
        print(f"❌ Failed to load workflow: {e}")
        return None

def validate_workflow_structure(workflow):
    """Validate the basic structure of the workflow."""
    required_fields = ['name', 'nodes', 'connections']
    
    for field in required_fields:
        if field not in workflow:
            print(f"❌ Missing required field: {field}")
            return False
    
    print(f"✅ Workflow structure valid")
    print(f"   - Name: {workflow['name']}")
    print(f"   - Nodes: {len(workflow['nodes'])}")
    print(f"   - Connections: {len(workflow['connections'])}")
    
    return True

def validate_nodes(workflow):
    """Validate individual nodes in the workflow."""
    expected_nodes = [
        "Manual Trigger",
        "Load Configuration",
        "Prepare Video Scan",
        "Scan Folder for Videos",
        "Process File List",
        "Extract Video Thumbnail",
        "Process Thumbnail",
        "AI Video Analysis",
        "Process AI Response",
        "Filter Kung Fu Videos",
        "Collect Final Results",
        "Handle Errors"
    ]
    
    node_names = [node['name'] for node in workflow['nodes']]
    
    for expected in expected_nodes:
        if expected in node_names:
            print(f"✅ Found node: {expected}")
        else:
            print(f"❌ Missing node: {expected}")
            return False
    
    return True

def test_lm_studio_connection():
    """Test connection to LM Studio endpoint."""
    endpoint = "http://localhost:1234/v1/models"
    
    try:
        response = requests.get(endpoint, timeout=5)
        if response.status_code == 200:
            models = response.json()
            print("✅ LM Studio connection successful")
            if 'data' in models:
                model_names = [model.get('id', 'unknown') for model in models['data']]
                print(f"   - Available models: {model_names}")
                
                # Check for mimo model
                mimo_models = [m for m in model_names if 'mimo' in m.lower()]
                if mimo_models:
                    print(f"✅ Found mimo model(s): {mimo_models}")
                else:
                    print("⚠️  No mimo models found - make sure mimo-vl-7b-rl is loaded")
            return True
        else:
            print(f"❌ LM Studio responded with status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to LM Studio - is it running on localhost:1234?")
        return False
    except Exception as e:
        print(f"❌ LM Studio connection error: {e}")
        return False

def test_ai_endpoint():
    """Test the actual AI completion endpoint."""
    endpoint = "http://localhost:1234/v1/chat/completions"
    
    test_payload = {
        "model": "mimo-vl-7b-rl",
        "messages": [
            {
                "role": "user", 
                "content": "Test message - respond with 'OK' if you can see this."
            }
        ],
        "max_tokens": 10,
        "temperature": 0.1
    }
    
    try:
        response = requests.post(endpoint, json=test_payload, timeout=30)
        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                ai_response = result['choices'][0]['message']['content']
                print(f"✅ AI endpoint test successful")
                print(f"   - AI Response: {ai_response.strip()}")
                return True
            else:
                print("❌ AI endpoint returned invalid response format")
                return False
        else:
            print(f"❌ AI endpoint responded with status: {response.status_code}")
            print(f"   - Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ AI endpoint test failed: {e}")
        return False

def test_ffmpeg_availability():
    """Test if FFmpeg is available for video thumbnail extraction."""
    import subprocess

    try:
        result = subprocess.run(['ffmpeg', '-version'],
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ FFmpeg available: {version_line}")
            return True
        else:
            print("❌ FFmpeg command failed")
            return False
    except FileNotFoundError:
        print("❌ FFmpeg not found - required for video thumbnail extraction")
        print("   Install FFmpeg: https://ffmpeg.org/download.html")
        return False
    except subprocess.TimeoutExpired:
        print("❌ FFmpeg command timed out")
        return False
    except Exception as e:
        print(f"❌ FFmpeg test error: {e}")
        return False

def create_test_folder():
    """Create a test folder with sample filenames for testing."""
    test_folder = Path(__file__).parent / "test_videos"
    test_folder.mkdir(exist_ok=True)

    # Create some test files (empty files for testing)
    test_files = [
        "kung_fu_practice.mp4",
        "karate_training.mp4",
        "cooking_video.mp4",
        "dance_performance.mp4",
        "martial_arts_sparring.mp4"
    ]

    for filename in test_files:
        test_file = test_folder / filename
        if not test_file.exists():
            test_file.touch()

    print(f"✅ Created test folder: {test_folder}")
    print(f"   - Test files: {test_files}")

    return str(test_folder)

def main():
    """Run all tests."""
    print("🧪 Testing Kung Fu Video Detector Workflow")
    print("=" * 50)
    
    # Test 1: Load workflow
    workflow = load_workflow()
    if not workflow:
        return
    
    # Test 2: Validate structure
    if not validate_workflow_structure(workflow):
        return
    
    # Test 3: Validate nodes
    if not validate_nodes(workflow):
        return
    
    print("\n🔌 Testing LM Studio Connection")
    print("-" * 30)
    
    # Test 4: LM Studio connection
    lm_studio_ok = test_lm_studio_connection()
    
    # Test 5: AI endpoint (only if LM Studio is running)
    if lm_studio_ok:
        ai_endpoint_ok = test_ai_endpoint()
    else:
        print("⏭️  Skipping AI endpoint test (LM Studio not available)")
        ai_endpoint_ok = False
    
    print("\n📁 Setting up Test Environment")
    print("-" * 30)

    # Test 6: Create test folder
    test_folder = create_test_folder()

    # Test 7: Check FFmpeg availability
    ffmpeg_ok = test_ffmpeg_availability()

    print("\n📋 Test Summary")
    print("=" * 20)
    print(f"✅ Workflow JSON: Valid")
    print(f"✅ Node Structure: Valid")
    print(f"{'✅' if lm_studio_ok else '❌'} LM Studio: {'Connected' if lm_studio_ok else 'Not Available'}")
    print(f"{'✅' if ai_endpoint_ok else '❌'} AI Endpoint: {'Working' if ai_endpoint_ok else 'Not Available'}")
    print(f"{'✅' if ffmpeg_ok else '❌'} FFmpeg: {'Available' if ffmpeg_ok else 'Not Available'}")
    print(f"✅ Test Folder: Created")

    if lm_studio_ok and ai_endpoint_ok and ffmpeg_ok:
        print("\n🎉 All tests passed! The workflow is ready to use.")
        print(f"\n📝 Next Steps:")
        print(f"1. Import the workflow JSON into N8N")
        print(f"2. Configure your video folder path using config.json")
        print(f"3. Test with your actual video folder: {test_folder}")
        print(f"\n🎯 The workflow now analyzes actual video content using:")
        print(f"   - FFmpeg for thumbnail extraction")
        print(f"   - Vision model for visual analysis")
        print(f"   - Base64 encoded images sent to AI")
    else:
        print("\n⚠️  Some tests failed. Please check:")
        if not lm_studio_ok:
            print("   - Start LM Studio on localhost:1234")
        if not ai_endpoint_ok:
            print("   - Load the mimo-vl-7b-rl model in LM Studio")
        if not ffmpeg_ok:
            print("   - Install FFmpeg for video thumbnail extraction")

if __name__ == "__main__":
    main()
