{"name": "VideoProcessor", "nodes": [{"parameters": {}, "id": "336416e9-112b-4779-b9b4-2c1490a27097", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-1664, -336]}, {"parameters": {"jsCode": "// Load configuration from external JSON file\nconst configPath = '/home/<USER>/shared/config/video_processor_config.json';\n\nconsole.log('=== LOADING EXTERNAL CONFIGURATION ===');\nconsole.log('Config file path:', configPath);\n\n// Note: This code node will be followed by an executeCommand node to read the config file\n// This node just prepares the path for the next node\nreturn [{ json: { configPath: configPath, step: 'prepare_config_load' } }];"}, "id": "90616add-a1d6-4ebb-82a2-43a5fe9b32c1", "name": "Load Configuration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1440, -336]}, {"parameters": {"command": "cat \"/home/<USER>/shared/config/video_processor_config.json\""}, "id": "b8f4c2d1-9e3a-4b5c-8d7e-1f2a3b4c5d6e", "name": "Read Config File", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-1328, -336]}, {"parameters": {"jsCode": "// Parse the external configuration JSON\nconst input = $input.first().json;\nconst configJson = input.stdout || '';\nconst exitCode = input.exitCode || 0;\n\nconsole.log('=== PARSING EXTERNAL CONFIGURATION ===');\nconsole.log('Config file read exit code:', exitCode);\nconsole.log('Config JSON length:', configJson.length);\n\nif (exitCode !== 0) {\n  console.log('ERROR: Failed to read config file');\n  console.log('Error:', input.stderr || 'Unknown error');\n  // Return fallback configuration\n  const fallbackConfig = {\n    folders: {\n      source: { videos: '/home/<USER>/shared/kung_fu_videos' },\n      output: { notes: '/home/<USER>/shared/notes', logs: '/home/<USER>/shared/logs' }\n    },\n    processing: {\n      file_extensions: ['.mp4'],\n      recursive_search: true,\n      skip_errors: true\n    },\n    ai_analysis: {\n      lm_studio: {\n        endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n        model: 'mimo-vl-7b-rl@q8_k_xl',\n        timeout: 30000,\n        temperature: 0.4,\n        max_tokens: 150\n      },\n      detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n    }\n  };\n  console.log('Using fallback configuration');\n  return [{ json: { config: fallbackConfig, configSource: 'fallback' } }];\n}\n\ntry {\n  const config = JSON.parse(configJson);\n  console.log('Configuration loaded successfully');\n  console.log('Config version:', config.version);\n  console.log('Environment:', config.environment);\n  console.log('Video folder:', config.folders?.source?.videos);\n  console.log('Notes folder:', config.folders?.output?.notes);\n  console.log('LM Studio endpoint:', config.ai_analysis?.lm_studio?.endpoint);\n  console.log('File extensions:', config.processing?.file_extensions?.length, 'extensions');\n  \n  return [{ json: { config: config, configSource: 'external_file' } }];\n  \n} catch (error) {\n  console.log('ERROR: Failed to parse config JSON:', error.message);\n  // Return fallback configuration on parse error\n  const fallbackConfig = {\n    folders: {\n      source: { videos: '/home/<USER>/shared/kung_fu_videos' },\n      output: { notes: '/home/<USER>/shared/notes', logs: '/home/<USER>/shared/logs' }\n    },\n    processing: {\n      file_extensions: ['.mp4'],\n      recursive_search: true,\n      skip_errors: true\n    },\n    ai_analysis: {\n      lm_studio: {\n        endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n        model: 'mimo-vl-7b-rl@q8_k_xl',\n        timeout: 30000,\n        temperature: 0.4,\n        max_tokens: 150\n      },\n      detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n    }\n  };\n  console.log('Using fallback configuration due to parse error');\n  return [{ json: { config: fallbackConfig, configSource: 'fallback_parse_error' } }];\n}"}, "id": "c9e5d3f2-8a7b-4c6d-9e1f-2a3b4c5d6e7f", "name": "Parse Config File", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1272, -336]}, {"parameters": {"jsCode": "// Log for Load Configuration\nconst input = $input.first().json;\nconst timestamp = new Date().toISOString();\n\nconsole.log('=== STEP 1: LOAD CONFIGURATION LOG ===');\nconsole.log('Timestamp:', timestamp);\nconsole.log('Config source:', input.configSource);\nconsole.log('Input data:', JSON.stringify(input, null, 2));\nconsole.log('Config loaded:', !!input.config);\nif (input.config) {\n  console.log('Video folder:', input.config.folders?.source?.videos);\n  console.log('Notes folder:', input.config.folders?.output?.notes);\n  console.log('LM Studio endpoint:', input.config.ai_analysis?.lm_studio?.endpoint);\n  console.log('Detection keywords:', input.config.ai_analysis?.detection_keywords?.length, 'keywords');\n  console.log('File extensions:', input.config.processing?.file_extensions?.length, 'extensions');\n}\nconsole.log('=== END STEP 1 LOG ===');\n\nreturn [{ json: input }];"}, "id": "403300cf-3575-4a04-a73e-7ef61c4b0199", "name": "Log Load Configuration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1216, -336]}, {"parameters": {"jsCode": "// Get configuration from previous node and build enhanced find command with date extraction\nconst input = $input.first().json;\nconst config = input.config;\n\nconsole.log('=== PREPARING ENHANCED VIDEO SCAN ===');\nconsole.log('Video folder:', config.folders?.source?.videos);\nconsole.log('Recursive search:', config.processing?.recursive_search);\nconsole.log('File extensions:', config.processing?.file_extensions);\nconsole.log('Date extraction enabled:', config.processing?.date_extraction?.fallback_to_modification_date);\n\n// Use the Docker mount path from new config structure\nconst folderPath = config.folders?.source?.videos || '/home/<USER>/shared/kung_fu_videos';\nconsole.log('Using Docker mount path:', folderPath);\n\n// Build enhanced find command for Linux with date extraction\nconst recursiveFlag = config.processing?.recursive_search ? '' : '-maxdepth 1';\nconst fileExtensions = config.processing?.file_extensions || ['.mp4'];\n\n// Create simplified find command for MP4 files only (compatible with all systems)\nconst findCommand = `find \"${folderPath}\" ${recursiveFlag} -type f -iname \"*.mp4\" 2>/dev/null`;\n\nconsole.log('Simplified find command:', findCommand);\nconsole.log('Command will return: filepath format (date extraction will be done in processing)');\n\nreturn [{\n  json: {\n    config: config,\n    findCommand: findCommand,\n    folderPath: folderPath,\n    dateExtractionEnabled: true\n  }\n}];"}, "id": "10ecacbb-c12f-40a3-b07c-4ef5fe485c28", "name": "Prepare Video Scan", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-992, -336]}, {"parameters": {"jsCode": "// Log for Prepare Video Scan\nconst input = $input.first().json;\nconst logData = {\n  step: 'prepare_video_scan',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_prepare_video_scan.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "7e293e4e-17ce-4c8e-b51a-edda6f396fff", "name": "Log Prepare Video Scan", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-768, -336]}, {"parameters": {"command": "find \"/home/<USER>/shared/kung_fu_videos\" -type f -iname \"*.mp4\" 2>/dev/null"}, "id": "a6794564-9735-4765-927a-dab6eba6b97c", "name": "<PERSON>an Folder for Videos", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-544, -336]}, {"parameters": {"jsCode": "// Log for Scan Folder for Videos\nconst input = $input.first().json;\nconst logData = {\n  step: 'scan_folder_for_videos',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_scan_folder_for_videos.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "dd0b0758-173f-41a2-9d6f-efa0b0fb1af2", "name": "Log Scan Folder for Videos", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-320, -336]}, {"parameters": {"jsCode": "// Process the enhanced find command output with date extraction\nconst input = $input.first().json\nconst commandOutput = input.stdout || '';\nconst commandError = input.stderr || '';\nconst exitCode = input.exitCode || 0;\n\n// Get config from previous node (should be passed through)\nconst config = input.config || {\n  folders: { source: { videos: '/home/<USER>/shared/kung_fu_videos' } },\n  processing: {\n    file_extensions: ['.mp4'],\n    recursive_search: true,\n    date_extraction: { filename_pattern: '^20\\\\d{6}', fallback_to_modification_date: true }\n  }\n};\n\nconst folderPath = config.folders?.source?.videos || '/home/<USER>/shared/kung_fu_videos';\nconst fileExtensions = config.processing?.file_extensions || ['.mp4'];\n\nconsole.log('=== PROCESSING ENHANCED VIDEO FILE SCAN WITH DATES ===');\nconsole.log('Folder path:', folderPath);\nconsole.log('File extensions:', fileExtensions);\nconsole.log('Command output length:', commandOutput.length);\nconsole.log('Exit code:', exitCode);\nconsole.log('Date extraction enabled:', config.processing?.date_extraction?.fallback_to_modification_date);\n\n// Check for command errors\nif (exitCode !== 0) {\n  console.log('ERROR: Enhanced find command failed');\n  console.log('Error:', commandError);\n  return [{ json: { \n    error: `Cannot access folder: ${folderPath}. Error: ${commandError}`, \n    folderPath,\n    config\n  }}];\n}\n\nif (!commandOutput || commandOutput.trim() === '') {\n  console.log('No video files found in folder');\n  return [{ json: { \n    error: 'No video files found in the specified folder', \n    folderPath,\n    config,\n    note: `Searched for extensions: ${fileExtensions.join(', ')}`\n  }}];\n}\n\n// Process simple find output and extract dates\nconst fileLines = commandOutput.trim().split('\\n').filter(line => line.trim() !== '');\nconst videoFiles = [];\n\nconsole.log(`Processing ${fileLines.length} file entries...`);\n\nfor (const line of fileLines) {\n  const cleanLine = line.trim();\n  if (cleanLine) {\n    const fullPath = cleanLine;\n    const filename = fullPath.split('/').pop();\n    \n    // Check if file has one of the configured extensions\n    const hasValidExtension = fileExtensions.some(ext => \n      filename.toLowerCase().endsWith(ext.toLowerCase())\n    );\n    \n    if (filename && hasValidExtension) {\n      // Extract date from filename (first 8 chars) - fallback will be handled later if needed\n      const filenameDate = filename.substring(0, 8);\n      const datePattern = new RegExp(config.processing?.date_extraction?.filename_pattern || '^20\\\\d{6}');\n      \n      let extractedDate;\n      let dateSource;\n      \n      if (datePattern.test(filenameDate)) {\n        // Use date from filename\n        extractedDate = filenameDate;\n        dateSource = 'filename';\n      } else {\n        // Use a default date - will be updated by stat command if needed\n        extractedDate = '20120125'; // Default fallback date\n        dateSource = 'fallback';\n      }\n      \n      console.log(`  File: ${filename} -> Date: ${extractedDate} (from ${dateSource})`);\n      \n      videoFiles.push({\n        filename: filename,\n        fullPath: fullPath,\n        folderPath: folderPath,\n        extractedDate: extractedDate,\n        dateSource: dateSource,\n        needsStatLookup: dateSource === 'fallback',\n        config: config\n      });\n    }\n  }\n}\n\nconsole.log(`Found ${videoFiles.length} video files with dates:`);\nvideoFiles.forEach(file => console.log(`  - ${file.filename} (${file.extractedDate})`));\n\nif (videoFiles.length === 0) {\n  return [{ json: { error: 'No valid video files found after processing', folderPath, config } }];\n}\n\n// Return each file as a separate item for processing\nreturn videoFiles.map(file => ({ json: file }));"}, "id": "38a414cd-4b4f-4a2e-b7a9-54685da840da", "name": "Process File List", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-96, -336]}, {"parameters": {"jsCode": "// Log for Process File List - PASS THROUGH ALL ITEMS\nconst allInputs = $input.all();\n\nconsole.log('=== PROCESS FILE LIST LOG ===');\nconsole.log(`Total videos found: ${allInputs.length}`);\nallInputs.forEach((input, index) => {\n  console.log(`Video ${index + 1}: ${input.json.filename}`);\n});\n\n// Create log data for debugging\nconst logData = {\n  step: 'process_file_list',\n  timestamp: new Date().toISOString(),\n  total_videos: allInputs.length,\n  videos: allInputs.map(input => input.json.filename)\n};\n\nconsole.log('Log data:', JSON.stringify(logData, null, 2));\n\n// CRITICAL: Return all items unchanged for SplitInBatches to process\nreturn allInputs;"}, "id": "fa95aad8-b857-4570-8d6b-ac67bfa0aa28", "name": "Log Process File List", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [128, -336]}, {"parameters": {"jsCode": "// Create FFmpeg request for file-based processing\nconst input = $input.first().json;\nconst filename = input.filename;\nconst fullPath = input.fullPath;\nconst config = input.config;\n\nconsole.log('=== FILE-BASED FFMPEG REQUEST ===');\nconsole.log(`Processing video: ${filename}`);\nconsole.log(`Full path: ${fullPath}`);\nconsole.log(`Processing index: ${input.processing_index}/${input.total_videos}`);\n\n// Generate unique request ID\nconst requestId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n\n// Create FFmpeg request data\nconst requestData = {\n  request_id: requestId,\n  filename: filename,\n  full_path: fullPath,\n  created_at: new Date().toISOString(),\n  processing_index: input.processing_index,\n  total_videos: input.total_videos\n};\n\nconsole.log(`Request ID: ${requestId}`);\nconsole.log('Request data:', JSON.stringify(requestData, null, 2));\n\n// Prepare data for file writing (simplified approach)\nconst jsonString = JSON.stringify(requestData, null, 2);\nconst requestFileName = `request_${requestId}.json`;\n\n// Return data for next nodes\nreturn [{\n  json: {\n    filename: filename,\n    fullPath: fullPath,\n    config: config,\n    request_id: requestId,\n    request_file: requestFileName,\n    file_content: jsonString,\n    processing_index: input.processing_index,\n    total_videos: input.total_videos,\n    step: 'create_ffmpeg_request'\n  }\n}];"}, "id": "7a29f0b1-e1c6-4da0-848b-a19fd06e6978", "name": "Create FFmpeg Request", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2288, -48]}, {"parameters": {"command": "=mkdir -p \"/home/<USER>/shared/ffmpeg_requests\" && if [ -n \"{{ $json.request_file }}\" ] && [ -n \"{{ $json.file_content }}\" ]; then echo '{{ $json.file_content }}' > \"/home/<USER>/shared/ffmpeg_requests/{{ $json.request_file }}\"; else echo 'ERROR: Missing request_file or file_content' > \"/home/<USER>/shared/ffmpeg_requests/error_$(date +%s).json\"; fi"}, "id": "8d439999-4929-44d7-9dab-825e5017d134", "name": "Save FFmpeg Request", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-2064, -48]}, {"parameters": {"command": "=sleep 15 && if [ -f \"/home/<USER>/shared/ffmpeg_results/result_{{ $json.request_id }}.json\" ]; then cat \"/home/<USER>/shared/ffmpeg_results/result_{{ $json.request_id }}.json\"; else echo '{\"success\": false, \"error\": \"FFmpeg result not found after 15 seconds\", \"request_id\": \"{{ $json.request_id }}\", \"filename\": \"{{ $json.filename }}\"}'; fi"}, "id": "99fabeeb-0b20-4a0b-9b1b-67be02d1c30e", "name": "Read FFmpeg Result", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-1872, -48]}, {"parameters": {"jsCode": "// Process file-based FFmpeg result\nconst input = $input.first().json;\nconst rawResultData = input.stdout || '';\nconst exitCode = input.exitCode || 0;\n\nconsole.log('=== PROCESSING FILE-BASED FFMPEG RESULT ===');\nconsole.log('Input keys:', Object.keys(input));\nconsole.log('Raw result length:', rawResultData.length);\nconsole.log('Exit code:', exitCode);\n\n// Parse the FFmpeg result JSON\nlet ffmpegResult;\ntry {\n  ffmpegResult = JSON.parse(rawResultData);\n  console.log('Parsed FFmpeg result:', ffmpegResult.success ? 'SUCCESS' : 'FAILED');\n} catch (e) {\n  console.log('Failed to parse FFmpeg result, treating as error');\n  ffmpegResult = {\n    success: false,\n    error: 'Failed to parse FFmpeg processor result',\n    filename: input.filename || 'unknown.mp4'\n  };\n}\n\n// Get video information\nconst filename = ffmpegResult.filename || input.filename || 'unknown.mp4';\nconst fullPath = input.fullPath || '/home/<USER>/shared/kung_fu_videos/unknown.mp4';\n\nconsole.log(`Processing result for: ${filename}`);\nconsole.log(`FFmpeg success: ${ffmpegResult.success}`);\n\n// Recreate config since executeCommand doesn't pass it through\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',\n  lm_studio: {\n    endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n    model: 'mimo-vl-7b-rl@q8_k_xl',\n    timeout: 30000,\n    temperature: 0.1,\n    max_tokens: 50\n  },\n  ai_prompt: 'Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training. Look for martial arts poses, fighting stances, training equipment, or combat movements. Please respond with only YES if it shows kung fu/martial arts practice, or NO if it does not.',\n  detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n};\n\nconsole.log('=== PROCESSING FILE-BASED FFMPEG RESULT ===');\nconsole.log(`File: ${filename}`);\nconsole.log(`FFmpeg result success: ${ffmpegResult.success}`);\nif (ffmpegResult.thumbnail_base64) {\n  console.log(`Thumbnail data length: ${ffmpegResult.thumbnail_base64.length}`);\n}\n\n// Check if file-based FFmpeg processing was successful\nif (!ffmpegResult.success) {\n  console.log('ERROR: File-based FFmpeg extraction FAILED');\n  \n  const errorDetails = {\n    timestamp: new Date().toISOString(),\n    operation: 'file_based_ffmpeg_extraction',\n    filename: filename,\n    fullPath: fullPath,\n    error: ffmpegResult.error || 'Unknown FFmpeg error',\n    error_type: 'ffmpeg_extraction_failed',\n    error_step: 'thumbnail_extraction',\n    request_id: ffmpegResult.request_id,\n    extraction_method: 'file_based_ffmpeg',\n    exit_code: exitCode,\n    raw_output_length: rawResultData.length,\n    skipped: true,\n    skip_reason: 'FFmpeg thumbnail extraction failed'\n  };\n  \n  console.log('Error details:', JSON.stringify(errorDetails, null, 2));\n  \n  return [{\n    json: {\n      filename: filename,\n      fullPath: fullPath,\n      config: config,\n      error: errorDetails,\n      success: false,\n      thumbnailBase64: null,\n      hasValidThumbnail: false\n    }\n  }];\n}\n\n// Extract thumbnail data from FFmpeg result\nconst thumbnailBase64 = ffmpegResult.thumbnail_base64;\n\nif (!thumbnailBase64) {\n  console.log('ERROR: No thumbnail data in FFmpeg result');\n  return [{\n    json: {\n      filename: filename,\n      fullPath: fullPath,\n      config: config,\n      error: { error: 'No thumbnail data in FFmpeg result' },\n      success: false,\n      thumbnailBase64: null,\n      hasValidThumbnail: false\n    }\n  }];\n}\n\nconsole.log('SUCCESS: File-based thumbnail extracted successfully!');\nconsole.log(`Thumbnail size: ${ffmpegResult.thumbnail_size_kb}KB`);\nconsole.log(`Extraction method: ${ffmpegResult.extraction_method}`);\n\nreturn [{\n  json: {\n    filename: filename,\n    fullPath: fullPath,\n    config: config,\n    thumbnailBase64: thumbnailBase64,\n    hasValidThumbnail: true,\n    success: true,\n    thumbnailSizeKB: ffmpegResult.thumbnail_size_kb,\n    extractionMethod: ffmpegResult.extraction_method,\n    requestId: ffmpegResult.request_id,\n    processedAt: ffmpegResult.processed_at\n  }\n}];"}, "id": "8638e6ae-d69c-45e7-83ca-cd2d9632a597", "name": "Process FFmpeg Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1648, -48]}, {"parameters": {"jsCode": "// Log for Process Thumbnail\nconst input = $input.first().json;\nconst timestamp = new Date().toISOString();\n\nconsole.log('=== STEP 6: PROCESS THUMBNAIL LOG ===');\nconsole.log('Timestamp:', timestamp);\nconsole.log('Filename:', input.filename);\nconsole.log('Success:', input.success);\nconsole.log('Has thumbnail:', !!input.thumbnailBase64);\nconsole.log('Thumbnail size KB:', input.thumbnailSizeKB);\nconsole.log('Error:', input.error || 'none');\nconsole.log('=== END STEP 6 LOG ===');\n\nreturn [{ json: input }];"}, "id": "dba7ec72-40f4-49b0-bea4-77ec12ccbcda", "name": "Log Process Thumbnail", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1424, -48]}, {"parameters": {"jsCode": "// Write Vision Request to Shared Folder (Simplified Method)\nconst input = $input.first().json;\nconst thumbnailBase64 = input.thumbnailBase64;\nconst filename = input.filename;\n\n// Skip if no thumbnail data\nif (!thumbnailBase64) {\n  console.log('Skipping vision analysis - no thumbnail data');\n  return [{\n    json: {\n      filename: filename,\n      success: false,\n      analysis_result: 'ERROR',\n      contains_kung_fu: false,\n      error: 'No thumbnail data available'\n    }\n  }];\n}\n\n// Generate unique request ID\nconst requestId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n\n// Prepare request data\nconst requestData = {\n  request_id: requestId,\n  filename: filename,\n  thumbnailBase64: thumbnailBase64,\n  prompt: \"Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training. Look for martial arts poses, fighting stances, training equipment, or combat movements. Please respond with only YES if it shows kung fu/martial arts practice, or NO if it does not.\",\n  created_at: new Date().toISOString()\n};\n\nconsole.log('=== WRITING VISION REQUEST (SIMPLIFIED) ===');\nconsole.log('Request ID:', requestId);\nconsole.log('Filename:', filename);\nconsole.log('Thumbnail length:', thumbnailBase64.length);\n\n// Prepare data for file writing (simplified approach)\nconst jsonString = JSON.stringify(requestData, null, 2);\nconst requestFileName = `request_${requestId}.json`;\n\n// Return data with simple structure\nconsole.log('DEBUG: About to return data');\nconsole.log('Request file:', requestFileName);\nconsole.log('File content length:', jsonString.length);\nconsole.log('First 100 chars of content:', jsonString.substring(0, 100));\n\nreturn [{\n  json: {\n    request_id: requestId,\n    filename: filename,\n    request_file: requestFileName,\n    data_size: jsonString.length,\n    file_content: jsonString,\n    original_data: input,\n    debug_info: {\n      request_id: requestId,\n      request_file: requestFileName,\n      content_preview: jsonString.substring(0, 50)\n    }\n  }\n}];"}, "id": "a9bf8885-a9b2-4ae7-8087-dcfec980f0cf", "name": "Write Vision Request (File-Based)", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1200, -48]}, {"parameters": {"jsCode": "// Log Vision Request Data Flow\nconst input = $input.first().json;\nconst timestamp = new Date().toISOString();\n\nconsole.log('=== VISION REQUEST DATA FLOW DEBUG ===');\nconsole.log('Timestamp:', timestamp);\nconsole.log('Input keys:', Object.keys(input));\nconsole.log('Request ID:', input.request_id || 'MISSING');\nconsole.log('Filename:', input.filename || 'MISSING');\nconsole.log('Request file:', input.request_file || 'MISSING');\nconsole.log('File content length:', input.file_content ? input.file_content.length : 'MISSING');\nconsole.log('File content preview:', input.file_content ? input.file_content.substring(0, 100) + '...' : 'NO CONTENT');\nconsole.log('Debug info:', input.debug_info || 'NO DEBUG INFO');\nconsole.log('Original data keys:', input.original_data ? Object.keys(input.original_data) : 'NO ORIGINAL DATA');\n\nif (input.original_data && input.original_data.thumbnailBase64) {\n  console.log('Thumbnail base64 length:', input.original_data.thumbnailBase64.length);\n  console.log('Thumbnail starts with:', input.original_data.thumbnailBase64.substring(0, 50));\n} else {\n  console.log('ERROR: No thumbnailBase64 in original_data!');\n}\n\n// Create detailed log data\nconst logData = {\n  step: 'vision_request_data_flow_debug',\n  timestamp: timestamp,\n  input_analysis: {\n    has_request_id: !!input.request_id,\n    has_filename: !!input.filename,\n    has_request_file: !!input.request_file,\n    has_file_content: !!input.file_content,\n    file_content_length: input.file_content ? input.file_content.length : 0,\n    has_debug_info: !!input.debug_info,\n    has_original_data: !!input.original_data\n  },\n  thumbnail_analysis: {\n    has_thumbnail: !!(input.original_data && input.original_data.thumbnailBase64),\n    thumbnail_length: input.original_data && input.original_data.thumbnailBase64 ? input.original_data.thumbnailBase64.length : 0,\n    thumbnail_preview: input.original_data && input.original_data.thumbnailBase64 ? input.original_data.thumbnailBase64.substring(0, 50) : 'NO THUMBNAIL'\n  },\n  full_input_data: input\n};\n\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: `vision_request_debug_${timestamp.replace(/[:.]/g, '-')}.json`,\n  fileExtension: 'json'\n};\n\n// Pass through the original data unchanged\nreturn [{\n  json: input,\n  binary: {\n    data: binaryData\n  }\n}];"}, "id": "debug-vision-request-12345", "name": "Log Vision Request Data Flow", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-988, -48]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/logs/' + 'vision_request_debug_' + new Date().toISOString().replace(/[:.]/g, '-') + '.json' }}", "dataPropertyName": "data", "options": {"overwrite": true}}, "id": "write-vision-debug-67890", "name": "Write Vision Debug Log", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [-800, -48]}, {"parameters": {"command": "=mkdir -p \"/home/<USER>/shared/vision_requests\" && if [ -n \"{{ $json.request_file }}\" ] && [ -n \"{{ $json.file_content }}\" ]; then echo '{{ $json.file_content }}' > \"/home/<USER>/shared/vision_requests/{{ $json.request_file }}\"; else echo 'ERROR: Missing request_file or file_content' > \"/home/<USER>/shared/vision_requests/error_$(date +%s).json\"; fi"}, "id": "5dc8021c-3826-435f-a5f8-8f5456c8ca2c", "name": "Save Vision Request (File-Based)", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-976, -48]}, {"parameters": {"command": "=sleep 10 && if [ -f \"/home/<USER>/shared/vision_results/result_{{ $json.request_id }}.json\" ]; then cat \"/home/<USER>/shared/vision_results/result_{{ $json.request_id }}.json\"; else echo '{\"success\": false, \"error\": \"Result not found after 10 seconds\", \"request_id\": \"{{ $json.request_id }}\", \"filename\": \"{{ $json.filename }}\"}'; fi"}, "id": "70e84aee-f549-443a-a55a-8ba584bd3168", "name": "Read Vision Result (File-Based)", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-752, -48]}, {"parameters": {"jsCode": "// Parse Vision Result (File-Based Method)\nconst input = $input.first().json;\nconst stdout = input.stdout;\nconst originalData = input.original_data || {};\n\nconsole.log('=== PARSING VISION RESULT (FILE-BASED) ===');\nconsole.log('Raw stdout:', stdout);\n\ntry {\n  // Parse the JSON result\n  const result = JSON.parse(stdout);\n  \n  console.log('Parsed result:', result);\n  console.log('Success:', result.success);\n  console.log('Analysis result:', result.analysis_result);\n  \n  // Return in the same format as the original HTTP Request node would\n  // This ensures compatibility with existing logging nodes\n  const finalResult = {\n    choices: [{\n      message: {\n        content: result.full_response || `Analysis: ${result.analysis_result}`\n      }\n    }],\n    success: result.success || false,\n    filename: result.filename || originalData.filename || 'unknown',\n    analysis_result: result.analysis_result || 'NO',\n    contains_kung_fu: result.contains_kung_fu || false,\n    full_response: result.full_response || '',\n    model_used: result.model_used || 'unknown',\n    processed_at: result.processed_at || new Date().toISOString(),\n    request_id: result.request_id || 'unknown',\n    error: result.error || null,\n    // Pass through thumbnail data for description analysis\n    thumbnailBase64: originalData.thumbnailBase64 || ''\n  };\n  \n  console.log('=== FILE-BASED VISION ANALYSIS COMPLETE ===');\n  console.log('File:', finalResult.filename);\n  console.log('Contains Kung Fu:', finalResult.contains_kung_fu);\n  console.log('Analysis:', finalResult.analysis_result);\n  \n  return [{ json: finalResult }];\n  \n} catch (error) {\n  console.log('Error parsing vision result:', error.message);\n  \n  // Enhanced error details for skipped files tracking\n  const errorDetails = {\n    timestamp: new Date().toISOString(),\n    operation: 'vision_analysis',\n    filename: originalData.filename || 'unknown',\n    error: `Failed to parse vision result: ${error.message}`,\n    error_type: 'vision_parse_failed',\n    error_step: 'vision_result_parsing',\n    raw_stdout_length: stdout ? stdout.length : 0,\n    skipped: true,\n    skip_reason: 'Vision analysis result parsing failed'\n  };\n  \n  // Return error result in expected format\n  return [{\n    json: {\n      choices: [{ message: { content: `Error: ${error.message}` } }],\n      success: false,\n      filename: originalData.filename || 'unknown',\n      analysis_result: 'ERROR',\n      contains_kung_fu: false,\n      full_response: '',\n      error: errorDetails,\n      raw_stdout: stdout,\n      processed_at: new Date().toISOString()\n    }\n  }];\n}"}, "id": "b611527c-0961-4638-8ac1-ca78e94e3d3c", "name": "Parse Vision Result (File-Based)", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-528, -48]}, {"parameters": {"jsCode": "// Log for AI Video Analysis\nconst input = $input.first().json;\nconst timestamp = new Date().toISOString();\n\nconsole.log('=== STEP 7: AI VIDEO ANALYSIS LOG ===');\nconsole.log('Timestamp:', timestamp);\nconsole.log('Has choices:', !!input.choices);\nconsole.log('AI Response:', input.choices?.[0]?.message?.content || 'No response');\nconsole.log('Response length:', (input.choices?.[0]?.message?.content || '').length);\nconsole.log('=== END STEP 7 LOG ===');\n\nreturn [{ json: input }];"}, "id": "50dd424e-30d2-46c1-b6e0-0feab7ed2932", "name": "Log AI Video Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-304, -48]}, {"parameters": {"jsCode": "// Create Description Request for File-Based Processing\nconst input = $input.first().json;\nconst filename = input.filename || 'unknown.mp4';\nconst isKungFu = input.contains_kung_fu || false;\nconst thumbnailBase64 = input.thumbnailBase64 || '';\n\nconsole.log('=== CREATING DESCRIPTION REQUEST ===');\nconsole.log('Filename:', filename);\nconsole.log('Is Kung Fu:', isKungFu);\nconsole.log('Has thumbnail:', !!thumbnailBase64);\nconsole.log('Thumbnail length:', thumbnailBase64.length);\n\n// Generate unique request ID\nconst requestId = Date.now() + '_desc_' + Math.random().toString(36).substr(2, 9);\n\n// Create description request data\nconst requestData = {\n  request_id: requestId,\n  filename: filename,\n  isKungFu: isKungFu,\n  thumbnailBase64: thumbnailBase64,\n  needs_thumbnail: !thumbnailBase64,  // Signal if processor needs to get thumbnail from FFmpeg results\n  created_at: new Date().toISOString()\n};\n\nconsole.log('Description request ID:', requestId);\nconsole.log('Request data prepared for file-based processing');\nif (!thumbnailBase64) {\n  console.log('Note: External processor will need to get thumbnail from FFmpeg results');\n}\n\n// Prepare data for file writing (simplified approach)\nconst jsonString = JSON.stringify(requestData, null, 2);\nconst requestFileName = `request_${requestId}.json`;\n\nreturn [{\n  json: {\n    request_id: requestId,\n    filename: filename,\n    request_file: requestFileName,\n    file_content: jsonString,\n    isKungFu: isKungFu,\n    original_data: input\n  }\n}];"}, "id": "d8e7f3a1-2b4c-5d6e-7f8g-9h0i1j2k3l4m", "name": "Create Description Request", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-200, -48]}, {"parameters": {"command": "=mkdir -p \"/home/<USER>/shared/description_requests\" && if [ -n \"{{ $json.request_file }}\" ] && [ -n \"{{ $json.file_content }}\" ]; then echo '{{ $json.file_content }}' > \"/home/<USER>/shared/description_requests/{{ $json.request_file }}\"; else echo 'ERROR: Missing request_file or file_content' > \"/home/<USER>/shared/description_requests/error_$(date +%s).json\"; fi"}, "id": "e9f0a1b2-3c4d-5e6f-7g8h-9i0j1k2l3m4n", "name": "Save Description Request", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [24, -48]}, {"parameters": {"command": "=sleep 10 && if [ -f \"/home/<USER>/shared/description_results/result_{{ $json.request_id }}.json\" ]; then cat \"/home/<USER>/shared/description_results/result_{{ $json.request_id }}.json\"; else echo '{\"success\": false, \"error\": \"Description result not found after 10 seconds\", \"request_id\": \"{{ $json.request_id }}\", \"filename\": \"{{ $json.filename }}\"}'; fi"}, "id": "f1a2b3c4-5d6e-7f8g-9h0i-1j2k3l4m5n6o", "name": "Read Description Result", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [248, -48]}, {"parameters": {"jsCode": "// Parse Description Result (File-Based Method)\nconst input = $input.first().json;\nconst stdout = input.stdout;\nconst originalData = input.original_data || {};\n\nconsole.log('=== PARSING DESCRIPTION RESULT (FILE-BASED) ===');\nconsole.log('Raw stdout:', stdout);\n\ntry {\n  // Parse the JSON result\n  const result = JSON.parse(stdout);\n  \n  console.log('Parsed description result:', result);\n  console.log('Success:', result.success);\n  console.log('Description:', result.description);\n  \n  // Combine with original vision analysis data\n  const finalResult = {\n    // Keep original vision analysis data\n    choices: originalData.choices || [{ message: { content: result.description || 'No description' } }],\n    success: result.success || false,\n    filename: result.filename || originalData.filename || 'unknown',\n    analysis_result: originalData.analysis_result || 'NO',\n    contains_kung_fu: result.is_kung_fu || originalData.contains_kung_fu || false,\n    full_response: originalData.full_response || '',\n    \n    // Add new description data\n    description: result.description || 'No description available',\n    description_success: result.success || false,\n    description_prompt_type: result.prompt_type || 'unknown',\n    \n    // Metadata\n    model_used: result.model_used || 'unknown',\n    processed_at: result.processed_at || new Date().toISOString(),\n    request_id: result.request_id || 'unknown',\n    error: result.error || null\n  };\n  \n  console.log('=== FILE-BASED DESCRIPTION ANALYSIS COMPLETE ===');\n  console.log('File:', finalResult.filename);\n  console.log('Contains Kung Fu:', finalResult.contains_kung_fu);\n  console.log('Description:', finalResult.description);\n  \n  return [{ json: finalResult }];\n  \n} catch (error) {\n  console.log('Error parsing description result:', error.message);\n  \n  // Enhanced error details for skipped files tracking\n  const errorDetails = {\n    timestamp: new Date().toISOString(),\n    operation: 'description_analysis',\n    filename: originalData.filename || 'unknown',\n    error: `Failed to parse description result: ${error.message}`,\n    error_type: 'description_parse_failed',\n    error_step: 'description_result_parsing',\n    raw_stdout_length: stdout ? stdout.length : 0,\n    skipped: true,\n    skip_reason: 'Description analysis result parsing failed'\n  };\n  \n  // Return error result with original data preserved\n  return [{\n    json: {\n      // Preserve original vision analysis\n      choices: originalData.choices || [{ message: { content: 'Description error' } }],\n      success: false,\n      filename: originalData.filename || 'unknown',\n      analysis_result: originalData.analysis_result || 'ERROR',\n      contains_kung_fu: originalData.contains_kung_fu || false,\n      full_response: originalData.full_response || '',\n      \n      // Add error description data\n      description: `Error: ${error.message}`,\n      description_success: false,\n      error: errorDetails,\n      raw_stdout: stdout,\n      processed_at: new Date().toISOString()\n    }\n  }];\n}"}, "id": "g2h3i4j5-6k7l-8m9n-0o1p-2q3r4s5t6u7v", "name": "Parse Description Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [472, -48]}, {"parameters": {"jsCode": "// Extract the AI response and determine if it's kung fu with description\nconst input = $input.first().json;\nconst aiResponse = input.choices?.[0]?.message?.content || '';\nconst description = input.description || 'No description available';\nconst filename = input.filename || 'unknown.mp4';\nconst isKungFu = input.contains_kung_fu || false;\n\n// Get config from external configuration (should be available from earlier nodes)\nconst config = {\n  detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n};\n\nconsole.log('=== PROCESSING AI RESPONSE WITH DESCRIPTION ===');\nconsole.log(`File: ${filename}`);\nconsole.log(`AI Response: ${aiResponse}`);\nconsole.log(`Description: ${description}`);\nconsole.log(`Is Kung Fu: ${isKungFu}`);\nconsole.log(`Detection keywords: ${config.detection_keywords.length} keywords`);\n\n// Check if AI response indicates kung fu/martial arts using configured keywords\nconst responseText = aiResponse.toLowerCase();\nconst detectedKungFu = config.detection_keywords.some(keyword => \n  responseText.includes(keyword.toLowerCase())\n);\n\nconsole.log(`Detected Kung Fu from response: ${detectedKungFu}`);\nif (detectedKungFu) {\n  const matchedKeywords = config.detection_keywords.filter(keyword => \n    responseText.includes(keyword.toLowerCase())\n  );\n  console.log(`Matched keywords: ${matchedKeywords.join(', ')}`);\n} else {\n  console.log('No kung fu keywords found in AI response');\n}\n\n// Use the more reliable detection result (from vision analysis)\nconst finalKungFu = isKungFu || detectedKungFu;\n\n// Also extract date information that should be available from earlier processing\nconst extractedDate = input.originalData?.extractedDate || input.extractedDate || 'unknown';\nconst dateSource = input.originalData?.dateSource || input.dateSource || 'unknown';\n\nconst processedResult = {\n  filename: filename,\n  fullPath: input.fullPath || `/home/<USER>/shared/kung_fu_videos/${filename}`,\n  aiResponse: aiResponse,\n  description: description,\n  isKungFu: finalKungFu,\n  detectedFromResponse: detectedKungFu,\n  detectedFromVision: isKungFu,\n  extractedDate: extractedDate,\n  dateSource: dateSource,\n  config: config,\n  timestamp: new Date().toISOString(),\n  // Pass through all original data for downstream processing\n  originalData: input\n};\n\nconsole.log(`Final processing result for ${filename}:`);\nconsole.log(`  Date: ${extractedDate} (${dateSource})`);\nconsole.log(`  Kung Fu: ${finalKungFu}`);\nconsole.log(`  Description: ${description}`);\n\nreturn [{ json: processedResult }];"}, "id": "7159035c-8930-4565-80b7-bb2653a16e4f", "name": "Process AI Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [696, -48]}, {"parameters": {"jsCode": "// Log for Process AI Response\nconst input = $input.first().json;\nconst timestamp = new Date().toISOString();\n\nconsole.log('=== STEP 8: PROCESS AI RESPONSE LOG ===');\nconsole.log('Timestamp:', timestamp);\nconsole.log('Filename:', input.filename);\nconsole.log('AI Response:', input.aiResponse);\nconsole.log('Is Kung Fu:', input.isKungFu);\nconsole.log('Keywords checked:', input.config?.detection_keywords?.length || 0);\nconsole.log('=== END STEP 8 LOG ===');\n\nreturn [{ json: input }];"}, "id": "69bcfd2a-215e-40ff-8874-f77ce0881a1e", "name": "Log Process AI Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [144, -48]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.isKungFu }}", "value2": true}]}}, "id": "4cb3b924-1fe1-49b8-ac3d-2c21d843ecc6", "name": "Filter Kung Fu Videos", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1888, 256]}, {"parameters": {"jsCode": "// Log for Filter Kung Fu Videos\nconst input = $input.first().json;\nconst logData = {\n  step: 'filter_kung_fu_videos',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_filter_kung_fu_videos.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "83e3c8f4-750e-4ae5-a49f-f29655239ec4", "name": "Log Filter Kung Fu Videos", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1664, 256]}, {"parameters": {"jsCode": "// Collect all kung fu video filenames into a final array\nconst allItems = $input.all();\nconst kungFuVideos = allItems\n  .filter(item => item.json.isKungFu)\n  .map(item => item.json.filename);\n\nconsole.log('=== COLLECTING FINAL RESULTS ===');\nconsole.log(`Total items processed: ${allItems.length}`);\nconsole.log(`Kung Fu videos found: ${kungFuVideos.length}`);\nkungFuVideos.forEach(video => console.log(`  SUCCESS: ${video}`));\n\nreturn [{\n  json: {\n    kungFuVideos: kungFuVideos,\n    totalFound: kungFuVideos.length,\n    timestamp: new Date().toISOString(),\n    summary: `Found ${kungFuVideos.length} kung fu practice videos`\n  }\n}];"}, "id": "f434ea18-2a8c-4771-b47c-47f3f78d3e88", "name": "Collect Final Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1440, 256]}, {"parameters": {"jsCode": "// Log for Collect Final Results\nconst input = $input.first().json;\nconst logData = {\n  step: 'collect_final_results',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_collect_final_results.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "5b387e66-8de1-4736-aafb-acdfc7757ae4", "name": "Log Collect Final Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1216, 256]}, {"parameters": {"jsCode": "// Handle errors and failed analyses\nconst allItems = $input.all();\nconst errors = allItems\n  .filter(item => item.json.error)\n  .map(item => ({\n    filename: item.json.filename || 'unknown',\n    error: item.json.error\n  }));\n\nconsole.log('=== HANDLING ERRORS ===');\nconsole.log(`Total errors: ${errors.length}`);\nerrors.forEach(error => console.log(`  ERROR: ${error.filename}: ${error.error}`));\n\nreturn [{\n  json: {\n    errors: errors,\n    errorCount: errors.length,\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "5fa3f291-f14f-4a4c-88c9-9773d2e07cda", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-992, 256]}, {"parameters": {"jsCode": "// Log for Handle Errors\nconst input = $input.first().json;\nconst logData = {\n  step: 'handle_errors',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_handle_errors.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "e620c592-ea8f-43f8-b58c-d2f5efd96888", "name": "Log Handle Errors", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-768, 256]}, {"parameters": {"jsCode": "// Create comprehensive execution log for debugging\nconst allInputs = $input.all();\nconst timestamp = new Date().toISOString();\n\nconst executionLog = {\n  timestamp: timestamp,\n  workflow_name: 'Kung Fu Video Detector',\n  execution_summary: {\n    total_inputs: allInputs.length,\n    execution_time: timestamp,\n    status: 'completed'\n  },\n  node_outputs: [],\n  debug_info: {\n    docker_mount: '/home/<USER>/shared/kung_fu_videos',\n    lm_studio_endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n    expected_files: 5\n  }\n};\n\n// Process each input and log details\nallInputs.forEach((input, index) => {\n  const data = input.json;\n  \n  executionLog.node_outputs.push({\n    input_index: index,\n    filename: data.filename || 'unknown',\n    fullPath: data.fullPath || 'unknown',\n    extractedDate: data.extractedDate || 'unknown',\n    dateSource: data.dateSource || 'unknown',\n    description: data.description || 'no description',\n    hasConfig: !!data.config,\n    hasThumbnail: !!data.thumbnailBase64,\n    isKungFu: data.isKungFu || false,\n    aiResponse: data.aiResponse || 'no response',\n    error: data.error || null,\n    timestamp: data.timestamp || 'unknown'\n  });\n});\n\n// Collect detailed error information for skipped files\nconst skippedFiles = [];\nconst successfulFiles = [];\n\nallInputs.forEach((input, index) => {\n  const data = input.json;\n  \n  if (data.error && typeof data.error === 'object') {\n    // Enhanced error object with detailed information\n    skippedFiles.push({\n      filename: data.filename || 'unknown',\n      error_details: data.error,\n      input_index: index,\n      timestamp: data.error.timestamp || new Date().toISOString()\n    });\n  } else if (data.error) {\n    // Simple error string\n    skippedFiles.push({\n      filename: data.filename || 'unknown',\n      error_details: {\n        error: data.error,\n        error_type: 'unknown_error',\n        error_step: 'unknown_step',\n        timestamp: new Date().toISOString(),\n        skipped: true,\n        skip_reason: 'Processing error occurred'\n      },\n      input_index: index,\n      timestamp: new Date().toISOString()\n    });\n  } else {\n    // Successful processing\n    successfulFiles.push({\n      filename: data.filename || 'unknown',\n      extractedDate: data.extractedDate || 'unknown',\n      description: data.description || 'no description',\n      isKungFu: data.isKungFu || false,\n      input_index: index\n    });\n  }\n});\n\n// Add comprehensive summary statistics\nexecutionLog.summary = {\n  total_videos_processed: allInputs.length,\n  successful_videos: successfulFiles.length,\n  skipped_videos: skippedFiles.length,\n  kung_fu_videos_found: allInputs.filter(input => input.json.isKungFu).length,\n  errors_encountered: skippedFiles.length,\n  successful_ai_calls: allInputs.filter(input => input.json.aiResponse && !input.json.error).length,\n  processing_success_rate: allInputs.length > 0 ? ((successfulFiles.length / allInputs.length) * 100).toFixed(1) + '%' : '0%'\n};\n\n// Add detailed skipped files section\nexecutionLog.skipped_files = {\n  count: skippedFiles.length,\n  files: skippedFiles,\n  error_types: {\n    ffmpeg_extraction_failed: skippedFiles.filter(f => f.error_details.error_type === 'ffmpeg_extraction_failed').length,\n    vision_parse_failed: skippedFiles.filter(f => f.error_details.error_type === 'vision_parse_failed').length,\n    description_parse_failed: skippedFiles.filter(f => f.error_details.error_type === 'description_parse_failed').length,\n    other_errors: skippedFiles.filter(f => !['ffmpeg_extraction_failed', 'vision_parse_failed', 'description_parse_failed'].includes(f.error_details.error_type)).length\n  }\n};\n\n// Add successful files section for notes generation\nexecutionLog.successful_files = {\n  count: successfulFiles.length,\n  files: successfulFiles\n};\n\nconsole.log('=== ENHANCED EXECUTION LOG SUMMARY ===');\nconsole.log(`Total videos processed: ${executionLog.summary.total_videos_processed}`);\nconsole.log(`Successful videos: ${executionLog.summary.successful_videos}`);\nconsole.log(`Skipped videos: ${executionLog.summary.skipped_videos}`);\nconsole.log(`Processing success rate: ${executionLog.summary.processing_success_rate}`);\nconsole.log(`Kung fu videos found: ${executionLog.summary.kung_fu_videos_found}`);\nconsole.log(`Successful AI calls: ${executionLog.summary.successful_ai_calls}`);\n\nif (executionLog.skipped_files.count > 0) {\n  console.log('\\n=== SKIPPED FILES DETAILS ===');\n  console.log(`FFmpeg extraction failed: ${executionLog.skipped_files.error_types.ffmpeg_extraction_failed}`);\n  console.log(`Vision parsing failed: ${executionLog.skipped_files.error_types.vision_parse_failed}`);\n  console.log(`Description parsing failed: ${executionLog.skipped_files.error_types.description_parse_failed}`);\n  console.log(`Other errors: ${executionLog.skipped_files.error_types.other_errors}`);\n  \n  console.log('\\nSkipped files:');\n  executionLog.skipped_files.files.forEach(file => {\n    console.log(`  - ${file.filename}: ${file.error_details.skip_reason}`);\n  });\n} else {\n  console.log('\\n✅ No files were skipped - all processing successful!');\n}\n\n// Create log content for file (writeBinaryFile expects 'data' property)\nconst logContent = JSON.stringify(executionLog, null, 2);\nconst filename = `kung_fu_detector_log_${timestamp.replace(/[:.]/g, '-')}.json`;\n\n// Prepare data for writeBinaryFile (correct pattern)\nreturn [{\n  json: {\n    filename: filename,\n    summary: executionLog.summary,\n    timestamp: timestamp\n  },\n  binary: {\n    data: {\n      data: Buffer.from(logContent, 'utf8').toString('base64'),\n      mimeType: 'application/json',\n      fileName: filename,\n      fileExtension: 'json'\n    }\n  }\n}];"}, "id": "6312e3a3-bffc-450a-9300-a3bcbae0d876", "name": "Create Execution Log", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-544, 256]}, {"parameters": {"jsCode": "// Log for Create Execution Log\nconst input = $input.first().json;\nconst logData = {\n  step: 'create_execution_log',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_create_execution_log.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "f2edcf5f-e61f-4721-8d7c-e3001af9b0f9", "name": "Log Create Execution Log", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-320, 256]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/' + $json.filename }}", "dataPropertyName": "data", "options": {}}, "id": "f9d685c8-5d83-426a-9343-50a928084d25", "name": "Write Log to Shared Folder", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [-96, 256]}, {"parameters": {"jsCode": "// Log for Write Log to Shared Folder\nconst input = $input.first().json;\nconst logData = {\n  step: 'write_log_to_shared_folder',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_write_log_to_shared_folder.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "f4c598d1-f68d-4e93-83e0-ec1139d2e5e4", "name": "Log Write Log to Shared Folder", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [432, 256]}, {"parameters": {"batchSize": 1, "options": {"reset": false}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [368, -336], "id": "4b99ffe7-74b6-4f7f-8349-f3c89f544b5d", "name": "Loop Over Items"}, {"parameters": {"jsCode": "// Group Videos by Date - Collect Results from External Processors\n// This node runs after all individual video processing is complete\n// It reads the actual results from the external processors and groups them by date\n\nconsole.log('=== GROUPING VIDEOS BY DATE FROM PROCESSOR RESULTS ===');\n\n// Since SplitInBatches Done output doesn't contain processed data,\n// we need to read the actual results from the external processors\n\n// The external processors have already processed all videos and saved results\n// We need to read those results and group them by date\n\nconst processingResults = [];\nconst videosByDate = {};\nconst dateGroups = [];\nconst skippedFiles = [];\n\n// Create a comprehensive data structure for the notes processor\n// The notes processor will read the actual results from the processor result folders\n\nconst notesGenerationTrigger = {\n  action: 'generate_notes_from_processor_results',\n  timestamp: new Date().toISOString(),\n  \n  // Instructions for the notes processor\n  instructions: {\n    step1: 'Read FFmpeg results from C:/Docker_Share/N8N/ffmpeg_results',\n    step2: 'Read Vision results from C:/Docker_Share/N8N/vision_results', \n    step3: 'Read Description results from C:/Docker_Share/N8N/description_results',\n    step4: 'Match results by filename and request_id',\n    step5: 'Extract dates from filenames (YYYYMMDD) or use fallback date',\n    step6: 'Group videos by date',\n    step7: 'Create YYYYMMDD_Notes.txt files with filename - description format'\n  },\n  \n  // Processor result locations\n  result_folders: {\n    ffmpeg_results: 'C:/Docker_Share/N8N/ffmpeg_results',\n    vision_results: 'C:/Docker_Share/N8N/vision_results',\n    description_results: 'C:/Docker_Share/N8N/description_results'\n  },\n  \n  // Output configuration\n  output: {\n    notes_folder: 'C:/Docker_Share/N8N/notes',\n    log_folder: 'C:/Docker_Share/N8N/logs',\n    filename_format: 'YYYYMMDD_Notes.txt',\n    content_format: 'filename - description'\n  },\n  \n  // Date extraction configuration\n  date_extraction: {\n    filename_pattern: '^(20\\\\d{6})',  // Extract YYYYMMDD from start of filename\n    fallback_date: '20120125',        // For files like M4H01890.MP4\n    date_source_priority: ['filename', 'file_modification', 'fallback']\n  },\n  \n  // Expected processing results (for validation)\n  expected_files: [\n    '20250406_110016_1.mp4',\n    '20250504_113836_1.mp4', \n    '20250622_100122.mp4',\n    'M4H01890.MP4',\n    'M4H01892.MP4'\n  ]\n};\n\nconsole.log('Created notes generation trigger with comprehensive instructions');\nconsole.log('Expected files to process:', notesGenerationTrigger.expected_files.length);\nconsole.log('Notes will be created in:', notesGenerationTrigger.output.notes_folder);\nconsole.log('Date extraction pattern:', notesGenerationTrigger.date_extraction.filename_pattern);\n\nreturn [{ json: notesGenerationTrigger }];"}, "id": "h3i4j5k6-7l8m-9n0o-1p2q-3r4s5t6u7v8w", "name": "Group Videos by Date", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [592, -336]}, {"parameters": {"jsCode": "// Log Data Before Notes Generation\nconst input = $input.first().json;\nconst allInputs = $input.all();\nconst timestamp = new Date().toISOString();\n\nconsole.log('=== NOTES GENERATION INPUT DATA DEBUG ===');\nconsole.log('Timestamp:', timestamp);\nconsole.log('Total inputs received:', allInputs.length);\nconsole.log('First input keys:', Object.keys(input));\nconsole.log('First input data:', JSON.stringify(input, null, 2));\n\nif (allInputs.length > 1) {\n  console.log('Multiple inputs detected:');\n  allInputs.forEach((inputItem, index) => {\n    console.log(`Input ${index}:`, Object.keys(inputItem.json));\n    console.log(`Input ${index} data:`, JSON.stringify(inputItem.json, null, 2));\n  });\n}\n\n// Check for specific data we expect\nif (input.grouped_videos) {\n  console.log('Grouped videos found:', Object.keys(input.grouped_videos));\n  console.log('Grouped videos data:', JSON.stringify(input.grouped_videos, null, 2));\n} else {\n  console.log('ERROR: No grouped_videos found in input!');\n}\n\nif (input.processing_results) {\n  console.log('Processing results found:', Object.keys(input.processing_results));\n  console.log('Processing results data:', JSON.stringify(input.processing_results, null, 2));\n} else {\n  console.log('WARNING: No processing_results found in input!');\n}\n\nif (input.video_files) {\n  console.log('Video files found:', input.video_files.length);\n  console.log('Video files list:', input.video_files);\n} else {\n  console.log('WARNING: No video_files found in input!');\n}\n\n// Create detailed log data\nconst logData = {\n  step: 'notes_generation_input_debug',\n  timestamp: timestamp,\n  input_analysis: {\n    total_inputs: allInputs.length,\n    has_grouped_videos: !!input.grouped_videos,\n    has_processing_results: !!input.processing_results,\n    has_video_files: !!input.video_files,\n    input_keys: Object.keys(input)\n  },\n  full_input_data: input,\n  all_inputs_summary: allInputs.map((item, index) => ({\n    index: index,\n    keys: Object.keys(item.json),\n    data_preview: JSON.stringify(item.json).substring(0, 200) + '...'\n  }))\n};\n\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: `notes_input_debug_${timestamp.replace(/[:.]/g, '-')}.json`,\n  fileExtension: 'json'\n};\n\n// Pass through the original data unchanged\nreturn [{\n  json: input,\n  binary: {\n    data: binaryData\n  }\n}];"}, "id": "debug-notes-input-12345", "name": "Log Notes Generation Input", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [600, -336]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/logs/' + 'notes_input_debug_' + new Date().toISOString().replace(/[:.]/g, '-') + '.json' }}", "dataPropertyName": "data", "options": {"overwrite": true}}, "id": "write-notes-input-debug-67890", "name": "Write Notes Input Debug Log", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [700, -336]}, {"parameters": {"jsCode": "// Generate Notes Files Data (Enhanced for Processor Results Integration)\nconst input = $input.first().json;\n\nconsole.log('=== GENERATING NOTES FILES DATA FROM PROCESSOR RESULTS ===');\nconsole.log('Input data:', JSON.stringify(input, null, 2));\n\n// Extract configuration from the trigger data\nconst resultFolders = input.result_folders || {\n  ffmpeg_results: 'C:/Docker_Share/N8N/ffmpeg_results',\n  vision_results: 'C:/Docker_Share/N8N/vision_results',\n  description_results: 'C:/Docker_Share/N8N/description_results'\n};\nconst outputConfig = input.output || {\n  notes_folder: 'C:/Docker_Share/N8N/notes',\n  log_folder: 'C:/Docker_Share/N8N/logs',\n  filename_format: 'YYYYMMDD_Notes.txt',\n  content_format: 'filename - description'\n};\nconst dateExtraction = input.date_extraction || {\n  filename_pattern: '^(20\\\\d{6})',\n  fallback_date: '20120125'\n};\nconst expectedFiles = input.expected_files || [\n  '20250406_110016_1.mp4',\n  '20250504_113836_1.mp4', \n  '20250622_100122.mp4',\n  'M4H01890.MP4',\n  'M4H01892.MP4'\n];\n\nconsole.log(`Expected files to process: ${expectedFiles.length}`);\nconsole.log('Result folders:', resultFolders);\nconsole.log('Output configuration:', outputConfig);\n\n// Create comprehensive data for the external notes processor\n// The external processor will read actual results from processor folders\nconst notesGenerationData = {\n  // Processing instructions\n  action: input.action || 'generate_notes_from_processor_results',\n  timestamp: new Date().toISOString(),\n  \n  // File processing configuration\n  processing: {\n    expected_files: expectedFiles,\n    result_folders: resultFolders,\n    date_extraction: dateExtraction\n  },\n  \n  // Output configuration\n  output: {\n    notes_folder: outputConfig.notes_folder || '/home/<USER>/shared/notes',\n    log_folder: outputConfig.log_folder || '/home/<USER>/shared/logs',\n    filename_format: outputConfig.filename_format || 'YYYYMMDD_Notes.txt',\n    content_format: outputConfig.content_format || 'filename - description'\n  },\n  \n  // Processing workflow\n  workflow: {\n    step1: 'Read FFmpeg results to get successful thumbnail extractions',\n    step2: 'Read Vision results to get kung fu detection (YES/NO)',\n    step3: 'Read Description results to get AI-generated descriptions',\n    step4: 'Match results by filename across all processors',\n    step5: 'Extract dates from filenames or use fallback dates',\n    step6: 'Group videos by extracted dates',\n    step7: 'Create notes files with format: filename - description',\n    step8: 'Write YYYYMMDD_Notes.txt files to notes folder'\n  },\n  \n  // Expected output structure\n  expected_output: {\n    notes_files: [\n      '20250406_Notes.txt',\n      '20250504_Notes.txt', \n      '20250622_Notes.txt',\n      '20120125_Notes.txt'  // For M4H files with fallback date\n    ],\n    content_example: [\n      '20250406_110016_1.mp4 - [AI-generated description]',\n      '20250504_113836_1.mp4 - [AI-generated kung fu technique]',\n      '20250622_100122.mp4 - [AI-generated kung fu technique]',\n      'M4H01890.MP4 - [AI-generated brief description]',\n      'M4H01892.MP4 - [AI-generated brief description]'\n    ]\n  },\n  \n  // Validation and debugging\n  validation: {\n    check_ffmpeg_results: true,\n    check_vision_results: true,\n    check_description_results: true,\n    require_all_files_processed: false,  // Allow partial processing\n    log_missing_files: true,\n    log_processing_errors: true\n  }\n};\n\nconsole.log('=== NOTES GENERATION DATA PREPARED ===');\nconsole.log(`Action: ${notesGenerationData.action}`);\nconsole.log(`Expected files: ${notesGenerationData.processing.expected_files.length}`);\nconsole.log(`Expected files list:`, notesGenerationData.processing.expected_files);\nconsole.log(`Notes folder: ${notesGenerationData.output.notes_folder}`);\nconsole.log(`Content format: ${notesGenerationData.output.content_format}`);\nconsole.log(`Result folders:`, notesGenerationData.processing.result_folders);\nconsole.log('Full data structure:', JSON.stringify(notesGenerationData, null, 2));\n\n// Create a JSON file with comprehensive instructions for external processing\nconst jsonContent = JSON.stringify(notesGenerationData, null, 2);\nconst dataFileName = `notes_generation_data_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;\n\nconsole.log(`Created data file: ${dataFileName}`);\n\nreturn [{\n  json: {\n    notesGenerationData: notesGenerationData,\n    dataFileName: dataFileName,\n    summary: {\n      action: notesGenerationData.action,\n      expected_files: notesGenerationData.processing.expected_files.length,\n      notes_folder: notesGenerationData.output.notes_folder\n    }\n  },\n  binary: {\n    data: {\n      data: Buffer.from(jsonContent, 'utf8').toString('base64'),\n      mimeType: 'application/json',\n      fileName: dataFileName,\n      fileExtension: 'json'\n    }\n  }\n}];"}, "id": "i4j5k6l7-8m9n-0o1p-2q3r-4s5t6u7v8w9x", "name": "Generate Notes Files", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [816, -336]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/logs/' + $json.dataFileName }}", "dataPropertyName": "data", "options": {"overwrite": true}}, "id": "j5k6l7m8-9n0o-1p2q-3r4s-5t6u7v8w9x0y", "name": "Write Notes File", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [1040, -336]}, {"parameters": {"jsCode": "// Log Notes Generation Results\nconst input = $input.first().json;\nconst summary = input.summary || {};\n\nconsole.log('=== NOTES GENERATION COMPLETE ===');\nconsole.log('Summary:', JSON.stringify(summary, null, 2));\n\nconst logData = {\n  step: 'notes_generation_complete',\n  timestamp: new Date().toISOString(),\n  summary: summary\n};\n\n// Create execution log\nconst logFileName = `notes_generation_log_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;\n\nreturn [{\n  json: {\n    summary: summary,\n    logFileName: logFileName\n  },\n  binary: {\n    data: {\n      data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n      mimeType: 'application/json',\n      fileName: logFileName,\n      fileExtension: 'json'\n    }\n  }\n}];"}, "id": "k6l7m8n9-0o1p-2q3r-4s5t-6u7v8w9x0y1z", "name": "Log Notes Generation", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1264, -336]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/logs/' + $json.logFileName }}", "dataPropertyName": "data", "options": {"overwrite": true}}, "id": "l7m8n9o0-1p2q-3r4s-5t6u-7v8w9x0y1z2a", "name": "Write Notes Log", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [1488, -336]}], "pinData": {}, "connections": {"Manual Trigger": {"main": [[{"node": "Load Configuration", "type": "main", "index": 0}]]}, "Load Configuration": {"main": [[{"node": "Read Config File", "type": "main", "index": 0}]]}, "Read Config File": {"main": [[{"node": "Parse Config File", "type": "main", "index": 0}]]}, "Parse Config File": {"main": [[{"node": "Log Load Configuration", "type": "main", "index": 0}]]}, "Log Load Configuration": {"main": [[{"node": "Prepare Video Scan", "type": "main", "index": 0}]]}, "Prepare Video Scan": {"main": [[{"node": "Log Prepare Video Scan", "type": "main", "index": 0}]]}, "Log Prepare Video Scan": {"main": [[{"node": "<PERSON>an Folder for Videos", "type": "main", "index": 0}]]}, "Scan Folder for Videos": {"main": [[{"node": "Log Scan Folder for Videos", "type": "main", "index": 0}]]}, "Log Scan Folder for Videos": {"main": [[{"node": "Process File List", "type": "main", "index": 0}]]}, "Process File List": {"main": [[{"node": "Log Process File List", "type": "main", "index": 0}]]}, "Log Process File List": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Create FFmpeg Request": {"main": [[{"node": "Save FFmpeg Request", "type": "main", "index": 0}]]}, "Save FFmpeg Request": {"main": [[{"node": "Read FFmpeg Result", "type": "main", "index": 0}]]}, "Read FFmpeg Result": {"main": [[{"node": "Process FFmpeg Result", "type": "main", "index": 0}]]}, "Process FFmpeg Result": {"main": [[{"node": "Log Process Thumbnail", "type": "main", "index": 0}]]}, "Log Process Thumbnail": {"main": [[{"node": "Write Vision Request (File-Based)", "type": "main", "index": 0}]]}, "Log AI Video Analysis": {"main": [[{"node": "Create Description Request", "type": "main", "index": 0}]]}, "Create Description Request": {"main": [[{"node": "Save Description Request", "type": "main", "index": 0}]]}, "Save Description Request": {"main": [[{"node": "Read Description Result", "type": "main", "index": 0}]]}, "Read Description Result": {"main": [[{"node": "Parse Description Result", "type": "main", "index": 0}]]}, "Parse Description Result": {"main": [[{"node": "Process AI Response", "type": "main", "index": 0}]]}, "Process AI Response": {"main": [[{"node": "Log Process AI Response", "type": "main", "index": 0}]]}, "Log Process AI Response": {"main": [[{"node": "Filter Kung Fu Videos", "type": "main", "index": 0}]]}, "Filter Kung Fu Videos": {"main": [[{"node": "Log Filter Kung Fu Videos", "type": "main", "index": 0}], [{"node": "Log Filter Kung Fu Videos", "type": "main", "index": 0}]]}, "Log Filter Kung Fu Videos": {"main": [[{"node": "Collect Final Results", "type": "main", "index": 0}]]}, "Collect Final Results": {"main": [[{"node": "Log Collect Final Results", "type": "main", "index": 0}]]}, "Log Collect Final Results": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Handle Errors": {"main": [[{"node": "Log Handle Errors", "type": "main", "index": 0}]]}, "Log Handle Errors": {"main": [[{"node": "Create Execution Log", "type": "main", "index": 0}]]}, "Create Execution Log": {"main": [[{"node": "Log Create Execution Log", "type": "main", "index": 0}]]}, "Log Create Execution Log": {"main": [[{"node": "Write Log to Shared Folder", "type": "main", "index": 0}]]}, "Write Log to Shared Folder": {"main": [[{"node": "Log Write Log to Shared Folder", "type": "main", "index": 0}]]}, "Write Vision Request (File-Based)": {"main": [[{"node": "Log Vision Request Data Flow", "type": "main", "index": 0}]]}, "Log Vision Request Data Flow": {"main": [[{"node": "Write Vision Debug Log", "type": "main", "index": 0}]]}, "Write Vision Debug Log": {"main": [[{"node": "Save Vision Request (File-Based)", "type": "main", "index": 0}]]}, "Save Vision Request (File-Based)": {"main": [[{"node": "Read Vision Result (File-Based)", "type": "main", "index": 0}]]}, "Read Vision Result (File-Based)": {"main": [[{"node": "Parse Vision Result (File-Based)", "type": "main", "index": 0}]]}, "Parse Vision Result (File-Based)": {"main": [[{"node": "Log AI Video Analysis", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Create FFmpeg Request", "type": "main", "index": 0}], [{"node": "Group Videos by Date", "type": "main", "index": 0}]]}, "Group Videos by Date": {"main": [[{"node": "Log Notes Generation Input", "type": "main", "index": 0}]]}, "Log Notes Generation Input": {"main": [[{"node": "Write Notes Input Debug Log", "type": "main", "index": 0}]]}, "Write Notes Input Debug Log": {"main": [[{"node": "Generate Notes Files", "type": "main", "index": 0}]]}, "Generate Notes Files": {"main": [[{"node": "Write Notes File", "type": "main", "index": 0}]]}, "Write Notes File": {"main": [[{"node": "Log Notes Generation", "type": "main", "index": 0}]]}, "Log Notes Generation": {"main": [[{"node": "Write Notes Log", "type": "main", "index": 0}]]}, "Log Write Log to Shared Folder": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "63bd8bd0-17b7-42af-a028-23e00be906fe", "meta": {"instanceId": "a9e00de748ec35ee88db078f832d6e48181d32e4fa741d36554310dd025f8599"}, "id": "rZPEtuXnSQOdmJGk", "tags": []}