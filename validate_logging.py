#!/usr/bin/env python3
import json

print('🔧 Testing Fixed Logging Workflow')
print('=' * 40)

# Load and validate the workflow
with open('kung_fu_video_workflow_with_logging.json', 'r', encoding='utf-8') as f:
    workflow = json.load(f)

print(f'✅ Workflow loaded: {len(workflow["nodes"])} nodes')

# Check the write node
write_node = next((node for node in workflow['nodes'] if node['name'] == 'Write Log to Shared Folder'), None)
if write_node:
    node_type = write_node['type']
    file_path = write_node['parameters'].get('fileName', '')
    print(f'✅ Write node type: {node_type}')
    print(f'✅ File path template: {file_path}')
    
    if 'writeBinaryFile' in node_type and '/home/<USER>/shared/' in file_path:
        print('✅ Correct node type and path format')
    else:
        print('❌ Issues with write node configuration')
else:
    print('❌ Write node not found')

# Check the log creation node
log_node = next((node for node in workflow['nodes'] if node['name'] == 'Create Execution Log'), None)
if log_node:
    js_code = log_node['parameters'].get('jsCode', '')
    if 'data:' in js_code and 'filename:' in js_code:
        print('✅ Log node returns correct data structure')
    else:
        print('❌ Log node data structure issue')

print('\n🎯 Ready for N8N import!')
