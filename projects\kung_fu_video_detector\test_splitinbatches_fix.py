#!/usr/bin/env python3
"""
Quick test to verify the SplitInBatches fix in VideoProcessor_B.json
"""

import json
import os

def analyze_workflow_connections():
    """Analyze the workflow connections to verify SplitIn<PERSON>atches fix"""
    
    workflow_path = "VideoProcessor_B.json"
    
    if not os.path.exists(workflow_path):
        print(f"❌ Workflow file not found: {workflow_path}")
        return False
    
    print("🔍 Analyzing VideoProcessor_B.json connections...")
    
    try:
        with open(workflow_path, 'r', encoding='utf-8') as f:
            workflow = json.load(f)
        
        connections = workflow.get('connections', {})
        
        # Check SplitInBatches connections
        split_connections = connections.get('Loop Over Items', {})
        main_outputs = split_connections.get('main', [])
        
        print("\n📊 SplitInBatches ('Loop Over Items') Analysis:")
        print(f"   Total outputs: {len(main_outputs)}")
        
        if len(main_outputs) >= 2:
            # Output 0 (Loop connector)
            loop_output = main_outputs[0]
            if loop_output and len(loop_output) > 0:
                loop_target = loop_output[0].get('node', 'Unknown')
                print(f"   ✅ Output 0 (Loop): → {loop_target}")
            else:
                print(f"   ❌ Output 0 (Loop): Empty")
                
            # Output 1 (Done connector)  
            done_output = main_outputs[1]
            if done_output and len(done_output) > 0:
                done_target = done_output[0].get('node', 'Unknown')
                print(f"   ✅ Output 1 (Done): → {done_target}")
            else:
                print(f"   ❌ Output 1 (Done): Empty")
        else:
            print(f"   ❌ Insufficient outputs: {len(main_outputs)}")
            return False
        
        # Check if final processing node connects back to SplitInBatches
        final_node_connections = connections.get('Log Write Log to Shared Folder', {})
        final_main = final_node_connections.get('main', [])
        
        print(f"\n🔄 Final Node ('Log Write Log to Shared Folder') Analysis:")
        if final_main and len(final_main) > 0 and len(final_main[0]) > 0:
            final_target = final_main[0][0].get('node', 'Unknown')
            print(f"   ✅ Connects to: {final_target}")
            
            if final_target == "Loop Over Items":
                print(f"   ✅ Correct: Loops back to SplitInBatches")
                loop_structure_correct = True
            else:
                print(f"   ❌ Incorrect: Should connect to 'Loop Over Items'")
                loop_structure_correct = False
        else:
            print(f"   ❌ No connection found")
            loop_structure_correct = False
        
        # Check batchSize parameter
        nodes = workflow.get('nodes', [])
        split_node = None
        for node in nodes:
            if node.get('name') == 'Loop Over Items':
                split_node = node
                break
        
        print(f"\n⚙️  SplitInBatches Configuration:")
        if split_node:
            batch_size = split_node.get('parameters', {}).get('batchSize', 'Not set')
            print(f"   batchSize: {batch_size}")
            
            if batch_size == 1:
                print(f"   ✅ Correct: batchSize = 1 (processes items individually)")
                batch_size_correct = True
            else:
                print(f"   ❌ Incorrect: batchSize should be 1")
                batch_size_correct = False
        else:
            print(f"   ❌ SplitInBatches node not found")
            batch_size_correct = False
        
        # Overall assessment
        print(f"\n🎯 Overall Assessment:")
        
        checks = [
            ("SplitInBatches Loop Output", loop_output and len(loop_output) > 0),
            ("SplitInBatches Done Output", done_output and len(done_output) > 0), 
            ("Loop Back Connection", loop_structure_correct),
            ("Batch Size Configuration", batch_size_correct)
        ]
        
        passed = 0
        for check_name, result in checks:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"   {status}: {check_name}")
            if result:
                passed += 1
        
        print(f"\n📈 Score: {passed}/{len(checks)} checks passed")
        
        if passed == len(checks):
            print(f"🎉 SUCCESS: SplitInBatches fix appears to be correctly implemented!")
            print(f"   The workflow should now process all 5 videos instead of just 1.")
            return True
        else:
            print(f"⚠️  ISSUES FOUND: The workflow may still have problems.")
            return False
            
    except Exception as e:
        print(f"❌ Error analyzing workflow: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Testing SplitInBatches Fix in VideoProcessor_B.json")
    print("=" * 60)
    
    success = analyze_workflow_connections()
    
    if success:
        print(f"\n✅ READY FOR TESTING:")
        print(f"1. Import VideoProcessor_B.json into N8N")
        print(f"2. Execute the workflow")
        print(f"3. Should process all 5 videos (2-3 minutes, not 1 second)")
        print(f"4. Monitor C:/Docker_Share/N8N/ folders for activity")
    else:
        print(f"\n❌ ISSUES DETECTED:")
        print(f"The workflow may still have connection problems.")
        print(f"Please review the analysis above.")

if __name__ == "__main__":
    main()
