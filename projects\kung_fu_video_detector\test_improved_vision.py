#!/usr/bin/env python3
"""
Test script to validate improved vision analysis settings
"""

import json
import base64
import requests
import time
from pathlib import Path

def test_vision_analysis():
    """Test the improved vision analysis with different settings"""
    
    # Get the most recent FFmpeg result
    ffmpeg_results_dir = Path("C:/Docker_Share/N8N/ffmpeg_results")
    result_files = list(ffmpeg_results_dir.glob("result_*.json"))
    
    if not result_files:
        print("No FFmpeg results found!")
        return
    
    # Get the most recent result
    latest_result = max(result_files, key=lambda x: x.stat().st_mtime)
    
    with open(latest_result, 'r') as f:
        ffmpeg_data = json.load(f)
    
    if not ffmpeg_data.get('success') or 'thumbnail_base64' not in ffmpeg_data:
        print(f"Invalid FFmpeg result: {latest_result}")
        return
    
    filename = ffmpeg_data.get('filename', 'unknown')
    thumbnail_base64 = ffmpeg_data.get('thumbnail_base64', '')
    
    print(f"Testing vision analysis for: {filename}")
    
    # Test different prompts and settings
    test_configs = [
        {
            "name": "Conservative (Current)",
            "temperature": 0.1,
            "max_tokens": 50,
            "prompt": "Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training. Look for martial arts poses, fighting stances, training equipment, or combat movements. Please respond with only YES if it shows kung fu/martial arts practice, or NO if it does not."
        },
        {
            "name": "Improved Settings",
            "temperature": 0.4,
            "max_tokens": 150,
            "prompt": """Analyze this video thumbnail for kung fu or martial arts content. Look for:
- Martial arts poses, stances, or movements
- Fighting techniques or combat training
- Traditional Chinese martial arts (kung fu, wushu, tai chi)
- Training equipment (wooden dummies, weapons, mats)
- Text mentioning martial arts terms (kung fu, wushu, bagua, tai chi, etc.)
- People in martial arts uniforms or practicing forms

If you see ANY of these elements, respond with YES. Only respond with NO if there are clearly no martial arts elements present. Be more inclusive rather than restrictive in your analysis."""
        },
        {
            "name": "High Creativity",
            "temperature": 0.7,
            "max_tokens": 200,
            "prompt": """Look at this video thumbnail and identify if it contains kung fu or martial arts content. 
Be generous in your interpretation - if you see martial arts text, poses, movements, or training, respond YES.
Explain what you see that makes you think it's martial arts related."""
        }
    ]
    
    results = []
    
    for config in test_configs:
        print(f"\nTesting: {config['name']}")
        
        payload = {
            "model": "mimo-vl-7b-rl@q8_k_xl",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": config["prompt"]
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{thumbnail_base64}"
                            }
                        }
                    ]
                }
            ],
            "temperature": config["temperature"],
            "max_tokens": config["max_tokens"],
            "stream": False
        }
        
        try:
            response = requests.post(
                "http://localhost:1234/v1/chat/completions",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                # Extract YES/NO
                analysis_result = "YES" if "YES" in content.upper() else "NO"
                
                results.append({
                    "config": config["name"],
                    "result": analysis_result,
                    "full_response": content,
                    "temperature": config["temperature"],
                    "max_tokens": config["max_tokens"]
                })
                
                print(f"Result: {analysis_result}")
                print(f"Response: {content}")
                
            else:
                print(f"Error: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"Error testing {config['name']}: {e}")
        
        time.sleep(2)  # Brief pause between tests
    
    # Save results
    output_file = Path("C:/Docker_Share/N8N/vision_test_results.json")
    with open(output_file, 'w') as f:
        json.dump({
            "filename": filename,
            "test_timestamp": time.time(),
            "results": results
        }, f, indent=2)
    
    print(f"\nTest results saved to: {output_file}")
    
    # Summary
    print("\n=== SUMMARY ===")
    for result in results:
        print(f"{result['config']}: {result['result']} (temp={result['temperature']})")

if __name__ == "__main__":
    test_vision_analysis()
