#!/usr/bin/env python3
"""
Test real video thumbnail with LM Studio to verify the fix
"""

import json
import subprocess
import base64
import uuid
import time
import os

def test_real_thumbnail():
    """Test with a real video thumbnail"""
    
    print("🧪 Testing real video thumbnail with fixed format...")
    
    # Extract thumbnail using the same FFmpeg command as N8N
    print("📹 Extracting thumbnail from video...")
    cmd = [
        'docker', 'exec', 'n8n', 'sh', '-c',
        'ffmpeg -i "/home/<USER>/shared/kung_fu_videos/20250406_110016_1.mp4" -ss 00:00:10 -vframes 1 -vf scale=320:240 -f image2pipe -vcodec png - | base64 -w 0'
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ FFmpeg failed: {result.stderr}")
        return False
    
    # Get the base64 data (skip FFmpeg info messages)
    lines = result.stdout.strip().split('\n')
    base64_data = lines[-1]  # Last line should be the base64 data
    
    print(f"✅ Extracted thumbnail: {len(base64_data)} characters")
    print(f"📝 Base64 sample: {base64_data[:50]}...")
    
    # Create test request
    request_id = f"test_{int(time.time())}"
    request_data = {
        "request_id": request_id,
        "filename": "test_real_thumbnail.mp4",
        "thumbnailBase64": base64_data,
        "prompt": "Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training. Please respond with only YES if it shows kung fu/martial arts practice, or NO if it does not.",
        "created_at": time.strftime("%Y-%m-%dT%H:%M:%S")
    }
    
    # Write request file
    request_file = f"C:/Docker_Share/N8N/vision_requests/request_{request_id}.json"
    with open(request_file, 'w', encoding='utf-8') as f:
        json.dump(request_data, f, indent=2)
    
    print(f"📝 Created test request: {request_file}")
    
    # Wait for processing
    print("⏳ Waiting for file processor to handle request...")
    result_file = f"C:/Docker_Share/N8N/vision_results/result_{request_id}.json"
    
    # Wait up to 30 seconds for result
    for i in range(30):
        if os.path.exists(result_file):
            break
        time.sleep(1)
        if i % 5 == 0:
            print(f"   Waiting... {i+1}s")
    
    if not os.path.exists(result_file):
        print("❌ No result file created after 30 seconds")
        return False
    
    # Read result
    with open(result_file, 'r', encoding='utf-8') as f:
        result = json.load(f)
    
    print("🎉 Result received!")
    print(f"✅ Success: {result.get('success', False)}")
    print(f"📊 Analysis: {result.get('analysis_result', 'Unknown')}")
    print(f"🥋 Contains Kung Fu: {result.get('contains_kung_fu', False)}")
    
    if result.get('error'):
        print(f"❌ Error: {result.get('error')}")
        print(f"📝 Details: {result.get('details', 'No details')}")
        return False
    
    print(f"💬 Full Response: {result.get('full_response', 'No response')}")
    
    return result.get('success', False)

if __name__ == '__main__':
    success = test_real_thumbnail()
    if success:
        print("\n🎉 Real thumbnail test PASSED! The fix works!")
        print("🚀 Ready to run the full kung fu workflow!")
    else:
        print("\n❌ Real thumbnail test FAILED")
        print("🔧 Need to investigate further")
