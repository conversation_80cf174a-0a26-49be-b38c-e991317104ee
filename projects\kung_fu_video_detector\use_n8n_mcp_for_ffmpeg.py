#!/usr/bin/env python3
"""Use N8N MCP to get guidance on FFmpeg installation and video processing"""

import sys
import os
import json
from pathlib import Path

# Add the n8n_builder module to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "n8n_builder"))

try:
    from n8n_builder import N8NBuilder
    from mcp_research_tool import N8NResearchTool
except ImportError as e:
    print(f"❌ Could not import N8N MCP tools: {e}")
    print("Make sure you're in the N8N_Builder directory and have the MCP tools installed")
    sys.exit(1)

async def research_ffmpeg_solutions():
    """Research FFmpeg installation and video processing solutions using N8N MCP."""
    print("🔍 RESEARCHING FFMPEG SOLUTIONS WITH N8N MCP")
    print("=" * 60)
    
    try:
        # Initialize the research tool
        research_tool = N8NResearchTool(cache_ttl=60, enable_enhanced_cache=False)
        
        async with research_tool:
            # Research FFmpeg installation in Docker containers
            print("1. Researching FFmpeg installation in Docker containers...")
            ffmpeg_results = await research_tool.search_n8n_docs(
                query="ffmpeg docker container installation video processing",
                node_type="executeCommand"
            )
            
            if ffmpeg_results:
                print(f"✅ Found {len(ffmpeg_results)} results for FFmpeg installation")
                for i, result in enumerate(ffmpeg_results[:3], 1):
                    print(f"   {i}. {result.title}")
                    print(f"      {result.snippet[:100]}...")
            else:
                print("❌ No specific FFmpeg installation results found")
            
            # Research video processing workflows
            print("\n2. Researching video processing workflows...")
            video_results = await research_tool.search_n8n_docs(
                query="video thumbnail extraction frame processing",
                node_type="executeCommand"
            )
            
            if video_results:
                print(f"✅ Found {len(video_results)} results for video processing")
                for i, result in enumerate(video_results[:3], 1):
                    print(f"   {i}. {result.title}")
                    print(f"      {result.snippet[:100]}...")
            else:
                print("❌ No specific video processing results found")
            
            # Research Docker container customization
            print("\n3. Researching Docker container customization...")
            docker_results = await research_tool.search_n8n_docs(
                query="docker container custom packages installation alpine",
                node_type="executeCommand"
            )
            
            if docker_results:
                print(f"✅ Found {len(docker_results)} results for Docker customization")
                for i, result in enumerate(docker_results[:3], 1):
                    print(f"   {i}. {result.title}")
                    print(f"      {result.snippet[:100]}...")
            else:
                print("❌ No specific Docker customization results found")
                
        return True
        
    except Exception as e:
        print(f"❌ Research failed: {e}")
        return False

def suggest_ffmpeg_solutions():
    """Suggest practical FFmpeg installation solutions."""
    print("\n🔧 PRACTICAL FFMPEG SOLUTIONS")
    print("=" * 40)
    
    print("Based on the Alpine Linux container issue, here are solutions:")
    print()
    
    print("1. **Fix Alpine Package Manager SSL Issue:**")
    print("   docker exec -u root n8n apk add --no-check-certificate ffmpeg")
    print("   (Bypasses SSL certificate verification)")
    print()
    
    print("2. **Use Custom N8N Docker Image:**")
    print("   Create Dockerfile with FFmpeg pre-installed:")
    print("   ```dockerfile")
    print("   FROM n8nio/n8n:latest")
    print("   USER root")
    print("   RUN apk add --no-cache ffmpeg")
    print("   USER node")
    print("   ```")
    print()
    
    print("3. **Mount FFmpeg Binary:**")
    print("   - Install FFmpeg on host system")
    print("   - Mount FFmpeg binary into container")
    print("   - Update docker-compose.yml with volume mount")
    print()
    
    print("4. **Alternative: Use Different Container:**")
    print("   - Switch to Ubuntu-based N8N image")
    print("   - Use apt-get install ffmpeg (no SSL issues)")
    print()
    
    print("5. **Test Current SSL Bypass:**")
    print("   docker exec -u root n8n apk add --no-check-certificate ffmpeg")

async def main():
    """Main function to research and suggest FFmpeg solutions."""
    print("🎯 N8N MCP FFMPEG RESEARCH & SOLUTIONS")
    print("=" * 50)
    
    # Try to research using MCP tools
    research_success = await research_ffmpeg_solutions()
    
    # Always provide practical solutions
    suggest_ffmpeg_solutions()
    
    print(f"\n🎉 Research completed! MCP research: {'✅' if research_success else '❌'}")
    print("Try the SSL bypass solution first: --no-check-certificate")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
