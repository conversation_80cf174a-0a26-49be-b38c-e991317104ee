#!/usr/bin/env python3
"""Create workflow with logging after each critical node"""

import json
import sys
from pathlib import Path

def create_per_node_logging():
    """Add logging nodes after each critical workflow step."""
    print("🔧 Creating Per-Node Logging Workflow")
    print("=" * 50)
    
    try:
        # Load current workflow
        workflow_path = Path("kung_fu_video_workflow_with_logging.json")
        with open(workflow_path, 'r', encoding='utf-8') as f:
            workflow = json.load(f)
        
        print(f"✅ Current workflow loaded: {len(workflow['nodes'])} nodes")
        
        # Create logging nodes for each critical step
        logging_nodes = []
        
        # 1. Log after "Scan Folder for Videos"
        scan_log_node = {
            "parameters": {
                "jsCode": """// Log scan results
const input = $input.first().json;
const timestamp = new Date().toISOString();

const logData = {
  step: 'scan_folder',
  timestamp: timestamp,
  input_data: input,
  files_found: input.files ? input.files.length : 0,
  raw_output: input.stdout || 'no stdout',
  error: input.error || null
};

console.log('=== SCAN FOLDER LOG ===');
console.log(`Files found: ${logData.files_found}`);
console.log(`Raw output: ${logData.raw_output}`);

return [{
  json: {
    data: JSON.stringify(logData, null, 2),
    filename: `scan_log_${timestamp.replace(/[:.]/g, '-')}.json`,
    originalData: input
  }
}];"""
            },
            "id": "log-scan-results",
            "name": "Log Scan Results",
            "type": "n8n-nodes-base.code",
            "typeVersion": 2,
            "position": [-1400, 100]
        }
        
        scan_write_node = {
            "parameters": {
                "fileName": "={{ '/home/<USER>/shared/' + $json.filename }}",
                "options": {"overwrite": true}
            },
            "id": "write-scan-log",
            "name": "Write Scan Log",
            "type": "n8n-nodes-base.writeBinaryFile",
            "typeVersion": 1,
            "position": [-1200, 100]
        }
        
        # 2. Log after "Process File List"
        process_log_node = {
            "parameters": {
                "jsCode": """// Log file processing results
const allInputs = $input.all();
const timestamp = new Date().toISOString();

const logData = {
  step: 'process_file_list',
  timestamp: timestamp,
  total_inputs: allInputs.length,
  processed_files: allInputs.map((input, index) => ({
    index: index,
    filename: input.json.filename || 'unknown',
    fullPath: input.json.fullPath || 'unknown',
    hasConfig: !!input.json.config,
    error: input.json.error || null
  }))
};

console.log('=== PROCESS FILE LIST LOG ===');
console.log(`Total files to process: ${logData.total_inputs}`);

return [{
  json: {
    data: JSON.stringify(logData, null, 2),
    filename: `process_log_${timestamp.replace(/[:.]/g, '-')}.json`,
    originalData: allInputs[0].json
  }
}];"""
            },
            "id": "log-process-results",
            "name": "Log Process Results", 
            "type": "n8n-nodes-base.code",
            "typeVersion": 2,
            "position": [-1000, 100]
        }
        
        process_write_node = {
            "parameters": {
                "fileName": "={{ '/home/<USER>/shared/' + $json.filename }}",
                "options": {"overwrite": true}
            },
            "id": "write-process-log",
            "name": "Write Process Log",
            "type": "n8n-nodes-base.writeBinaryFile", 
            "typeVersion": 1,
            "position": [-800, 100]
        }
        
        # 3. Log after "AI Video Analysis"
        ai_log_node = {
            "parameters": {
                "jsCode": """// Log AI analysis results
const allInputs = $input.all();
const timestamp = new Date().toISOString();

const logData = {
  step: 'ai_analysis',
  timestamp: timestamp,
  total_inputs: allInputs.length,
  ai_responses: allInputs.map((input, index) => ({
    index: index,
    filename: input.json.filename || 'unknown',
    ai_response: input.json.aiResponse || 'no response',
    http_status: input.json.statusCode || 'unknown',
    error: input.json.error || null
  }))
};

console.log('=== AI ANALYSIS LOG ===');
console.log(`Total AI calls: ${logData.total_inputs}`);
console.log(`Successful responses: ${logData.ai_responses.filter(r => !r.error).length}`);

return [{
  json: {
    data: JSON.stringify(logData, null, 2),
    filename: `ai_log_${timestamp.replace(/[:.]/g, '-')}.json`,
    originalData: allInputs[0].json
  }
}];"""
            },
            "id": "log-ai-results",
            "name": "Log AI Results",
            "type": "n8n-nodes-base.code",
            "typeVersion": 2,
            "position": [-600, 100]
        }
        
        ai_write_node = {
            "parameters": {
                "fileName": "={{ '/home/<USER>/shared/' + $json.filename }}",
                "options": {"overwrite": true}
            },
            "id": "write-ai-log",
            "name": "Write AI Log",
            "type": "n8n-nodes-base.writeBinaryFile",
            "typeVersion": 1,
            "position": [-400, 100]
        }
        
        # Add all logging nodes
        logging_nodes = [
            scan_log_node, scan_write_node,
            process_log_node, process_write_node, 
            ai_log_node, ai_write_node
        ]
        
        # Add nodes to workflow
        workflow['nodes'].extend(logging_nodes)
        
        # Add connections for logging nodes
        # Connect "Scan Folder for Videos" to logging
        if "Scan Folder for Videos" not in workflow['connections']:
            workflow['connections']["Scan Folder for Videos"] = {"main": [[]]}
        
        workflow['connections']["Scan Folder for Videos"]["main"][0].append({
            "node": "Log Scan Results",
            "type": "main", 
            "index": 0
        })
        
        workflow['connections']["Log Scan Results"] = {
            "main": [[{"node": "Write Scan Log", "type": "main", "index": 0}]]
        }
        
        # Connect "Process File List" to logging
        if "Process File List" not in workflow['connections']:
            workflow['connections']["Process File List"] = {"main": [[]]}
            
        workflow['connections']["Process File List"]["main"][0].append({
            "node": "Log Process Results",
            "type": "main",
            "index": 0
        })
        
        workflow['connections']["Log Process Results"] = {
            "main": [[{"node": "Write Process Log", "type": "main", "index": 0}]]
        }
        
        # Connect "AI Video Analysis" to logging
        if "AI Video Analysis" not in workflow['connections']:
            workflow['connections']["AI Video Analysis"] = {"main": [[]]}
            
        workflow['connections']["AI Video Analysis"]["main"][0].append({
            "node": "Log AI Results", 
            "type": "main",
            "index": 0
        })
        
        workflow['connections']["Log AI Results"] = {
            "main": [[{"node": "Write AI Log", "type": "main", "index": 0}]]
        }
        
        # Save enhanced workflow
        output_path = Path("kung_fu_video_workflow_per_node_logging.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(workflow, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Enhanced workflow saved: {output_path}")
        print(f"   Total nodes: {len(workflow['nodes'])}")
        print(f"   Added per-node logging: 6 new nodes")
        
        print(f"\n🎯 Per-Node Logging Strategy:")
        print(f"   1. Log after 'Scan Folder for Videos' → scan_log_[timestamp].json")
        print(f"   2. Log after 'Process File List' → process_log_[timestamp].json") 
        print(f"   3. Log after 'AI Video Analysis' → ai_log_[timestamp].json")
        print(f"   4. Plus final execution log → kung_fu_detector_log_[timestamp].json")
        
        print(f"\n📋 Benefits:")
        print(f"   - See exactly where workflow stops")
        print(f"   - Individual step debugging")
        print(f"   - Multiple log files for detailed analysis")
        print(f"   - Works even if workflow fails early")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating per-node logging: {e}")
        return False

if __name__ == "__main__":
    success = create_per_node_logging()
    sys.exit(0 if success else 1)
