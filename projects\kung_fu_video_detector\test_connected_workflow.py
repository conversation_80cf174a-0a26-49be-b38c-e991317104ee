#!/usr/bin/env python3
"""
Test the connected N8N workflow for kung fu video detection.
This script imports the workflow and tests its execution.
"""

import json
import requests
import time
import os

# N8N API configuration
N8N_BASE_URL = "http://localhost:5678"
N8N_API_URL = f"{N8N_BASE_URL}/api/v1"

def import_workflow():
    """Import the connected workflow into N8N."""
    print("Importing connected workflow into N8N...")
    
    # Load the workflow JSON
    with open("kung_fu_video_workflow_properly_connected.json", 'r') as f:
        workflow_data = json.load(f)
    
    # Import workflow via N8N API
    try:
        response = requests.post(
            f"{N8N_API_URL}/workflows",
            json=workflow_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 201:
            workflow_id = response.json()['id']
            print(f"✅ Workflow imported successfully! ID: {workflow_id}")
            return workflow_id
        else:
            print(f"❌ Failed to import workflow: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to N8N API. Is N8N running on localhost:5678?")
        return None
    except Exception as e:
        print(f"❌ Error importing workflow: {e}")
        return None

def execute_workflow(workflow_id):
    """Execute the workflow and monitor its progress."""
    print(f"Executing workflow {workflow_id}...")
    
    try:
        # Execute the workflow
        response = requests.post(
            f"{N8N_API_URL}/workflows/{workflow_id}/execute",
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            execution_id = response.json()['data']['executionId']
            print(f"✅ Workflow execution started! Execution ID: {execution_id}")
            
            # Monitor execution
            return monitor_execution(execution_id)
        else:
            print(f"❌ Failed to execute workflow: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error executing workflow: {e}")
        return None

def monitor_execution(execution_id):
    """Monitor workflow execution progress."""
    print(f"Monitoring execution {execution_id}...")
    
    max_wait_time = 120  # 2 minutes
    check_interval = 5   # 5 seconds
    elapsed_time = 0
    
    while elapsed_time < max_wait_time:
        try:
            response = requests.get(f"{N8N_API_URL}/executions/{execution_id}")
            
            if response.status_code == 200:
                execution_data = response.json()
                status = execution_data.get('finished', False)
                mode = execution_data.get('mode', 'unknown')
                
                print(f"  Status: {'Finished' if status else 'Running'} | Mode: {mode} | Time: {elapsed_time}s")
                
                if status:
                    success = execution_data.get('success', False)
                    if success:
                        print("✅ Workflow execution completed successfully!")
                        return execution_data
                    else:
                        print("❌ Workflow execution failed!")
                        print(f"Error: {execution_data.get('error', 'Unknown error')}")
                        return execution_data
            
            time.sleep(check_interval)
            elapsed_time += check_interval
            
        except Exception as e:
            print(f"❌ Error monitoring execution: {e}")
            break
    
    print(f"⏰ Execution monitoring timed out after {max_wait_time} seconds")
    return None

def check_log_files():
    """Check the log files created by the workflow."""
    print("\n=== CHECKING LOG FILES ===")
    
    log_files = [
        "1_config_log.json",
        "2_scan_log.json", 
        "3_thumbnail_success_*.json",
        "3_thumbnail_error_*.json"
    ]
    
    # Check Docker shared folder for log files
    try:
        result = os.system('powershell -Command "docker exec n8n ls -la /home/<USER>/shared/*.json"')
        if result == 0:
            print("✅ Log files found in Docker shared folder")
        else:
            print("❌ No log files found or error accessing Docker")
    except Exception as e:
        print(f"❌ Error checking log files: {e}")

def main():
    """Main function to test the connected workflow."""
    print("=== TESTING CONNECTED N8N WORKFLOW ===")
    print("This script will:")
    print("1. Import the connected workflow into N8N")
    print("2. Execute the workflow")
    print("3. Monitor execution progress")
    print("4. Check generated log files")
    print()
    
    # Step 1: Import workflow
    workflow_id = import_workflow()
    if not workflow_id:
        print("❌ Cannot proceed without importing workflow")
        return
    
    print()
    
    # Step 2: Execute workflow
    execution_data = execute_workflow(workflow_id)
    if not execution_data:
        print("❌ Workflow execution failed or timed out")
    
    print()
    
    # Step 3: Check log files
    check_log_files()
    
    print("\n=== TEST SUMMARY ===")
    if execution_data and execution_data.get('success', False):
        print("✅ Connected workflow test PASSED!")
        print("   - Workflow imported successfully")
        print("   - Execution completed without errors")
        print("   - Log files should be generated")
        print("\nNext steps:")
        print("1. Check N8N UI to verify all nodes are visually connected")
        print("2. Review log files in Docker shared folder")
        print("3. Verify thumbnail extraction worked (no mock fallbacks)")
    else:
        print("❌ Connected workflow test FAILED!")
        print("   - Check N8N logs for detailed error information")
        print("   - Verify Docker container has access to video files")
        print("   - Ensure FFmpeg is working properly")

if __name__ == "__main__":
    main()
