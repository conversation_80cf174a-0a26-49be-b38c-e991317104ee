#!/usr/bin/env python3
"""
File-based FFmpeg processor for N8N Kung Fu Video Detection.
Monitors shared folder for FFmpeg requests and processes them individually.
"""

import os
import json
import time
import subprocess
import base64
import logging
from datetime import datetime
from pathlib import Path

# Configuration
SHARED_FOLDER = "C:/Docker_Share/N8N"
FFMPEG_REQUESTS_FOLDER = os.path.join(SHARED_FOLDER, "ffmpeg_requests")
FFMPEG_RESULTS_FOLDER = os.path.join(SHARED_FOLDER, "ffmpeg_results")
POLL_INTERVAL = 1  # seconds
LOG_FILE = os.path.join(SHARED_FOLDER, "ffmpeg_processor.log")

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def ensure_directories():
    """Create necessary directories if they don't exist."""
    for folder in [FFMPEG_REQUESTS_FOLDER, FFMPEG_RESULTS_FOLDER]:
        os.makedirs(folder, exist_ok=True)
        logger.info(f"Ensured directory exists: {folder}")

def process_ffmpeg_request(request_file):
    """Process a single FFmpeg request file."""
    request_path = os.path.join(FFMPEG_REQUESTS_FOLDER, request_file)
    
    try:
        logger.info(f"Processing FFmpeg request: {request_file}")
        
        # Read request data
        with open(request_path, 'r', encoding='utf-8') as f:
            request_data = json.load(f)
        
        request_id = request_data.get('request_id')
        filename = request_data.get('filename')
        full_path = request_data.get('full_path')
        
        logger.info(f"Request ID: {request_id}")
        logger.info(f"Video file: {filename}")
        logger.info(f"Full path: {full_path}")
        
        # Validate request data
        if not all([request_id, filename, full_path]):
            raise ValueError("Missing required fields in request")
        
        # Check if video file exists
        if not os.path.exists(full_path.replace('/home/<USER>/shared', SHARED_FOLDER.replace('\\', '/'))):
            # Try Windows path
            windows_path = full_path.replace('/home/<USER>/shared', SHARED_FOLDER)
            if not os.path.exists(windows_path):
                raise FileNotFoundError(f"Video file not found: {full_path}")
            full_path = windows_path
        else:
            full_path = full_path.replace('/home/<USER>/shared', SHARED_FOLDER.replace('\\', '/'))
        
        # Execute FFmpeg command
        ffmpeg_command = [
            'ffmpeg',
            '-i', full_path,
            '-ss', '00:00:10',
            '-vframes', '1',
            '-vf', 'scale=320:240',
            '-f', 'image2pipe',
            '-vcodec', 'png',
            '-'
        ]
        
        logger.info(f"Executing FFmpeg: {' '.join(ffmpeg_command)}")
        
        # Run FFmpeg
        result = subprocess.run(
            ffmpeg_command,
            capture_output=True,
            timeout=30
        )
        
        # Process result
        if result.returncode == 0 and result.stdout:
            # Convert to base64
            thumbnail_base64 = base64.b64encode(result.stdout).decode('utf-8')
            
            # Validate PNG signature
            if not thumbnail_base64.startswith('iVBORw0KGgo'):
                logger.warning("Base64 doesn't start with PNG signature, attempting to fix...")
                # Try to find the actual PNG start
                png_start = thumbnail_base64.find('iVBORw0KGgo')
                if png_start > 0:
                    thumbnail_base64 = thumbnail_base64[png_start:]
                    logger.info("Fixed base64 PNG signature")
            
            # Validate final result
            try:
                decoded_data = base64.b64decode(thumbnail_base64)
                if decoded_data[:8] == b'\x89PNG\r\n\x1a\n':
                    logger.info("Valid PNG signature confirmed")
                else:
                    raise ValueError("Invalid PNG signature after processing")
            except Exception as e:
                raise ValueError(f"Base64 validation failed: {e}")
            
            # Create success result
            result_data = {
                'request_id': request_id,
                'filename': filename,
                'success': True,
                'thumbnail_base64': thumbnail_base64,
                'thumbnail_size_kb': round(len(thumbnail_base64) / 1024, 2),
                'extraction_method': 'file_based_ffmpeg',
                'processed_at': datetime.now().isoformat(),
                'ffmpeg_command': ' '.join(ffmpeg_command)
            }
            
            logger.info(f"SUCCESS: Thumbnail extracted ({result_data['thumbnail_size_kb']}KB)")
            
        else:
            # FFmpeg failed
            error_msg = result.stderr.decode('utf-8') if result.stderr else 'Unknown FFmpeg error'
            logger.error(f"FFmpeg failed: {error_msg}")
            
            result_data = {
                'request_id': request_id,
                'filename': filename,
                'success': False,
                'error': error_msg,
                'error_type': 'ffmpeg_execution_failed',
                'error_step': 'thumbnail_extraction',
                'exit_code': result.returncode,
                'processed_at': datetime.now().isoformat(),
                'ffmpeg_command': ' '.join(ffmpeg_command),
                'video_path': video_path,
                'output_path': output_path,
                'skip_reason': f'FFmpeg thumbnail extraction failed with exit code {result.returncode}'
            }
        
        # Write result file
        result_filename = f"result_{request_id}.json"
        result_path = os.path.join(FFMPEG_RESULTS_FOLDER, result_filename)
        
        with open(result_path, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, indent=2)
        
        logger.info(f"Result written: {result_filename}")
        
        # Clean up request file
        os.remove(request_path)
        logger.info(f"Request file cleaned up: {request_file}")
        
    except Exception as e:
        logger.error(f"Error processing {request_file}: {e}")
        
        # Create error result
        try:
            error_result = {
                'request_id': request_data.get('request_id', 'unknown'),
                'filename': request_data.get('filename', 'unknown'),
                'success': False,
                'error': str(e),
                'processed_at': datetime.now().isoformat()
            }
            
            result_filename = f"result_{error_result['request_id']}.json"
            result_path = os.path.join(FFMPEG_RESULTS_FOLDER, result_filename)
            
            with open(result_path, 'w', encoding='utf-8') as f:
                json.dump(error_result, f, indent=2)
            
            # Clean up request file
            if os.path.exists(request_path):
                os.remove(request_path)
                
        except Exception as cleanup_error:
            logger.error(f"Error during cleanup: {cleanup_error}")

def monitor_requests():
    """Main monitoring loop."""
    logger.info("Starting FFmpeg file processor...")
    logger.info(f"Monitoring: {FFMPEG_REQUESTS_FOLDER}")
    logger.info(f"Results: {FFMPEG_RESULTS_FOLDER}")
    logger.info(f"Poll interval: {POLL_INTERVAL} seconds")
    
    ensure_directories()
    
    while True:
        try:
            # Look for request files
            request_files = [f for f in os.listdir(FFMPEG_REQUESTS_FOLDER) 
                           if f.startswith('request_') and f.endswith('.json')]
            
            if request_files:
                logger.info(f"Found {len(request_files)} FFmpeg requests")
                
                for request_file in request_files:
                    process_ffmpeg_request(request_file)
            
            time.sleep(POLL_INTERVAL)
            
        except KeyboardInterrupt:
            logger.info("FFmpeg processor stopped by user")
            break
        except Exception as e:
            logger.error(f"Error in monitoring loop: {e}")
            time.sleep(POLL_INTERVAL)

if __name__ == "__main__":
    monitor_requests()
