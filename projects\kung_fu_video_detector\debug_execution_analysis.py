#!/usr/bin/env python3
"""
Debug execution issues in the kung fu video detector workflow.
This script analyzes log files to identify where execution stops.
"""

import json
import os
import subprocess
import datetime
from pathlib import Path

def check_docker_environment():
    """Check if Docker and N8N are running properly."""
    print("=== DOCKER ENVIRONMENT CHECK ===")
    
    try:
        # Check if Docker is running
        result = subprocess.run(['docker', 'ps'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Docker is running")
            
            # Check N8N container
            if 'n8n' in result.stdout:
                print("✅ N8N container is running")
            else:
                print("❌ N8N container not found")
                return False
        else:
            print("❌ Docker is not running")
            return False
            
    except FileNotFoundError:
        print("❌ Docker command not found")
        return False
    
    return True

def check_video_files():
    """Check if video files exist in Docker shared folder."""
    print("\n=== VIDEO FILES CHECK ===")
    
    try:
        result = subprocess.run([
            'docker', 'exec', 'n8n', 'ls', '-la', '/home/<USER>/shared/kung_fu_videos/'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            video_files = [line for line in lines if any(ext in line.lower() for ext in ['.mp4', '.avi', '.mov', '.mkv'])]
            
            print(f"✅ Found {len(video_files)} video files:")
            for file_line in video_files:
                parts = file_line.split()
                if len(parts) >= 9:
                    filename = parts[-1]
                    size = parts[4]
                    print(f"   - {filename} ({size} bytes)")
            
            return len(video_files)
        else:
            print("❌ Cannot access video files directory")
            print(f"Error: {result.stderr}")
            return 0
            
    except Exception as e:
        print(f"❌ Error checking video files: {e}")
        return 0

def check_ffmpeg():
    """Check if FFmpeg is working in the container."""
    print("\n=== FFMPEG CHECK ===")
    
    try:
        result = subprocess.run([
            'docker', 'exec', 'n8n', 'ffmpeg', '-version'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ FFmpeg is available: {version_line}")
            return True
        else:
            print("❌ FFmpeg not available")
            print(f"Error: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking FFmpeg: {e}")
        return False

def test_ffmpeg_thumbnail():
    """Test FFmpeg thumbnail extraction on one video file."""
    print("\n=== FFMPEG THUMBNAIL TEST ===")
    
    try:
        # Test thumbnail extraction on first video file
        result = subprocess.run([
            'docker', 'exec', 'n8n', 'sh', '-c',
            'ffmpeg -i "/home/<USER>/shared/kung_fu_videos/20250406_110016_1.mp4" -ss 00:00:10 -vframes 1 -f image2pipe -vcodec png - 2>/dev/null | wc -c'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            size = int(result.stdout.strip())
            if size > 1000:
                print(f"✅ FFmpeg thumbnail extraction works: {size} bytes")
                return True
            else:
                print(f"❌ FFmpeg thumbnail too small: {size} bytes")
                return False
        else:
            print("❌ FFmpeg thumbnail extraction failed")
            print(f"Error: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing FFmpeg: {e}")
        return False

def analyze_log_files():
    """Analyze log files to understand execution flow."""
    print("\n=== LOG FILE ANALYSIS ===")
    
    try:
        # Get list of log files
        result = subprocess.run([
            'docker', 'exec', 'n8n', 'ls', '-la', '/home/<USER>/shared/*.json'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            log_files = []
            for line in result.stdout.strip().split('\n'):
                if '.json' in line:
                    parts = line.split()
                    if len(parts) >= 9:
                        filename = parts[-1].split('/')[-1]
                        size = parts[4]
                        timestamp = f"{parts[5]} {parts[6]} {parts[7]}"
                        log_files.append((filename, size, timestamp))
            
            print(f"Found {len(log_files)} log files:")
            
            # Analyze each log file
            execution_steps = {}
            for filename, size, timestamp in log_files:
                print(f"\n📄 {filename} ({size} bytes, {timestamp})")
                
                # Read log file content
                cat_result = subprocess.run([
                    'docker', 'exec', 'n8n', 'cat', f'/home/<USER>/shared/{filename}'
                ], capture_output=True, text=True)
                
                if cat_result.returncode == 0:
                    try:
                        log_data = json.loads(cat_result.stdout)
                        step = log_data.get('step', 'unknown')
                        execution_steps[step] = log_data
                        
                        # Analyze step-specific data
                        if step == '1_config_load':
                            config = log_data.get('config', {})
                            print(f"   ✅ Config loaded: {config.get('video_folder', 'unknown')}")
                            
                        elif step == '2_scan_success':
                            files_found = log_data.get('total_files_found', 0)
                            print(f"   ✅ Files found: {files_found}")
                            
                        elif step == '2_scan_error':
                            error = log_data.get('error', 'unknown')
                            print(f"   ❌ Scan error: {error}")
                            
                        elif step.startswith('3_thumbnail_success'):
                            filename = log_data.get('filename', 'unknown')
                            size = log_data.get('thumbnail_size_bytes', 0)
                            print(f"   ✅ Thumbnail success: {filename} ({size} bytes)")
                            
                        elif step.startswith('3_thumbnail_error'):
                            filename = log_data.get('filename', 'unknown')
                            error = log_data.get('error', 'unknown')
                            print(f"   ❌ Thumbnail error: {filename} - {error}")
                            
                    except json.JSONDecodeError:
                        print(f"   ❌ Invalid JSON in log file")
                else:
                    print(f"   ❌ Cannot read log file")
            
            return execution_steps
        else:
            print("❌ No log files found")
            return {}
            
    except Exception as e:
        print(f"❌ Error analyzing log files: {e}")
        return {}

def diagnose_execution_issues(execution_steps):
    """Diagnose execution issues based on log analysis."""
    print("\n=== EXECUTION DIAGNOSIS ===")
    
    if not execution_steps:
        print("❌ No execution steps found - workflow may not have run")
        return
    
    # Check execution flow
    expected_steps = ['1_config_load', '2_scan_success', '3_thumbnail_success']
    
    if '1_config_load' in execution_steps:
        print("✅ Step 1: Configuration loaded successfully")
    else:
        print("❌ Step 1: Configuration loading failed")
        return
    
    if '2_scan_success' in execution_steps:
        scan_data = execution_steps['2_scan_success']
        files_found = scan_data.get('total_files_found', 0)
        print(f"✅ Step 2: Video scan successful ({files_found} files)")
        
        if files_found == 0:
            print("❌ Issue: No video files found during scan")
            print("   Check: Video files exist in /home/<USER>/shared/kung_fu_videos/")
            
    elif '2_scan_error' in execution_steps:
        print("❌ Step 2: Video scan failed")
        error_data = execution_steps['2_scan_error']
        print(f"   Error: {error_data.get('error', 'unknown')}")
        return
    else:
        print("❌ Step 2: No scan results found")
        print("   Issue: Workflow stopped after configuration step")
        return
    
    # Check thumbnail processing
    thumbnail_success_steps = [step for step in execution_steps.keys() if step.startswith('3_thumbnail_success')]
    thumbnail_error_steps = [step for step in execution_steps.keys() if step.startswith('3_thumbnail_error')]
    
    if thumbnail_success_steps:
        print(f"✅ Step 3: Thumbnail extraction successful ({len(thumbnail_success_steps)} files)")
        for step in thumbnail_success_steps:
            data = execution_steps[step]
            filename = data.get('filename', 'unknown')
            size = data.get('thumbnail_size_bytes', 0)
            print(f"   - {filename}: {size} bytes")
    
    if thumbnail_error_steps:
        print(f"❌ Step 3: Thumbnail extraction errors ({len(thumbnail_error_steps)} files)")
        for step in thumbnail_error_steps:
            data = execution_steps[step]
            filename = data.get('filename', 'unknown')
            error = data.get('error', 'unknown')
            print(f"   - {filename}: {error}")
    
    if not thumbnail_success_steps and not thumbnail_error_steps:
        print("❌ Step 3: No thumbnail processing found")
        print("   Issue: Workflow stopped after file scanning")

def main():
    """Main function to debug execution issues."""
    print("=== KUNG FU VIDEO DETECTOR - EXECUTION DEBUG ===")
    print("This script analyzes the workflow execution to identify issues.")
    print()
    
    # Check environment
    if not check_docker_environment():
        print("❌ Docker environment issues detected")
        return
    
    # Check video files
    video_count = check_video_files()
    if video_count == 0:
        print("❌ No video files found - workflow will fail")
        return
    
    # Check FFmpeg
    if not check_ffmpeg():
        print("❌ FFmpeg issues detected")
        return
    
    # Test FFmpeg thumbnail
    if not test_ffmpeg_thumbnail():
        print("❌ FFmpeg thumbnail extraction issues detected")
    
    # Analyze log files
    execution_steps = analyze_log_files()
    
    # Diagnose issues
    diagnose_execution_issues(execution_steps)
    
    print("\n=== RECOMMENDATIONS ===")
    if execution_steps:
        print("✅ Workflow is executing - check log analysis above")
        print("   - If execution is too fast, check per-node timing")
        print("   - If thumbnails fail, check FFmpeg commands")
        print("   - If files not found, check Docker volume mounts")
    else:
        print("❌ Workflow not executing properly")
        print("   - Import the connected workflow into N8N")
        print("   - Execute the workflow manually")
        print("   - Check N8N logs for errors")

if __name__ == "__main__":
    main()
