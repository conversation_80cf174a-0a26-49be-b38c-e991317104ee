{"name": "Kung Fu Video Detector - Real FFmpeg", "nodes": [{"parameters": {}, "id": "ed85e2e5-dc5e-4575-88eb-afd38180a7c8", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"jsCode": "// Load configuration\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',\n  file_extensions: ['.mp4', '.avi', '.mov', '.mkv']\n};\n\nconsole.log('=== CONFIGURATION LOADED ===');\nreturn [{ json: { config: config } }];"}, "id": "load-config", "name": "Load Configuration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [500, 300]}, {"parameters": {"command": "find \"/home/<USER>/shared/kung_fu_videos\" -type f \\( -iname \"*.mp4\" -o -iname \"*.avi\" -o -iname \"*.mov\" -o -iname \"*.mkv\" \\) | head -1"}, "id": "scan-first-video", "name": "<PERSON>an First Video", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [750, 300]}, {"parameters": {"jsCode": "// Process scan result to get the first video file path\nconst input = $input.first().json;\nconst commandOutput = input.stdout || '';\nconst exitCode = input.exitCode || 0;\n\nconsole.log('=== PROCESSING SCAN RESULT ===');\nconsole.log('Exit code:', exitCode);\nconsole.log('Output:', commandOutput);\n\nif (exitCode !== 0 || !commandOutput || commandOutput.trim() === '') {\n  console.log('No video files found');\n  return [{ json: { error: 'No video files found' } }];\n}\n\nconst filePath = commandOutput.trim();\nconst filename = filePath.split('/').pop();\n\nconsole.log(`Found video: ${filename}`);\nconsole.log(`Full path: ${filePath}`);\n\nreturn [{ json: { filename: filename, fullPath: filePath, foundFile: true } }];"}, "id": "process-scan", "name": "Process Scan", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1000, 300]}, {"parameters": {"command": "ffmpeg -i \"/home/<USER>/shared/kung_fu_videos/20250406_110016_1.mp4\" -ss 00:00:10 -vframes 1 -vf scale=320:240 -f image2pipe -vcodec png -"}, "id": "extract-thumbnail-hardcoded", "name": "Extract Thumbnail (Hardcoded)", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1250, 300]}, {"parameters": {"jsCode": "// Process real FFmpeg output\nconst input = $input.first().json;\nconst rawImageData = input.stdout || '';\nconst exitCode = input.exitCode || 0;\nconst stderr = input.stderr || '';\n\nconsole.log('=== PROCESSING REAL FFMPEG OUTPUT ===');\nconsole.log(`FFmpeg exit code: ${exitCode}`);\nconsole.log(`Raw image data length: ${rawImageData.length}`);\nconsole.log(`Stderr: ${stderr}`);\n\n// Check if FFmpeg extraction was successful\nif (exitCode !== 0 || !rawImageData || rawImageData.length < 1000) {\n  console.log('ERROR: FFmpeg thumbnail extraction FAILED');\n  \n  // Create detailed error log\n  const errorDetails = {\n    timestamp: new Date().toISOString(),\n    operation: 'ffmpeg_thumbnail_extraction',\n    exitCode: exitCode,\n    stderr: stderr,\n    rawDataLength: rawImageData.length,\n    error: 'FFmpeg failed to extract video thumbnail'\n  };\n  \n  console.log('Error details:', JSON.stringify(errorDetails, null, 2));\n  \n  // Return error state - NO MOCK FALLBACK\n  return [{\n    json: {\n      error: errorDetails,\n      success: false,\n      thumbnailBase64: null,\n      hasValidThumbnail: false\n    }\n  }];\n}\n\n// Convert raw PNG data to base64\nconst thumbnailBase64 = Buffer.from(rawImageData, 'binary').toString('base64');\n\nconsole.log('SUCCESS: Real thumbnail extracted!');\nconsole.log(`Thumbnail size: ${Math.round(thumbnailBase64.length / 1024)}KB`);\n\nreturn [{\n  json: {\n    thumbnailBase64: thumbnailBase64,\n    hasValidThumbnail: true,\n    success: true,\n    thumbnailSizeKB: Math.round(thumbnailBase64.length / 1024),\n    extractionMethod: 'real_ffmpeg_hardcoded'\n  }\n}];"}, "id": "process-thumbnail", "name": "Process Thumbnail", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1500, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Load Configuration", "type": "main", "index": 0}]]}, "Load Configuration": {"main": [[{"node": "<PERSON>an First Video", "type": "main", "index": 0}]]}, "Scan First Video": {"main": [[{"node": "Process Scan", "type": "main", "index": 0}]]}, "Process Scan": {"main": [[{"node": "Extract Thumbnail (Hardcoded)", "type": "main", "index": 0}]]}, "Extract Thumbnail (Hardcoded)": {"main": [[{"node": "Process Thumbnail", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}