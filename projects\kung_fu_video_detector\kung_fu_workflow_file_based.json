{"name": "Kung Fu Video Detection - File Based", "nodes": [{"parameters": {}, "id": "manual-trigger-file-based", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"jsCode": "// Load Configuration for Kung Fu Video Detection\nconst config = {\n  videoFolder: '/home/<USER>/shared/kung_fu_videos',\n  supportedFormats: ['.mp4', '.avi', '.mov', '.mkv'],\n  thumbnailSettings: {\n    timeOffset: '00:00:10',\n    scale: '320:240',\n    format: 'png'\n  },\n  aiModel: 'mimo-vl-7b-rl@q8_k_xl',\n  logFolder: '/home/<USER>/shared/logs'\n};\n\nconsole.log('=== KUNG FU VIDEO DETECTION CONFIG ===');\nconsole.log('Video folder:', config.videoFolder);\nconsole.log('Supported formats:', config.supportedFormats);\nconsole.log('AI model:', config.aiModel);\n\nreturn [{ json: config }];"}, "id": "load-config-file-based", "name": "Load Configuration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [450, 300]}, {"parameters": {"command": "find \"/home/<USER>/shared/kung_fu_videos\" -type f \\( -iname \"*.mp4\" -o -iname \"*.avi\" -o -iname \"*.mov\" -o -iname \"*.mkv\" \\)"}, "id": "scan-videos-file-based", "name": "Scan for Videos", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [650, 300]}, {"parameters": {"jsCode": "// Process Video File List\nconst input = $input.first().json;\nconst stdout = input.stdout;\n\nconsole.log('=== PROCESSING VIDEO FILE LIST ===');\nconsole.log('Raw stdout:', stdout);\n\n// Split stdout into individual file paths\nconst filePaths = stdout.trim().split('\\n').filter(path => path.length > 0);\n\nconsole.log('Found video files:', filePaths.length);\nfilePaths.forEach((path, index) => {\n  console.log(`${index + 1}. ${path}`);\n});\n\n// Return array of file objects\nconst fileObjects = filePaths.map(fullPath => {\n  const filename = fullPath.split('/').pop();\n  return {\n    fullPath: fullPath,\n    filename: filename,\n    processed: false\n  };\n});\n\nreturn fileObjects.map(file => ({ json: file }));"}, "id": "process-file-list-file-based", "name": "Process File List", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [850, 300]}, {"parameters": {"command": "=ffmpeg -i \"{{ $json.fullPath }}\" -ss 00:00:10 -vframes 1 -vf scale=320:240 -f image2pipe -vcodec png - | base64 -w 0"}, "id": "extract-thumbnail-file-based", "name": "Extract Video Thumbnail", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1050, 300]}, {"parameters": {"jsCode": "// Process Thumbnail Data\nconst input = $input.first().json;\nconst stdout = input.stdout;\nconst filename = input.filename;\nconst fullPath = input.fullPath;\n\nconsole.log('=== PROCESSING THUMBNAIL ===');\nconsole.log('Filename:', filename);\nconsole.log('Thumbnail base64 length:', stdout ? stdout.length : 0);\n\nif (!stdout || stdout.length === 0) {\n  console.log('ERROR: No thumbnail data received');\n  return [{\n    json: {\n      filename: filename,\n      fullPath: fullPath,\n      error: 'No thumbnail data generated',\n      thumbnailBase64: null\n    }\n  }];\n}\n\n// Clean the base64 data (remove any whitespace)\nconst cleanBase64 = stdout.trim();\n\nconsole.log('Clean base64 length:', cleanBase64.length);\nconsole.log('Base64 sample:', cleanBase64.substring(0, 50) + '...');\n\nreturn [{\n  json: {\n    filename: filename,\n    fullPath: fullPath,\n    thumbnailBase64: cleanBase64,\n    thumbnailSize: cleanBase64.length,\n    processed: true\n  }\n}];"}, "id": "process-thumbnail-file-based", "name": "Process Thumbnail", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1250, 300]}, {"parameters": {"jsCode": "// Write Vision Request to Shared Folder\nconst input = $input.first().json;\nconst thumbnailBase64 = input.thumbnailBase64;\nconst filename = input.filename;\n\n// Skip if no thumbnail data\nif (!thumbnailBase64) {\n  console.log('Skipping vision analysis - no thumbnail data');\n  return [{\n    json: {\n      filename: filename,\n      success: false,\n      analysis_result: 'ERROR',\n      contains_kung_fu: false,\n      error: 'No thumbnail data available'\n    }\n  }];\n}\n\n// Generate unique request ID\nconst requestId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n\n// Prepare request data\nconst requestData = {\n  request_id: requestId,\n  filename: filename,\n  thumbnailBase64: thumbnailBase64,\n  prompt: \"Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training. Look for martial arts poses, fighting stances, training equipment, or combat movements. Please respond with only YES if it shows kung fu/martial arts practice, or NO if it does not.\",\n  created_at: new Date().toISOString()\n};\n\nconsole.log('=== WRITING VISION REQUEST ===');\nconsole.log('Request ID:', requestId);\nconsole.log('Filename:', filename);\nconsole.log('Thumbnail length:', thumbnailBase64.length);\n\n// Prepare data for file writing\nconst jsonString = JSON.stringify(requestData, null, 2);\nconst binaryData = {\n  data: Buffer.from(jsonString, 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: `request_${requestId}.json`,\n  fileExtension: 'json'\n};\n\nreturn [{\n  json: {\n    request_id: requestId,\n    filename: filename,\n    request_file: `request_${requestId}.json`,\n    data_size: jsonString.length,\n    original_data: input\n  },\n  binary: {\n    data: binaryData\n  }\n}];"}, "id": "write-vision-request-kung-fu", "name": "Write Vision Request", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1450, 300]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/vision_requests/' + $json.request_file }}", "options": {"overwrite": true}}, "id": "save-vision-request-kung-fu", "name": "Save Vision Request", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [1650, 300]}, {"parameters": {"command": "=sleep 5 && if [ -f \"/home/<USER>/shared/vision_results/result_{{ $json.request_id }}.json\" ]; then cat \"/home/<USER>/shared/vision_results/result_{{ $json.request_id }}.json\"; else echo '{\"success\": false, \"error\": \"Result not found after 5 seconds\", \"request_id\": \"{{ $json.request_id }}\", \"filename\": \"{{ $json.filename }}\"}'; fi"}, "id": "read-vision-result-kung-fu", "name": "Read Vision Result", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1850, 300]}, {"parameters": {"jsCode": "// Parse Vision Result and Generate Final Report\nconst input = $input.first().json;\nconst stdout = input.stdout;\nconst originalData = input.original_data || {};\n\nconsole.log('=== PARSING VISION RESULT ===');\nconsole.log('Raw stdout:', stdout);\n\ntry {\n  // Parse the JSON result\n  const result = JSON.parse(stdout);\n  \n  console.log('Parsed result:', result);\n  console.log('Success:', result.success);\n  console.log('Analysis result:', result.analysis_result);\n  \n  // Create comprehensive final result\n  const finalResult = {\n    // Original file info\n    filename: result.filename || originalData.filename || 'unknown',\n    fullPath: originalData.fullPath || 'unknown',\n    \n    // Vision analysis results\n    success: result.success || false,\n    analysis_result: result.analysis_result || 'NO',\n    contains_kung_fu: result.contains_kung_fu || false,\n    full_response: result.full_response || '',\n    \n    // Processing info\n    model_used: result.model_used || 'unknown',\n    processed_at: result.processed_at || new Date().toISOString(),\n    request_id: result.request_id || 'unknown',\n    \n    // Thumbnail info\n    thumbnail_size: originalData.thumbnailSize || 0,\n    \n    // Error info if any\n    error: result.error || null\n  };\n  \n  console.log('=== FINAL KUNG FU ANALYSIS RESULT ===');\n  console.log('File:', finalResult.filename);\n  console.log('Contains Kung Fu:', finalResult.contains_kung_fu);\n  console.log('Analysis:', finalResult.analysis_result);\n  \n  return [{ json: finalResult }];\n  \n} catch (error) {\n  console.log('Error parsing result:', error.message);\n  \n  // Return error result\n  return [{\n    json: {\n      filename: originalData.filename || 'unknown',\n      fullPath: originalData.fullPath || 'unknown',\n      success: false,\n      analysis_result: 'ERROR',\n      contains_kung_fu: false,\n      full_response: '',\n      error: `Failed to parse result: ${error.message}`,\n      raw_stdout: stdout,\n      processed_at: new Date().toISOString()\n    }\n  }];\n}"}, "id": "parse-vision-result-kung-fu", "name": "Parse Vision Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2050, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Load Configuration", "type": "main", "index": 0}]]}, "Load Configuration": {"main": [[{"node": "Scan for Videos", "type": "main", "index": 0}]]}, "Scan for Videos": {"main": [[{"node": "Process File List", "type": "main", "index": 0}]]}, "Process File List": {"main": [[{"node": "Extract Video Thumbnail", "type": "main", "index": 0}]]}, "Extract Video Thumbnail": {"main": [[{"node": "Process Thumbnail", "type": "main", "index": 0}]]}, "Process Thumbnail": {"main": [[{"node": "Write Vision Request", "type": "main", "index": 0}]]}, "Write Vision Request": {"main": [[{"node": "Save Vision Request", "type": "main", "index": 0}]]}, "Save Vision Request": {"main": [[{"node": "Read Vision Result", "type": "main", "index": 0}]]}, "Read Vision Result": {"main": [[{"node": "Parse Vision Result", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}