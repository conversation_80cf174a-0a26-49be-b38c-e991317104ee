{"name": "Kung Fu Video Detector - Templating Test", "nodes": [{"parameters": {}, "id": "ed85e2e5-dc5e-4575-88eb-afd38180a7c8", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"jsCode": "// Create test data with file path\nreturn [{ json: { fullPath: '/home/<USER>/shared/kung_fu_videos/20250406_110016_1.mp4', filename: 'test.mp4' } }];"}, "id": "create-test-data", "name": "Create Test Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [500, 300]}, {"parameters": {"command": "=ffmpeg -i \"{{$json.fullPath}}\" -ss 00:00:10 -vframes 1 -f image2pipe -vcodec png - | wc -c"}, "id": "test-templating-v1", "name": "Test Templating V1", "type": "n8n-nodes-base.executeCommand", "typeVersion": 2, "position": [750, 300]}, {"parameters": {"command": "={{\"ffmpeg -i \\\"\" + $json.fullPath + \"\\\" -ss 00:00:10 -vframes 1 -f image2pipe -vcodec png - | wc -c\"}}"}, "id": "test-templating-v2", "name": "Test Templating V2", "type": "n8n-nodes-base.executeCommand", "typeVersion": 2, "position": [1000, 300]}, {"parameters": {"jsCode": "// Process results\nconst inputs = $input.all();\nconst results = inputs.map((input, index) => ({\n  test: `v${index + 1}`,\n  exitCode: input.json.exitCode,\n  success: input.json.exitCode === 0\n}));\n\nconsole.log('=== TEMPLATING TEST RESULTS ===');\nresults.forEach(r => console.log(`${r.test}: ${r.success ? 'SUCCESS' : 'FAILED'} (exit: ${r.exitCode})`));\n\nreturn [{ json: { results: results } }];"}, "id": "process-results", "name": "Process Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1250, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Create Test Data", "type": "main", "index": 0}]]}, "Create Test Data": {"main": [[{"node": "Test Templating V1", "type": "main", "index": 0}, {"node": "Test Templating V2", "type": "main", "index": 0}]]}, "Test Templating V1": {"main": [[{"node": "Process Results", "type": "main", "index": 0}]]}, "Test Templating V2": {"main": [[{"node": "Process Results", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}