#!/usr/bin/env python3
import json

print('🔧 Creating Immediate Logging Workflow')
print('=' * 50)

# Load current workflow
with open('kung_fu_video_workflow_with_logging.json', 'r', encoding='utf-8') as f:
    workflow = json.load(f)

print(f'✅ Current workflow loaded: {len(workflow["nodes"])} nodes')

# Add immediate logging after scan step
scan_log_node = {
    "parameters": {
        "jsCode": "// Log scan results immediately\nconst input = $input.first().json;\nconst timestamp = new Date().toISOString();\n\nconst logData = {\n  step: 'scan_folder_immediate',\n  timestamp: timestamp,\n  input_received: !!input,\n  stdout: input.stdout || 'no stdout',\n  stderr: input.stderr || 'no stderr',\n  error: input.error || null,\n  raw_data: input\n};\n\nconsole.log('=== IMMEDIATE SCAN LOG ===');\nconsole.log('Input received:', !!input);\nconsole.log('Stdout:', logData.stdout);\n\nreturn [{\n  json: {\n    data: JSON.stringify(logData, null, 2),\n    filename: 'immediate_scan_log.json'\n  }\n}];"
    },
    "id": "immediate-scan-log",
    "name": "Immediate Scan Log",
    "type": "n8n-nodes-base.code",
    "typeVersion": 2,
    "position": [-1400, 200]
}

scan_write_node = {
    "parameters": {
        "fileName": "={{ '/home/<USER>/shared/' + $json.filename }}",
        "options": {"overwrite": True}
    },
    "id": "write-immediate-scan-log", 
    "name": "Write Immediate Scan Log",
    "type": "n8n-nodes-base.writeBinaryFile",
    "typeVersion": 1,
    "position": [-1200, 200]
}

# Add nodes
workflow['nodes'].extend([scan_log_node, scan_write_node])

# Add connections
if 'Scan Folder for Videos' not in workflow['connections']:
    workflow['connections']['Scan Folder for Videos'] = {'main': [[]]}

workflow['connections']['Scan Folder for Videos']['main'][0].append({
    'node': 'Immediate Scan Log',
    'type': 'main',
    'index': 0
})

workflow['connections']['Immediate Scan Log'] = {
    'main': [[{'node': 'Write Immediate Scan Log', 'type': 'main', 'index': 0}]]
}

# Save
with open('kung_fu_video_workflow_immediate_logging.json', 'w', encoding='utf-8') as f:
    json.dump(workflow, f, indent=2, ensure_ascii=False)

print('✅ Created workflow with immediate logging')
print('   File: kung_fu_video_workflow_immediate_logging.json')
print('   Will create: immediate_scan_log.json')
print('   This will show exactly what the scan step produces!')
