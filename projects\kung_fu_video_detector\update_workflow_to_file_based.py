#!/usr/bin/env python3
"""
Update the kung fu workflow to use file-based vision analysis
while preserving all the original logging nodes
"""

import json
import uuid

def update_workflow_to_file_based():
    """Update the workflow to use file-based vision analysis"""
    
    # Read the original workflow
    with open('projects/kung_fu_video_detector/kung_fu_workflow_complete_file_based.json', 'r', encoding='utf-8') as f:
        workflow = json.load(f)
    
    print("🔧 Updating workflow to use file-based vision analysis...")
    
    # Find the HTTP Request node to replace
    http_node_id = None
    http_node_index = None
    
    for i, node in enumerate(workflow['nodes']):
        if node.get('name') == 'AI Video Analysis (Proxy)' and node.get('type') == 'n8n-nodes-base.httpRequest':
            http_node_id = node['id']
            http_node_index = i
            print(f"✅ Found HTTP Request node at index {i} with ID {http_node_id}")
            break
    
    if http_node_index is None:
        print("❌ Could not find HTTP Request node to replace")
        return False
    
    # Get the position of the original node
    original_position = workflow['nodes'][http_node_index]['position']
    
    # Create the file-based nodes
    file_based_nodes = [
        {
            "parameters": {
                "jsCode": "// Write Vision Request to Shared Folder (File-Based Method)\nconst input = $input.first().json;\nconst thumbnailBase64 = input.thumbnailBase64;\nconst filename = input.filename;\n\n// Skip if no thumbnail data\nif (!thumbnailBase64) {\n  console.log('Skipping vision analysis - no thumbnail data');\n  return [{\n    json: {\n      filename: filename,\n      success: false,\n      analysis_result: 'ERROR',\n      contains_kung_fu: false,\n      error: 'No thumbnail data available'\n    }\n  }];\n}\n\n// Generate unique request ID\nconst requestId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n\n// Prepare request data\nconst requestData = {\n  request_id: requestId,\n  filename: filename,\n  thumbnailBase64: thumbnailBase64,\n  prompt: \"Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training. Look for martial arts poses, fighting stances, training equipment, or combat movements. Please respond with only YES if it shows kung fu/martial arts practice, or NO if it does not.\",\n  created_at: new Date().toISOString()\n};\n\nconsole.log('=== WRITING VISION REQUEST (FILE-BASED) ===');\nconsole.log('Request ID:', requestId);\nconsole.log('Filename:', filename);\nconsole.log('Thumbnail length:', thumbnailBase64.length);\n\n// Prepare data for file writing\nconst jsonString = JSON.stringify(requestData, null, 2);\nconst binaryData = {\n  data: Buffer.from(jsonString, 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: `request_${requestId}.json`,\n  fileExtension: 'json'\n};\n\nreturn [{\n  json: {\n    request_id: requestId,\n    filename: filename,\n    request_file: `request_${requestId}.json`,\n    data_size: jsonString.length,\n    original_data: input\n  },\n  binary: {\n    data: binaryData\n  }\n}];"
            },
            "id": str(uuid.uuid4()),
            "name": "Write Vision Request (File-Based)",
            "type": "n8n-nodes-base.code",
            "typeVersion": 2,
            "position": original_position
        },
        {
            "parameters": {
                "fileName": "={{ '/home/<USER>/shared/vision_requests/' + $json.request_file }}",
                "options": {
                    "overwrite": True
                }
            },
            "id": str(uuid.uuid4()),
            "name": "Save Vision Request (File-Based)",
            "type": "n8n-nodes-base.writeBinaryFile",
            "typeVersion": 1,
            "position": [original_position[0] + 200, original_position[1]]
        },
        {
            "parameters": {
                "command": "=sleep 5 && if [ -f \"/home/<USER>/shared/vision_results/result_{{ $json.request_id }}.json\" ]; then cat \"/home/<USER>/shared/vision_results/result_{{ $json.request_id }}.json\"; else echo '{\"success\": false, \"error\": \"Result not found after 5 seconds\", \"request_id\": \"{{ $json.request_id }}\", \"filename\": \"{{ $json.filename }}\"}'; fi"
            },
            "id": str(uuid.uuid4()),
            "name": "Read Vision Result (File-Based)",
            "type": "n8n-nodes-base.executeCommand",
            "typeVersion": 1,
            "position": [original_position[0] + 400, original_position[1]]
        },
        {
            "parameters": {
                "jsCode": "// Parse Vision Result (File-Based Method)\nconst input = $input.first().json;\nconst stdout = input.stdout;\nconst originalData = input.original_data || {};\n\nconsole.log('=== PARSING VISION RESULT (FILE-BASED) ===');\nconsole.log('Raw stdout:', stdout);\n\ntry {\n  // Parse the JSON result\n  const result = JSON.parse(stdout);\n  \n  console.log('Parsed result:', result);\n  console.log('Success:', result.success);\n  console.log('Analysis result:', result.analysis_result);\n  \n  // Return in the same format as the original HTTP Request node would\n  // This ensures compatibility with existing logging nodes\n  const finalResult = {\n    choices: [{\n      message: {\n        content: result.full_response || `Analysis: ${result.analysis_result}`\n      }\n    }],\n    success: result.success || false,\n    filename: result.filename || originalData.filename || 'unknown',\n    analysis_result: result.analysis_result || 'NO',\n    contains_kung_fu: result.contains_kung_fu || false,\n    full_response: result.full_response || '',\n    model_used: result.model_used || 'unknown',\n    processed_at: result.processed_at || new Date().toISOString(),\n    request_id: result.request_id || 'unknown',\n    error: result.error || null\n  };\n  \n  console.log('=== FILE-BASED VISION ANALYSIS COMPLETE ===');\n  console.log('File:', finalResult.filename);\n  console.log('Contains Kung Fu:', finalResult.contains_kung_fu);\n  console.log('Analysis:', finalResult.analysis_result);\n  \n  return [{ json: finalResult }];\n  \n} catch (error) {\n  console.log('Error parsing result:', error.message);\n  \n  // Return error result in expected format\n  return [{\n    json: {\n      choices: [{ message: { content: `Error: ${error.message}` } }],\n      success: false,\n      filename: originalData.filename || 'unknown',\n      analysis_result: 'ERROR',\n      contains_kung_fu: false,\n      full_response: '',\n      error: `Failed to parse result: ${error.message}`,\n      raw_stdout: stdout,\n      processed_at: new Date().toISOString()\n    }\n  }];\n}"
            },
            "id": str(uuid.uuid4()),
            "name": "Parse Vision Result (File-Based)",
            "type": "n8n-nodes-base.code",
            "typeVersion": 2,
            "position": [original_position[0] + 600, original_position[1]]
        }
    ]
    
    # Replace the HTTP Request node with the file-based nodes
    workflow['nodes'][http_node_index:http_node_index+1] = file_based_nodes
    
    print(f"✅ Replaced 1 HTTP Request node with {len(file_based_nodes)} file-based nodes")
    
    # Update connections - find connections that point to the old HTTP node
    old_node_name = "AI Video Analysis (Proxy)"
    new_first_node_name = "Write Vision Request (File-Based)"
    new_last_node_name = "Parse Vision Result (File-Based)"
    
    # Update incoming connections to point to the first file-based node
    for node_name, connections in workflow['connections'].items():
        if 'main' in connections:
            for main_connection_group in connections['main']:
                if isinstance(main_connection_group, list):
                    for connection in main_connection_group:
                        if isinstance(connection, dict) and connection.get('node') == old_node_name:
                            connection['node'] = new_first_node_name
                            print(f"✅ Updated connection from {node_name} to {new_first_node_name}")
    
    # Add connections between the file-based nodes
    workflow['connections'][new_first_node_name] = {
        "main": [[{"node": "Save Vision Request (File-Based)", "type": "main", "index": 0}]]
    }
    workflow['connections']["Save Vision Request (File-Based)"] = {
        "main": [[{"node": "Read Vision Result (File-Based)", "type": "main", "index": 0}]]
    }
    workflow['connections']["Read Vision Result (File-Based)"] = {
        "main": [[{"node": new_last_node_name, "type": "main", "index": 0}]]
    }
    
    # Update outgoing connections from the old node to come from the last file-based node
    if old_node_name in workflow['connections']:
        workflow['connections'][new_last_node_name] = workflow['connections'][old_node_name]
        del workflow['connections'][old_node_name]
        print(f"✅ Moved outgoing connections from {old_node_name} to {new_last_node_name}")
    
    # Update the workflow name
    workflow['name'] = "Kung Fu Video Detection - Complete File-Based"
    
    # Save the updated workflow
    with open('projects/kung_fu_video_detector/kung_fu_workflow_complete_file_based.json', 'w', encoding='utf-8') as f:
        json.dump(workflow, f, indent=2)
    
    print("🎉 Successfully updated workflow to use file-based vision analysis!")
    print("✅ All original logging nodes preserved")
    print("✅ File-based vision analysis nodes added")
    print("✅ Connections updated correctly")
    
    return True

if __name__ == '__main__':
    success = update_workflow_to_file_based()
    if success:
        print("\n🚀 Ready to test the complete file-based kung fu workflow!")
    else:
        print("\n❌ Failed to update workflow")
