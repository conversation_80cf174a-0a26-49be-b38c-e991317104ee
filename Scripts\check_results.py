#!/usr/bin/env python3
"""
Video Processor Results Checker
Analyzes the results of the N8N Video Processor workflow
"""

import os
import json
import glob
import argparse
from datetime import datetime
from pathlib import Path

def load_json_safe(file_path):
    """Safely load JSON file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Warning: Failed to parse JSON {file_path}: {e}")
        return None

def get_latest_files(directory, pattern):
    """Get latest files matching pattern"""
    if not os.path.exists(directory):
        return []
    
    files = glob.glob(os.path.join(directory, pattern))
    return sorted(files, key=os.path.getmtime, reverse=True)

def check_notes_files(docker_share_path, detailed=False):
    """Check generated notes files"""
    print("=" * 80)
    print("1. NOTES FILES GENERATED")
    print("-" * 40)
    
    notes_path = os.path.join(docker_share_path, "notes")
    if not os.path.exists(notes_path):
        print("❌ ERROR: Notes folder not found")
        return 0
    
    notes_files = glob.glob(os.path.join(notes_path, "*.txt"))
    notes_files.sort()
    
    if notes_files:
        print(f"✅ SUCCESS: Found {len(notes_files)} notes files")
        
        for file_path in notes_files:
            filename = os.path.basename(file_path)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    line_count = len([line for line in content.split('\n') if line.strip()])
                    print(f"  - {filename}: {line_count} entries")
                    
                    if detailed and content.strip():
                        print("    Content preview:")
                        lines = [line for line in content.split('\n') if line.strip()][:3]
                        for line in lines:
                            print(f"      {line}")
                        if line_count > 3:
                            print(f"      ... and {line_count - 3} more entries")
                        print()
            except Exception as e:
                print(f"  - {filename}: Error reading file - {e}")
    else:
        print("⚠️  WARNING: No notes files found")
    
    print()
    return len(notes_files)

def check_processing_stats(docker_share_path):
    """Check processing statistics"""
    print("2. PROCESSING STATISTICS")
    print("-" * 40)
    
    logs_path = os.path.join(docker_share_path, "logs")
    summary_files = get_latest_files(logs_path, "notes_processing_summary_*.json")
    
    if summary_files:
        latest_summary = summary_files[0]
        summary = load_json_safe(latest_summary)
        
        if summary:
            filename = os.path.basename(latest_summary)
            print(f"📊 Latest Processing Summary: {filename}")
            print(f"  Total videos processed: {summary.get('total_videos_processed', 0)}")
            print(f"  Successful videos: {summary.get('successful_videos', 0)}")
            print(f"  Skipped videos: {summary.get('skipped_videos', 0)}")
            print(f"  Notes files created: {summary.get('notes_files_created', 0)}")
            print(f"  Processing success rate: {summary.get('processing_success_rate', 'N/A')}")
            
            skipped_details = summary.get('skipped_files_details', {})
            if skipped_details.get('count', 0) > 0:
                print("  Error breakdown:")
                error_summary = skipped_details.get('error_summary', {})
                for error_type, count in error_summary.items():
                    if count > 0:
                        print(f"    {error_type.replace('_', ' ').title()}: {count}")
    else:
        print("⚠️  No processing summary found")
    
    print()

def check_processor_status(docker_share_path):
    """Check individual processor status"""
    print("3. PROCESSOR STATUS")
    print("-" * 40)
    
    processors = [
        ("FFmpeg Processing", "ffmpeg_results"),
        ("Vision Analysis", "vision_results"),
        ("Description Analysis", "description_results")
    ]
    
    for name, folder in processors:
        results_path = os.path.join(docker_share_path, folder)
        if os.path.exists(results_path):
            result_files = glob.glob(os.path.join(results_path, "*.json"))
            success_count = 0
            failed_count = 0
            
            for file_path in result_files:
                result = load_json_safe(file_path)
                if result and result.get('success'):
                    success_count += 1
                else:
                    failed_count += 1
            
            status_icon = "✅" if failed_count == 0 else "⚠️"
            print(f"{status_icon} {name}: {success_count} successful, {failed_count} failed")
        else:
            print(f"❌ {name}: Results folder not found")
    
    print()

def main():
    parser = argparse.ArgumentParser(description='Check Video Processor Results')
    parser.add_argument('--docker-share', default='C:/Docker_Share/N8N', 
                       help='Docker share path (default: C:/Docker_Share/N8N)')
    parser.add_argument('--detailed', action='store_true', 
                       help='Show detailed information')
    parser.add_argument('--errors', action='store_true', 
                       help='Show recent errors')
    
    args = parser.parse_args()
    
    print("=" * 80)
    print("VIDEO PROCESSOR RESULTS ANALYSIS")
    print("=" * 80)
    print()
    
    if not os.path.exists(args.docker_share):
        print(f"❌ ERROR: Docker Share path not found: {args.docker_share}")
        return 1
    
    # Check results
    notes_count = check_notes_files(args.docker_share, args.detailed)
    check_processing_stats(args.docker_share)
    check_processor_status(args.docker_share)
    
    # Summary
    print("5. SUMMARY & RECOMMENDATIONS")
    print("-" * 40)
    
    if notes_count > 0:
        print("✅ SUCCESS: Workflow completed successfully!")
        print(f"Generated {notes_count} notes files with video descriptions.")
    else:
        print("⚠️  INCOMPLETE: Workflow ran but no notes files were generated.")
        print("Recommendations:")
        print("  1. Check if LM Studio model is loaded (localhost:1234)")
        print("  2. Verify all processors are running")
        print("  3. Check for errors with --errors flag")
    
    print()
    print("=" * 80)
    print("Analysis complete. Use --detailed for more info, --errors for error details.")
    print("=" * 80)
    
    return 0

if __name__ == "__main__":
    exit(main())
