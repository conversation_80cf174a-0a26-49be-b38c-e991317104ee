{"name": "Kung Fu Video Detector - Exact Copy", "nodes": [{"parameters": {}, "id": "ed85e2e5-dc5e-4575-88eb-afd38180a7c8", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"jsCode": "// Load configuration for kung fu video detection\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',\n  recursive_search: true,\n  file_extensions: ['.mp4', '.avi', '.mov', '.mkv'],\n  lm_studio: {\n    endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n    model: 'mimo-vl-7b-rl@q8_k_xl',\n    timeout: 30000,\n    temperature: 0.1,\n    max_tokens: 50\n  },\n  ai_prompt: 'Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training.',\n  ffmpeg_path: 'ffmpeg',\n  detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n};\n\nconsole.log('=== CONFIGURATION LOADED ===');\nconsole.log('Video folder:', config.video_folder);\nconsole.log('Extensions:', config.file_extensions);\nconsole.log('LM Studio endpoint:', config.lm_studio.endpoint);\n\nreturn [{ json: { config: config } }];"}, "id": "dee3c1e8-483c-4376-a78a-381b062af29f", "name": "Load Configuration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [500, 300]}, {"parameters": {"command": "find \"/home/<USER>/shared/kung_fu_videos\" -type f \\( -iname \"*.mp4\" -o -iname \"*.avi\" -o -iname \"*.mov\" -o -iname \"*.mkv\" \\) 2>/dev/null"}, "id": "05a21cf5-9a6b-4b45-a985-afad83287fac", "name": "<PERSON>an Folder for Videos", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [750, 300]}, {"parameters": {"jsCode": "// Process the find command output to create file list\nconst input = $input.first().json;\nconst commandOutput = input.stdout || '';\nconst exitCode = input.exitCode || 0;\n\nconsole.log('=== PROCESSING VIDEO FILE SCAN ===');\nconsole.log('Command output length:', commandOutput.length);\nconsole.log('Exit code:', exitCode);\n\nif (exitCode !== 0 || !commandOutput || commandOutput.trim() === '') {\n  console.log('No video files found');\n  return [{ json: { error: 'No video files found' } }];\n}\n\n// Split output into individual files\nconst fileLines = commandOutput.trim().split('\\n').filter(line => line.trim() !== '');\nconst videoFiles = [];\n\nfor (const line of fileLines) {\n  const cleanLine = line.trim();\n  if (cleanLine) {\n    const filename = cleanLine.split('/').pop();\n    videoFiles.push({\n      filename: filename,\n      fullPath: cleanLine\n    });\n  }\n}\n\nconsole.log(`Found ${videoFiles.length} video files`);\n\n// Return each file as a separate item\nreturn videoFiles.map(file => ({ json: file }));"}, "id": "c9b53082-af88-4093-960d-891dacb989a3", "name": "Process File List", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1000, 300]}, {"parameters": {"command": "ffmpeg -i \"{{ $json.fullPath }}\" -ss 00:00:10 -vframes 1 -f image2pipe -vcodec png -"}, "id": "53e8d6ae-acbc-4f48-ae64-3b7e8e876606", "name": "Extract Video Thumbnail", "type": "n8n-nodes-base.executeCommand", "typeVersion": 2, "position": [1250, 300]}, {"parameters": {"jsCode": "// Process FFmpeg thumbnail extraction\nconst input = $input.first().json;\nconst rawImageData = input.stdout || '';\nconst filename = input.filename;\nconst exitCode = input.exitCode || 0;\n\nconsole.log('=== PROCESSING THUMBNAIL ===');\nconsole.log(`File: ${filename}`);\nconsole.log(`Exit code: ${exitCode}`);\nconsole.log(`Raw data length: ${rawImageData.length}`);\n\nif (exitCode !== 0 || !rawImageData || rawImageData.length < 1000) {\n  console.log('Thumbnail extraction failed');\n  return [{ json: { filename: filename, error: 'Thumbnail extraction failed', success: false } }];\n}\n\n// Convert to base64\nconst thumbnailBase64 = Buffer.from(rawImageData, 'binary').toString('base64');\nconsole.log('Thumbnail extracted successfully');\n\nreturn [{ json: { filename: filename, thumbnailBase64: thumbnailBase64, success: true } }];"}, "id": "b2c3d4e5-f6g7-8901-bcde-f23456789012", "name": "Process Thumbnail", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1500, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Load Configuration", "type": "main", "index": 0}]]}, "Load Configuration": {"main": [[{"node": "<PERSON>an Folder for Videos", "type": "main", "index": 0}]]}, "Scan Folder for Videos": {"main": [[{"node": "Process File List", "type": "main", "index": 0}]]}, "Process File List": {"main": [[{"node": "Extract Video Thumbnail", "type": "main", "index": 0}]]}, "Extract Video Thumbnail": {"main": [[{"node": "Process Thumbnail", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}