#!/usr/bin/env python3
"""
Analyze the kung fu video detection workflow and identify the multi-item processing issue.
"""

import json
import sys
import os

def load_workflow():
    """Load the current workflow JSON."""
    workflow_path = os.path.join(os.path.dirname(__file__), 'kung_fu_workflow_complete_file_based.json')
    
    with open(workflow_path, 'r', encoding='utf-8') as f:
        return f.read()

def analyze_workflow():
    """Analyze the workflow using N8N validation and builder tools."""
    print("🔍 ANALYZING KUNG FU VIDEO DETECTION WORKFLOW")
    print("=" * 50)

    # Load workflow
    workflow_json = load_workflow()
    print(f"✅ Loaded workflow: {len(workflow_json)} characters")

    # Parse workflow data
    try:
        workflow_data = json.loads(workflow_json)
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON: {e}")
        return

    # Initialize validation service
    validator = ValidationService(config=DEFAULT_CONFIG)

    # 1. Validate workflow structure
    print("\n1️⃣ VALIDATING WORKFLOW STRUCTURE")
    print("-" * 30)

    try:
        validation_result = validator.validate_workflow(workflow_data)
        print("Validation Result:")
        print(f"  ✅ Valid: {validation_result.is_valid}")
        if validation_result.errors:
            print(f"  ❌ Errors: {len(validation_result.errors)}")
            for error in validation_result.errors[:3]:  # Show first 3 errors
                print(f"    - {error}")
        if validation_result.warnings:
            print(f"  ⚠️  Warnings: {len(validation_result.warnings)}")
            for warning in validation_result.warnings[:3]:  # Show first 3 warnings
                print(f"    - {warning}")
    except Exception as e:
        print(f"❌ Validation failed: {e}")

    # 2. Analyze workflow structure for multi-item processing
    print("\n2️⃣ ANALYZING MULTI-ITEM PROCESSING")
    print("-" * 35)

    try:
        analyze_multi_item_processing(workflow_data)
    except Exception as e:
        print(f"❌ Multi-item analysis failed: {e}")

    # 3. Identify the specific issue
    print("\n3️⃣ IDENTIFYING THE ISSUE")
    print("-" * 25)

    try:
        identify_processing_issue(workflow_data)
    except Exception as e:
        print(f"❌ Issue identification failed: {e}")

    # 4. Suggest solutions
    print("\n4️⃣ SOLUTION RECOMMENDATIONS")
    print("-" * 30)

    try:
        suggest_solutions(workflow_data)
    except Exception as e:
        print(f"❌ Solution suggestions failed: {e}")

def analyze_multi_item_processing(workflow_data):
    """Analyze the workflow for multi-item processing capabilities."""
    nodes = workflow_data.get('nodes', [])
    connections = workflow_data.get('connections', {})

    print("📊 Workflow Structure Analysis:")
    print(f"  - Total nodes: {len(nodes)}")
    print(f"  - Total connections: {len(connections)}")

    # Find key nodes
    process_file_list_node = None
    extract_thumbnail_node = None

    for node in nodes:
        if node.get('name') == 'Process File List':
            process_file_list_node = node
        elif node.get('name') == 'Extract Video Thumbnail':
            extract_thumbnail_node = node

    if process_file_list_node:
        print(f"  ✅ Found 'Process File List' node (ID: {process_file_list_node.get('id')})")
        # Check if it returns multiple items
        js_code = process_file_list_node.get('parameters', {}).get('jsCode', '')
        if 'return videoFiles.map' in js_code:
            print("  ✅ Node correctly returns multiple items using .map()")
        else:
            print("  ❌ Node may not be returning multiple items")

    if extract_thumbnail_node:
        print(f"  ✅ Found 'Extract Video Thumbnail' node (ID: {extract_thumbnail_node.get('id')})")
        node_type = extract_thumbnail_node.get('type')
        print(f"  - Node type: {node_type}")

        if node_type == 'n8n-nodes-base.executeCommand':
            command = extract_thumbnail_node.get('parameters', {}).get('command', '')
            if '20250406_110016_1.mp4' in command:
                print("  ❌ ISSUE FOUND: Hardcoded filename in FFmpeg command!")
                print(f"  - Command: {command[:100]}...")
            else:
                print("  ✅ Command appears to be dynamic")

def identify_processing_issue(workflow_data):
    """Identify the specific issue causing single-video processing."""
    print("🔍 Root Cause Analysis:")

    # Check the Extract Video Thumbnail node
    nodes = workflow_data.get('nodes', [])
    extract_node = None

    for node in nodes:
        if node.get('name') == 'Extract Video Thumbnail':
            extract_node = node
            break

    if extract_node:
        node_type = extract_node.get('type')
        if node_type == 'n8n-nodes-base.executeCommand':
            command = extract_node.get('parameters', {}).get('command', '')
            if '20250406_110016_1.mp4' in command:
                print("  🎯 ROOT CAUSE IDENTIFIED:")
                print("    - The 'Extract Video Thumbnail' node has a hardcoded FFmpeg command")
                print("    - It only processes one specific video file: '20250406_110016_1.mp4'")
                print("    - Even though 'Process File List' returns 5 items, only the first video gets processed")
                print("    - The hardcoded path ignores the dynamic filename from previous nodes")
                return True

    print("  ❓ Could not identify the specific issue")
    return False

def suggest_solutions(workflow_data):
    """Suggest specific solutions for the multi-item processing issue."""
    print("💡 RECOMMENDED SOLUTIONS:")

    print("\n🔧 SOLUTION 1: Loop-Based Processing (RECOMMENDED)")
    print("  - Add a 'SplitInBatches' node after 'Process File List'")
    print("  - This will process each video file individually in a loop")
    print("  - Each iteration gets one video file with its dynamic path")
    print("  - The FFmpeg command can then use the dynamic filename")
    print("  - Benefits: Simple, reliable, preserves existing logic")

    print("\n🔧 SOLUTION 2: Dynamic FFmpeg Command")
    print("  - Replace executeCommand node with Code node that builds dynamic commands")
    print("  - Use file-based execution (write script, execute, read result)")
    print("  - Benefits: More flexible, handles all videos in one execution")
    print("  - Complexity: Higher, requires file-based command execution")

    print("\n🎯 IMMEDIATE ACTION PLAN:")
    print("  1. Add 'SplitInBatches' node between 'Process File List' and 'Extract Video Thumbnail'")
    print("  2. Configure batch size = 1 (process one video at a time)")
    print("  3. Update FFmpeg command to use templating: {{ $json.fullPath }}")
    print("  4. Test with one video first, then verify all 5 videos are processed")

    print("\n📋 VALIDATION STEPS:")
    print("  1. Check execution logs show 5 separate processing cycles")
    print("  2. Verify 5 vision requests are created in shared folder")
    print("  3. Confirm 5 kung fu detection results are generated")

def main():
    """Main function."""
    try:
        analyze_workflow()
        print("\n✅ ANALYSIS COMPLETE")
        print("=" * 20)
        print("The root cause is identified: hardcoded FFmpeg command.")
        print("Recommended solution: Add SplitInBatches node for loop-based processing.")

    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
