#!/usr/bin/env python3
"""Create a properly connected workflow with ALL intended operations"""

import json
import sys
from pathlib import Path

def create_complete_workflow():
    """Create a complete workflow with all nodes properly connected."""
    print("🔧 CREATING COMPLETE CONNECTED WORKFLOW")
    print("=" * 45)
    
    try:
        # Load the current workflow to get all node definitions
        workflow_path = Path("kung_fu_video_workflow_connected.json")
        with open(workflow_path, 'r', encoding='utf-8') as f:
            workflow = json.load(f)
        
        print(f"✅ Current workflow loaded: {len(workflow['nodes'])} nodes")
        
        # Define the complete workflow flow
        complete_connections = {
            # Main workflow path
            "Manual Trigger": {
                "main": [[{"node": "Load Configuration", "type": "main", "index": 0}]]
            },
            "Load Configuration": {
                "main": [[
                    {"node": "Prepare Video Scan", "type": "main", "index": 0},
                    {"node": "Log 1 Config", "type": "main", "index": 0}
                ]]
            },
            "Prepare Video Scan": {
                "main": [[{"node": "Scan Folder for Videos", "type": "main", "index": 0}]]
            },
            "Scan Folder for Videos": {
                "main": [[
                    {"node": "Process File List", "type": "main", "index": 0},
                    {"node": "Immediate Scan Log", "type": "main", "index": 0},
                    {"node": "Log 2 Scan", "type": "main", "index": 0}
                ]]
            },
            "Process File List": {
                "main": [[
                    {"node": "Extract Video Thumbnail", "type": "main", "index": 0},
                    {"node": "Log Process File List", "type": "main", "index": 0},
                    {"node": "Log 3 Process", "type": "main", "index": 0}
                ]]
            },
            "Extract Video Thumbnail": {
                "main": [[
                    {"node": "Process Thumbnail", "type": "main", "index": 0},
                    {"node": "Log 4 Thumbnail", "type": "main", "index": 0}
                ]]
            },
            "Process Thumbnail": {
                "main": [[
                    {"node": "AI Video Analysis", "type": "main", "index": 0},
                    {"node": "Handle Errors", "type": "main", "index": 0}
                ]]
            },
            "AI Video Analysis": {
                "main": [[
                    {"node": "Process AI Response", "type": "main", "index": 0},
                    {"node": "Log 5 AI", "type": "main", "index": 0}
                ]]
            },
            "Process AI Response": {
                "main": [[{"node": "Filter Kung Fu Videos", "type": "main", "index": 0}]]
            },
            "Filter Kung Fu Videos": {
                "main": [[
                    {"node": "Collect Final Results", "type": "main", "index": 0}
                ]]
            },
            "Collect Final Results": {
                "main": [[{"node": "Create Execution Log", "type": "main", "index": 0}]]
            },
            "Create Execution Log": {
                "main": [[{"node": "Write Log to Shared Folder", "type": "main", "index": 0}]]
            },
            
            # Logging connections
            "Log 1 Config": {
                "main": [[{"node": "Write Log 1", "type": "main", "index": 0}]]
            },
            "Log 2 Scan": {
                "main": [[{"node": "Write Log 2", "type": "main", "index": 0}]]
            },
            "Log 3 Process": {
                "main": [[{"node": "Write Log 3", "type": "main", "index": 0}]]
            },
            "Log 4 Thumbnail": {
                "main": [[{"node": "Write Log 4", "type": "main", "index": 0}]]
            },
            "Log 5 AI": {
                "main": [[{"node": "Write Log 5", "type": "main", "index": 0}]]
            },
            "Immediate Scan Log": {
                "main": [[{"node": "Write Immediate Scan Log", "type": "main", "index": 0}]]
            },
            "Log Process File List": {
                "main": [[{"node": "Write Process Log", "type": "main", "index": 0}]]
            }
        }
        
        # Verify all nodes exist
        existing_node_names = [node['name'] for node in workflow['nodes']]
        
        # Clean connections to only include existing nodes
        cleaned_connections = {}
        missing_nodes = []
        
        for source, targets in complete_connections.items():
            if source in existing_node_names:
                valid_targets = []
                for target_list in targets["main"]:
                    valid_target_list = []
                    for target in target_list:
                        if target["node"] in existing_node_names:
                            valid_target_list.append(target)
                        else:
                            missing_nodes.append(target["node"])
                    if valid_target_list:
                        valid_targets.append(valid_target_list)
                
                if valid_targets:
                    cleaned_connections[source] = {"main": valid_targets}
            else:
                missing_nodes.append(source)
        
        if missing_nodes:
            print(f"⚠️  Missing nodes: {set(missing_nodes)}")
        
        # Update workflow connections
        workflow['connections'] = cleaned_connections
        
        # Save the complete workflow
        output_path = Path("kung_fu_video_workflow_complete.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(workflow, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Complete workflow saved: {output_path}")
        print(f"   Total nodes: {len(workflow['nodes'])}")
        print(f"   Connected nodes: {len(workflow['connections'])}")
        
        # Show the complete workflow path
        print(f"\n📋 COMPLETE WORKFLOW PATH:")
        print(f"   Manual Trigger → Load Configuration → Prepare Video Scan")
        print(f"   → Scan Folder → Process File List → Extract Thumbnail")
        print(f"   → Process Thumbnail → AI Analysis → Process AI Response")
        print(f"   → Filter Kung Fu Videos → Collect Final Results → Create Execution Log")
        
        # Show disconnected nodes (if any)
        connected_nodes = set()
        for source, targets in workflow['connections'].items():
            connected_nodes.add(source)
            for target_list in targets["main"]:
                for target in target_list:
                    connected_nodes.add(target["node"])
        
        disconnected_nodes = set(existing_node_names) - connected_nodes
        if disconnected_nodes:
            print(f"\n⚠️  STILL DISCONNECTED NODES:")
            for node in disconnected_nodes:
                print(f"     - {node}")
        else:
            print(f"\n✅ ALL NODES ARE NOW CONNECTED!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating complete workflow: {e}")
        return False

if __name__ == "__main__":
    success = create_complete_workflow()
    
    if success:
        print(f"\n🎉 SUCCESS!")
        print(f"   Import 'kung_fu_video_workflow_complete.json' into N8N")
        print(f"   This workflow includes ALL intended operations with proper connections")
    else:
        print(f"\n❌ Failed to create complete workflow")
    
    sys.exit(0 if success else 1)
