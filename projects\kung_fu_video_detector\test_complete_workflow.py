#!/usr/bin/env python3
"""
Test the complete kung fu video detection workflow.
This script validates the end-to-end detection system.
"""

import json
import subprocess
import time
import os
from datetime import datetime

def check_lm_studio():
    """Check if LM Studio is running and accessible."""
    print("=== LM STUDIO CHECK ===")
    
    try:
        import requests
        response = requests.get("http://localhost:1234/v1/models", timeout=5)
        
        if response.status_code == 200:
            models = response.json()
            print("✅ LM Studio is accessible")
            
            # Check for vision model
            model_names = [model.get('id', '') for model in models.get('data', [])]
            vision_models = [name for name in model_names if 'mimo' in name.lower() or 'vision' in name.lower()]
            
            if vision_models:
                print(f"✅ Vision model available: {vision_models[0]}")
                return True
            else:
                print("❌ No vision model found")
                print(f"Available models: {model_names}")
                return False
        else:
            print(f"❌ LM Studio not accessible: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Cannot connect to LM Studio: {e}")
        print("Make sure LM Studio is running on localhost:1234")
        return False

def test_ai_vision_endpoint():
    """Test the AI vision endpoint with a simple request."""
    print("\n=== AI VISION ENDPOINT TEST ===")
    
    try:
        import requests
        
        # Simple test payload
        test_payload = {
            "model": "mimo-vl-7b-rl@q8_k_xl",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "What do you see in this image?"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
                            }
                        }
                    ]
                }
            ],
            "temperature": 0.1,
            "max_tokens": 10
        }
        
        response = requests.post(
            "http://localhost:1234/v1/chat/completions",
            json=test_payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"✅ AI vision endpoint working: '{content}'")
            return True
        else:
            print(f"❌ AI vision endpoint failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing AI vision endpoint: {e}")
        return False

def run_workflow_test():
    """Run the complete workflow and analyze results."""
    print("\n=== WORKFLOW EXECUTION TEST ===")
    print("Please manually execute the workflow in N8N and then press Enter...")
    input("Press Enter after executing the workflow...")
    
    # Wait a moment for files to be written
    time.sleep(2)
    
    # Analyze results
    return analyze_workflow_results()

def analyze_workflow_results():
    """Analyze the workflow execution results."""
    print("\n=== WORKFLOW RESULTS ANALYSIS ===")
    
    try:
        # Get list of log files
        result = subprocess.run([
            'docker', 'exec', 'n8n', 'ls', '-la', '/home/<USER>/shared/*.json'
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print("❌ No log files found - workflow may not have executed")
            return False
        
        log_files = []
        for line in result.stdout.strip().split('\n'):
            if '.json' in line:
                parts = line.split()
                if len(parts) >= 9:
                    filename = parts[-1].split('/')[-1]
                    size = parts[4]
                    log_files.append((filename, size))
        
        print(f"Found {len(log_files)} log files:")
        
        # Check for expected log files
        expected_steps = [
            '1_config_log.json',
            '2_scan_log.json', 
            '4_ai_analysis_',
            '5_final_report.json'
        ]
        
        step_results = {}
        
        for filename, size in log_files:
            print(f"  📄 {filename} ({size} bytes)")
            
            # Read and analyze each log file
            cat_result = subprocess.run([
                'docker', 'exec', 'n8n', 'cat', f'/home/<USER>/shared/{filename}'
            ], capture_output=True, text=True)
            
            if cat_result.returncode == 0:
                try:
                    log_data = json.loads(cat_result.stdout)
                    step = log_data.get('step', 'unknown')
                    step_results[step] = log_data
                    
                    # Analyze specific steps
                    if step == '1_config_load':
                        print("    ✅ Configuration loaded successfully")
                        
                    elif step == '2_scan_success':
                        files_found = log_data.get('total_files_found', 0)
                        print(f"    ✅ Video scan successful: {files_found} files")
                        
                    elif step.startswith('4_ai_analysis'):
                        filename = log_data.get('filename', 'unknown')
                        ai_response = log_data.get('ai_response', '')
                        kung_fu_detected = log_data.get('kung_fu_detected', False)
                        print(f"    🤖 AI Analysis - {filename}: {'KUNG FU' if kung_fu_detected else 'NO KUNG FU'}")
                        print(f"        Response: '{ai_response}'")
                        
                    elif step == '5_final_report':
                        report = log_data.get('report', {})
                        total = report.get('total_videos_analyzed', 0)
                        kung_fu_count = report.get('kung_fu_videos_found', 0)
                        detection_rate = report.get('kung_fu_detection_rate', '0%')
                        print(f"    📊 Final Report:")
                        print(f"        Total videos: {total}")
                        print(f"        Kung Fu videos: {kung_fu_count}")
                        print(f"        Detection rate: {detection_rate}")
                        
                        # Show detected kung fu videos
                        kung_fu_videos = report.get('kung_fu_videos', [])
                        if kung_fu_videos:
                            print(f"        Detected in:")
                            for video in kung_fu_videos:
                                print(f"          - {video.get('filename', 'unknown')}")
                        
                except json.JSONDecodeError:
                    print(f"    ❌ Invalid JSON in {filename}")
        
        # Validate complete execution
        success_criteria = {
            'config_loaded': '1_config_load' in step_results,
            'videos_scanned': '2_scan_success' in step_results,
            'ai_analysis_done': any(step.startswith('4_ai_analysis') for step in step_results.keys()),
            'final_report_generated': '5_final_report' in step_results
        }
        
        print(f"\n=== SUCCESS CRITERIA ===")
        all_passed = True
        for criteria, passed in success_criteria.items():
            status = "✅" if passed else "❌"
            print(f"{status} {criteria.replace('_', ' ').title()}: {passed}")
            if not passed:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error analyzing workflow results: {e}")
        return False

def main():
    """Main function to test the complete workflow."""
    print("=== COMPLETE KUNG FU VIDEO DETECTOR TEST ===")
    print("This script validates the end-to-end detection system.")
    print()
    
    # Check prerequisites
    print("Checking prerequisites...")
    
    # Check LM Studio
    if not check_lm_studio():
        print("❌ LM Studio is required for AI analysis")
        print("Please start LM Studio and load a vision model")
        return
    
    # Test AI vision endpoint
    if not test_ai_vision_endpoint():
        print("❌ AI vision endpoint is not working")
        print("Please check LM Studio configuration")
        return
    
    print("\n✅ All prerequisites met!")
    print("\n=== WORKFLOW TESTING INSTRUCTIONS ===")
    print("1. Open N8N web interface: http://localhost:5678")
    print("2. Import workflow: kung_fu_video_workflow_complete.json")
    print("3. Verify all 11 nodes are connected:")
    print("   Start → Load Config → Write Config Log → Scan Videos →")
    print("   Process Files → Extract Thumbnail → Process Thumbnail →")
    print("   AI Analysis → Process AI Response → Generate Report → Write Final Report")
    print("4. Execute the workflow")
    print("5. Return here and press Enter")
    
    # Run workflow test
    success = run_workflow_test()
    
    print("\n=== TEST SUMMARY ===")
    if success:
        print("🎉 COMPLETE WORKFLOW TEST PASSED!")
        print("✅ All components working correctly:")
        print("   - Video file scanning")
        print("   - FFmpeg thumbnail extraction")
        print("   - AI vision analysis")
        print("   - Kung fu detection")
        print("   - Final report generation")
        print("\n📊 Check the final report in Docker shared folder:")
        print("   docker exec n8n cat /home/<USER>/shared/5_final_report.json")
    else:
        print("❌ COMPLETE WORKFLOW TEST FAILED!")
        print("Check the analysis above for specific issues")
        print("Common problems:")
        print("   - LM Studio not responding")
        print("   - Vision model not loaded")
        print("   - Network connectivity issues")
        print("   - Workflow execution errors")

if __name__ == "__main__":
    main()
