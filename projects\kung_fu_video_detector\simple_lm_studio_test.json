{"name": "Simple LM Studio Test", "nodes": [{"parameters": {}, "id": "manual-trigger-simple", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"method": "POST", "url": "http://host.docker.internal:1234/v1/chat/completions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyContentType": "json", "jsonBody": "{\"model\":\"mimo-vl-7b-rl@q8_k_xl\",\"messages\":[{\"role\":\"user\",\"content\":\"Hello, please respond with just OK\"}],\"temperature\":0.1,\"max_tokens\":5}", "options": {"timeout": 30000}}, "id": "simple-http-request", "name": "Simple HTTP Request", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [500, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Simple HTTP Request", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}