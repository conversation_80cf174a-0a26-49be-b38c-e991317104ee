#!/usr/bin/env python3
"""
Configuration script for the Kung Fu Video Detector workflow
Easily set up the video folder path and other settings
"""

import json
import os
from pathlib import Path

def get_current_config():
    """Load current configuration or create default."""
    config_path = Path("config.json")
    
    if config_path.exists():
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️  Error reading config: {e}")
    
    # Default configuration
    return {
        "video_folder": str(Path.cwd() / "test_videos"),
        "recursive_search": True,
        "file_extensions": [".mp4", ".avi", ".mov", ".mkv"],
        "lm_studio": {
            "endpoint": "http://localhost:1234/v1/chat/completions",
            "model": "mimo-vl-7b-rl@q8_k_xl",
            "timeout": 30000,
            "temperature": 0.1,
            "max_tokens": 50
        },
        "ai_prompt": "Analyze this video file and determine if it shows kung fu practice or martial arts training. The video file is: {filename}. Please respond with only 'YES' if it shows kung fu/martial arts practice, or 'NO' if it does not. Be specific about martial arts content.",
        "detection_keywords": [
            "yes", "kung fu", "martial arts", "karate", "taekwondo", 
            "fighting", "combat", "training", "practice"
        ]
    }

def validate_folder(folder_path):
    """Validate that the folder exists and is accessible."""
    path = Path(folder_path)
    
    if not path.exists():
        print(f"❌ Folder does not exist: {folder_path}")
        return False
    
    if not path.is_dir():
        print(f"❌ Path is not a directory: {folder_path}")
        return False
    
    try:
        # Test if we can list the directory
        list(path.iterdir())
        print(f"✅ Folder is accessible: {folder_path}")
        return True
    except PermissionError:
        print(f"❌ No permission to access folder: {folder_path}")
        return False

def count_video_files(folder_path, extensions, recursive=True):
    """Count video files in the folder."""
    path = Path(folder_path)
    count = 0
    
    try:
        if recursive:
            for ext in extensions:
                count += len(list(path.rglob(f"*{ext}")))
        else:
            for ext in extensions:
                count += len(list(path.glob(f"*{ext}")))
        
        return count
    except Exception as e:
        print(f"⚠️  Error counting files: {e}")
        return 0

def main():
    """Main configuration interface."""
    print("🛠️  Kung Fu Video Detector - Configuration")
    print("=" * 50)
    
    # Load current config
    config = get_current_config()
    
    print(f"\n📋 Current Configuration:")
    print(f"   Video Folder: {config['video_folder']}")
    print(f"   Recursive Search: {config['recursive_search']}")
    print(f"   File Extensions: {', '.join(config['file_extensions'])}")
    print(f"   LM Studio Model: {config['lm_studio']['model']}")
    
    # Validate current folder
    if validate_folder(config['video_folder']):
        video_count = count_video_files(
            config['video_folder'], 
            config['file_extensions'], 
            config['recursive_search']
        )
        print(f"   📹 Video files found: {video_count}")
    
    print(f"\n🔧 Configuration Options:")
    print(f"1. Change video folder path")
    print(f"2. Toggle recursive search (currently: {config['recursive_search']})")
    print(f"3. Modify file extensions")
    print(f"4. Update LM Studio settings")
    print(f"5. Test current configuration")
    print(f"6. Save and exit")
    print(f"0. Exit without saving")
    
    while True:
        try:
            choice = input(f"\nSelect option (0-6): ").strip()
            
            if choice == "0":
                print("Exiting without saving changes.")
                return
            
            elif choice == "1":
                print(f"\nCurrent folder: {config['video_folder']}")
                new_folder = input("Enter new video folder path: ").strip()
                
                if new_folder:
                    # Convert to absolute path
                    new_path = Path(new_folder).resolve()
                    if validate_folder(new_path):
                        config['video_folder'] = str(new_path)
                        video_count = count_video_files(
                            config['video_folder'], 
                            config['file_extensions'], 
                            config['recursive_search']
                        )
                        print(f"✅ Updated folder path. Found {video_count} video files.")
                    else:
                        print("❌ Invalid folder path. Not updated.")
            
            elif choice == "2":
                config['recursive_search'] = not config['recursive_search']
                print(f"✅ Recursive search set to: {config['recursive_search']}")
                
                # Recount files with new setting
                if validate_folder(config['video_folder']):
                    video_count = count_video_files(
                        config['video_folder'], 
                        config['file_extensions'], 
                        config['recursive_search']
                    )
                    print(f"   📹 Video files found: {video_count}")
            
            elif choice == "3":
                print(f"\nCurrent extensions: {', '.join(config['file_extensions'])}")
                new_extensions = input("Enter extensions (comma-separated, e.g., .mp4,.avi,.mov): ").strip()
                
                if new_extensions:
                    extensions = [ext.strip() for ext in new_extensions.split(',')]
                    # Ensure extensions start with dot
                    extensions = [ext if ext.startswith('.') else f'.{ext}' for ext in extensions]
                    config['file_extensions'] = extensions
                    print(f"✅ Updated extensions: {', '.join(extensions)}")
            
            elif choice == "4":
                print(f"\nLM Studio Settings:")
                print(f"   Endpoint: {config['lm_studio']['endpoint']}")
                print(f"   Model: {config['lm_studio']['model']}")
                print(f"   Timeout: {config['lm_studio']['timeout']}ms")
                
                new_endpoint = input(f"New endpoint (Enter to keep current): ").strip()
                if new_endpoint:
                    config['lm_studio']['endpoint'] = new_endpoint
                
                new_model = input(f"New model name (Enter to keep current): ").strip()
                if new_model:
                    config['lm_studio']['model'] = new_model
                
                print("✅ LM Studio settings updated.")
            
            elif choice == "5":
                print(f"\n🧪 Testing Configuration:")
                print(f"   Folder: {config['video_folder']}")
                
                if validate_folder(config['video_folder']):
                    video_count = count_video_files(
                        config['video_folder'], 
                        config['file_extensions'], 
                        config['recursive_search']
                    )
                    print(f"   ✅ Found {video_count} video files")
                    
                    if video_count == 0:
                        print(f"   ⚠️  No video files found. Check folder path and extensions.")
                    else:
                        print(f"   🎯 Configuration looks good!")
                else:
                    print(f"   ❌ Folder validation failed.")
            
            elif choice == "6":
                # Save configuration
                try:
                    with open("config.json", 'w', encoding='utf-8') as f:
                        json.dump(config, f, indent=2, ensure_ascii=False)
                    
                    print(f"\n✅ Configuration saved to config.json")
                    print(f"\n📋 Final Configuration:")
                    print(f"   Video Folder: {config['video_folder']}")
                    print(f"   Recursive Search: {config['recursive_search']}")
                    print(f"   File Extensions: {', '.join(config['file_extensions'])}")
                    
                    if validate_folder(config['video_folder']):
                        video_count = count_video_files(
                            config['video_folder'], 
                            config['file_extensions'], 
                            config['recursive_search']
                        )
                        print(f"   📹 Video files ready: {video_count}")
                    
                    print(f"\n🚀 Next steps:")
                    print(f"   1. Make sure LM Studio is running with your model")
                    print(f"   2. Import the workflow JSON into N8N")
                    print(f"   3. Run the workflow to detect kung fu videos!")
                    
                    return
                
                except Exception as e:
                    print(f"❌ Error saving configuration: {e}")
            
            else:
                print("Invalid option. Please select 0-6.")
        
        except KeyboardInterrupt:
            print(f"\n\nExiting...")
            return
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()
