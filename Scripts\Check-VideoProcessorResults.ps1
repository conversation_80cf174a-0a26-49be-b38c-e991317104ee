# Check Video Processor Results Script
# Analyzes the results of the N8N Video Processor workflow
# Shows processing status, success rates, and generated notes files

param(
    [string]$DockerSharePath = "C:\Docker_Share\N8N",
    [switch]$Detailed,
    [switch]$ShowErrors,
    [switch]$OpenNotes
)

Write-Host "=" * 80 -ForegroundColor Cyan
Write-Host "VIDEO PROCESSOR RESULTS ANALYSIS" -ForegroundColor Cyan
Write-Host "=" * 80 -ForegroundColor Cyan
Write-Host ""

# Check if Docker Share exists
if (!(Test-Path $DockerSharePath)) {
    Write-Host "ERROR: Docker Share path not found: $DockerSharePath" -ForegroundColor Red
    exit 1
}

# Function to get latest files by pattern
function Get-LatestFiles {
    param([string]$Path, [string]$Pattern)
    if (Test-Path $Path) {
        Get-ChildItem -Path $Path -Filter $Pattern | Sort-Object LastWriteTime -Descending
    }
}

# Function to analyze JSON file
function Get-JsonContent {
    param([string]$FilePath)
    try {
        if (Test-Path $FilePath) {
            return Get-Content $FilePath -Raw | ConvertFrom-Json
        }
    }
    catch {
        Write-Warning "Failed to parse JSON: $FilePath"
        return $null
    }
}

# 1. CHECK NOTES FILES (Primary Output)
Write-Host "1. NOTES FILES GENERATED" -ForegroundColor Yellow
Write-Host "-" * 40 -ForegroundColor Yellow

$notesPath = Join-Path $DockerSharePath "notes"
if (Test-Path $notesPath) {
    $notesFiles = Get-ChildItem -Path $notesPath -Filter "*.txt" | Sort-Object Name
    
    if ($notesFiles.Count -gt 0) {
        Write-Host "SUCCESS: Found $($notesFiles.Count) notes files" -ForegroundColor Green
        
        foreach ($file in $notesFiles) {
            $content = Get-Content $file.FullName -Raw
            $lineCount = ($content -split "`n").Count
            Write-Host "  - $($file.Name): $lineCount entries" -ForegroundColor Green
            
            if ($Detailed) {
                Write-Host "    Content preview:" -ForegroundColor Gray
                $lines = $content -split "`n" | Select-Object -First 3
                foreach ($line in $lines) {
                    if ($line.Trim()) {
                        Write-Host "      $line" -ForegroundColor Gray
                    }
                }
                if ($lineCount -gt 3) {
                    Write-Host "      ... and $($lineCount - 3) more entries" -ForegroundColor Gray
                }
                Write-Host ""
            }
        }
        
        if ($OpenNotes) {
            Write-Host "Opening notes files..." -ForegroundColor Cyan
            foreach ($file in $notesFiles) {
                Start-Process notepad.exe -ArgumentList $file.FullName
            }
        }
    } else {
        Write-Host "WARNING: No notes files found" -ForegroundColor Yellow
    }
} else {
    Write-Host "ERROR: Notes folder not found" -ForegroundColor Red
}

Write-Host ""

# 2. CHECK PROCESSING LOGS
Write-Host "2. PROCESSING STATISTICS" -ForegroundColor Yellow
Write-Host "-" * 40 -ForegroundColor Yellow

$logsPath = Join-Path $DockerSharePath "logs"
$latestSummary = Get-LatestFiles -Path $logsPath -Pattern "notes_processing_summary_*.json" | Select-Object -First 1

if ($latestSummary) {
    $summary = Get-JsonContent -FilePath $latestSummary.FullName
    if ($summary) {
        Write-Host "Latest Processing Summary: $($latestSummary.Name)" -ForegroundColor Cyan
        Write-Host "  Total videos processed: $($summary.total_videos_processed)" -ForegroundColor White
        Write-Host "  Successful videos: $($summary.successful_videos)" -ForegroundColor Green
        Write-Host "  Skipped videos: $($summary.skipped_videos)" -ForegroundColor Yellow
        Write-Host "  Notes files created: $($summary.notes_files_created)" -ForegroundColor Green
        Write-Host "  Processing success rate: $($summary.processing_success_rate)" -ForegroundColor Cyan
        
        if ($summary.skipped_files_details -and $summary.skipped_files_details.count -gt 0) {
            Write-Host "  Error breakdown:" -ForegroundColor Yellow
            $errors = $summary.skipped_files_details.error_summary
            if ($errors.ffmpeg_failures -gt 0) { Write-Host "    FFmpeg failures: $($errors.ffmpeg_failures)" -ForegroundColor Red }
            if ($errors.vision_failures -gt 0) { Write-Host "    Vision failures: $($errors.vision_failures)" -ForegroundColor Red }
            if ($errors.description_failures -gt 0) { Write-Host "    Description failures: $($errors.description_failures)" -ForegroundColor Red }
            if ($errors.other_failures -gt 0) { Write-Host "    Other failures: $($errors.other_failures)" -ForegroundColor Red }
        }
    }
} else {
    Write-Host "No processing summary found" -ForegroundColor Yellow
}

Write-Host ""

# 3. CHECK INDIVIDUAL PROCESSOR STATUS
Write-Host "3. PROCESSOR STATUS" -ForegroundColor Yellow
Write-Host "-" * 40 -ForegroundColor Yellow

# FFmpeg Results
$ffmpegResults = Join-Path $DockerSharePath "ffmpeg_results"
if (Test-Path $ffmpegResults) {
    $ffmpegFiles = Get-ChildItem -Path $ffmpegResults -Filter "*.json"
    $ffmpegSuccess = 0
    $ffmpegFailed = 0
    
    foreach ($file in $ffmpegFiles) {
        $result = Get-JsonContent -FilePath $file.FullName
        if ($result -and $result.success) {
            $ffmpegSuccess++
        } else {
            $ffmpegFailed++
        }
    }
    
    Write-Host "FFmpeg Processing: $ffmpegSuccess successful, $ffmpegFailed failed" -ForegroundColor $(if ($ffmpegFailed -eq 0) { "Green" } else { "Yellow" })
}

# Vision Results
$visionResults = Join-Path $DockerSharePath "vision_results"
if (Test-Path $visionResults) {
    $visionFiles = Get-ChildItem -Path $visionResults -Filter "*.json"
    $visionSuccess = 0
    $visionFailed = 0
    
    foreach ($file in $visionFiles) {
        $result = Get-JsonContent -FilePath $file.FullName
        if ($result -and $result.success) {
            $visionSuccess++
        } else {
            $visionFailed++
        }
    }
    
    Write-Host "Vision Analysis: $visionSuccess successful, $visionFailed failed" -ForegroundColor $(if ($visionFailed -eq 0) { "Green" } else { "Yellow" })
}

# Description Results
$descResults = Join-Path $DockerSharePath "description_results"
if (Test-Path $descResults) {
    $descFiles = Get-ChildItem -Path $descResults -Filter "*.json"
    $descSuccess = 0
    $descFailed = 0
    
    foreach ($file in $descFiles) {
        $result = Get-JsonContent -FilePath $file.FullName
        if ($result -and $result.success) {
            $descSuccess++
        } else {
            $descFailed++
        }
    }
    
    Write-Host "Description Analysis: $descSuccess successful, $descFailed failed" -ForegroundColor $(if ($descFailed -eq 0) { "Green" } else { "Yellow" })
}

Write-Host ""

# 4. CHECK FOR RECENT ERRORS
if ($ShowErrors) {
    Write-Host "4. RECENT ERRORS" -ForegroundColor Yellow
    Write-Host "-" * 40 -ForegroundColor Yellow
    
    # Check vision results for errors
    if (Test-Path $visionResults) {
        $recentVisionErrors = Get-LatestFiles -Path $visionResults -Pattern "*.json" | Select-Object -First 3
        foreach ($file in $recentVisionErrors) {
            $result = Get-JsonContent -FilePath $file.FullName
            if ($result -and !$result.success) {
                Write-Host "Vision Error in $($file.Name):" -ForegroundColor Red
                Write-Host "  $($result.error)" -ForegroundColor Red
            }
        }
    }
    
    # Check description results for errors
    if (Test-Path $descResults) {
        $recentDescErrors = Get-LatestFiles -Path $descResults -Pattern "*.json" | Select-Object -First 3
        foreach ($file in $recentDescErrors) {
            $result = Get-JsonContent -FilePath $file.FullName
            if ($result -and !$result.success) {
                Write-Host "Description Error in $($file.Name):" -ForegroundColor Red
                Write-Host "  $($result.error)" -ForegroundColor Red
            }
        }
    }
}

# 5. SUMMARY AND RECOMMENDATIONS
Write-Host "5. SUMMARY & RECOMMENDATIONS" -ForegroundColor Yellow
Write-Host "-" * 40 -ForegroundColor Yellow

if ($notesFiles -and $notesFiles.Count -gt 0) {
    Write-Host "SUCCESS: Workflow completed successfully!" -ForegroundColor Green
    Write-Host "Generated $($notesFiles.Count) notes files with video descriptions." -ForegroundColor Green
} else {
    Write-Host "INCOMPLETE: Workflow ran but no notes files were generated." -ForegroundColor Yellow
    Write-Host "Recommendations:" -ForegroundColor Yellow
    Write-Host "  1. Check if LM Studio model is loaded (localhost:1234)" -ForegroundColor Yellow
    Write-Host "  2. Verify all processors are running" -ForegroundColor Yellow
    Write-Host "  3. Check for errors with -ShowErrors flag" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=" * 80 -ForegroundColor Cyan
Write-Host "Analysis complete. Use -Detailed for more info, -ShowErrors for error details, -OpenNotes to view files." -ForegroundColor Cyan
Write-Host "=" * 80 -ForegroundColor Cyan
