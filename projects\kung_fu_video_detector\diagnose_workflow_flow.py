#!/usr/bin/env python3
"""Diagnose why workflow stops after Process File List"""

import json
from pathlib import Path

def diagnose_workflow_flow():
    """Analyze the workflow execution flow."""
    print("🔍 DIAGNOSING WORKFLOW EXECUTION FLOW")
    print("=" * 50)
    
    # Check the timestamps of all log files
    log_files = [
        ("1_config_log.json", "Load Configuration"),
        ("2_scan_log.json", "Scan Folder for Videos"), 
        ("3_process_log.json", "Process File List"),
        ("4_thumbnail_log.json", "Extract Video Thumbnail"),
        ("5_ai_log.json", "AI Video Analysis")
    ]
    
    log_dir = Path("C:/Docker_Share/N8N/")
    recent_logs = []
    old_logs = []
    
    for log_file, step_name in log_files:
        log_path = log_dir / log_file
        if log_path.exists():
            stat = log_path.stat()
            # Check if file was modified in the last hour (recent execution)
            import time
            current_time = time.time()
            file_time = stat.st_mtime
            age_minutes = (current_time - file_time) / 60
            
            if age_minutes < 60:  # Less than 1 hour old
                recent_logs.append((log_file, step_name, age_minutes))
            else:
                old_logs.append((log_file, step_name, age_minutes))
        else:
            print(f"❌ {log_file} not found")
    
    print("📋 RECENT LOGS (from latest execution):")
    for log_file, step_name, age in recent_logs:
        print(f"✅ {step_name}: {log_file} ({age:.1f} minutes ago)")
    
    print(f"\n📋 OLD LOGS (from previous executions):")
    for log_file, step_name, age in old_logs:
        print(f"⏰ {step_name}: {log_file} ({age:.1f} minutes ago)")
    
    # Analyze the data flow
    print(f"\n🔍 DATA FLOW ANALYSIS:")
    print("-" * 30)
    
    # Check step 3 (Process File List) output
    process_log_path = log_dir / "3_process_log.json"
    if process_log_path.exists() and process_log_path in [Path(log_dir / f[0]) for f in recent_logs]:
        try:
            with open(process_log_path, 'r') as f:
                process_data = json.load(f)
            
            total_files = process_data.get('total_inputs', 0)
            files = process_data.get('files', [])
            
            print(f"Step 3 (Process File List):")
            print(f"  - Total files processed: {total_files}")
            print(f"  - Files with valid paths: {len([f for f in files if f.get('fullPath')])}")
            print(f"  - Files with config: {len([f for f in files if f.get('hasConfig')])}")
            print(f"  - Files with errors: {len([f for f in files if f.get('error')])}")
            
            if total_files > 1:
                print(f"  ✅ Multiple files processed successfully")
            else:
                print(f"  ⚠️  Only {total_files} file processed")
                
        except Exception as e:
            print(f"❌ Error reading process log: {e}")
    
    # Check if step 4 executed recently
    thumbnail_log_path = log_dir / "4_thumbnail_log.json"
    step_4_recent = thumbnail_log_path in [Path(log_dir / f[0]) for f in recent_logs]
    
    if not step_4_recent:
        print(f"\n🚨 ISSUE IDENTIFIED:")
        print(f"  - Step 3 (Process File List) completed successfully")
        print(f"  - Step 4 (Extract Video Thumbnail) did NOT execute in recent run")
        print(f"  - This suggests a workflow connection or data flow issue")
        
        print(f"\n🔧 POSSIBLE CAUSES:")
        print(f"  1. N8N only processing first item from Process File List")
        print(f"  2. Workflow connection issue between steps 3 and 4")
        print(f"  3. Data format issue preventing step 4 execution")
        print(f"  4. N8N workflow execution stopping after logging nodes")
    
    # Check for any error patterns
    print(f"\n📊 EXECUTION PATTERN:")
    if len(recent_logs) == 3 and len(old_logs) == 2:
        print(f"  - Steps 1-3: Recent execution ✅")
        print(f"  - Steps 4-5: Old execution (not reached) ❌")
        print(f"  - Pattern: Workflow stops after step 3")
    elif len(recent_logs) == 5:
        print(f"  - All steps: Recent execution ✅")
        print(f"  - Pattern: Full workflow execution")
    else:
        print(f"  - Mixed execution pattern - needs investigation")

def suggest_solutions():
    """Suggest solutions based on the diagnosis."""
    print(f"\n🚀 SUGGESTED SOLUTIONS:")
    print("-" * 25)
    
    print("1. **Check N8N Workflow Execution:**")
    print("   - Look at N8N UI execution log")
    print("   - Check if step 4 shows any errors")
    print("   - Verify data is flowing from step 3 to step 4")
    print()
    
    print("2. **Single File Processing Issue:**")
    print("   - N8N might only process first item from multi-item output")
    print("   - Check if Extract Video Thumbnail receives all 5 files")
    print("   - May need to adjust workflow design for parallel processing")
    print()
    
    print("3. **Logging Node Interference:**")
    print("   - Multiple logging nodes might be interfering with main flow")
    print("   - Try temporarily disabling logging nodes")
    print("   - Focus on main workflow path first")

if __name__ == "__main__":
    diagnose_workflow_flow()
    suggest_solutions()
