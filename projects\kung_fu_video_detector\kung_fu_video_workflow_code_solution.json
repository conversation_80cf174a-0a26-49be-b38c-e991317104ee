{"name": "Kung Fu Video Detector - Code Solution", "nodes": [{"parameters": {}, "id": "ed85e2e5-dc5e-4575-88eb-afd38180a7c8", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"jsCode": "// Load configuration\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',\n  file_extensions: ['.mp4', '.avi', '.mov', '.mkv']\n};\n\nreturn [{ json: { config: config } }];"}, "id": "load-config", "name": "Load Configuration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [500, 300]}, {"parameters": {"command": "find /home/<USER>/shared/kung_fu_videos -type f \\( -iname \"*.mp4\" -o -iname \"*.avi\" -o -iname \"*.mov\" -o -iname \"*.mkv\" \\)"}, "id": "scan-videos", "name": "Scan Videos", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [750, 300]}, {"parameters": {"jsCode": "// Process file list and create FFmpeg commands\nconst input = $input.first().json;\nconst commandOutput = input.stdout || '';\nconst exitCode = input.exitCode || 0;\n\nconsole.log('=== PROCESSING VIDEO FILES ===');\nconsole.log('Exit code:', exitCode);\n\nif (exitCode !== 0 || !commandOutput || commandOutput.trim() === '') {\n  console.log('No video files found');\n  return [{ json: { error: 'No video files found' } }];\n}\n\n// Split output into individual files and create FFmpeg commands\nconst fileLines = commandOutput.trim().split('\\n').filter(line => line.trim() !== '');\nconst videoCommands = [];\n\nfor (const line of fileLines) {\n  const cleanPath = line.trim();\n  if (cleanPath) {\n    const filename = cleanPath.split('/').pop();\n    const ffmpegCommand = `ffmpeg -i \"${cleanPath}\" -ss 00:00:10 -vframes 1 -f image2pipe -vcodec png -`;\n    \n    videoCommands.push({\n      filename: filename,\n      fullPath: cleanPath,\n      ffmpegCommand: ffmpegCommand\n    });\n  }\n}\n\nconsole.log(`Created ${videoCommands.length} FFmpeg commands`);\n\n// Return each command as a separate item\nreturn videoCommands.map(cmd => ({ json: cmd }));"}, "id": "create-commands", "name": "Create FFmpeg Commands", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1000, 300]}, {"parameters": {"jsCode": "// Execute FFmpeg command using child_process alternative\nconst input = $input.first().json;\nconst ffmpegCommand = input.ffmpegCommand;\nconst filename = input.filename;\n\nconsole.log('=== EXECUTING FFMPEG ===');\nconsole.log(`File: ${filename}`);\nconsole.log(`Command: ${ffmpegCommand}`);\n\n// Since we can't use child_process in code nodes, we'll prepare the command\n// for the next executeCommand node\nreturn [{ json: { \n  filename: filename,\n  command: ffmpegCommand,\n  ready: true\n} }];"}, "id": "prepare-ffmpeg", "name": "Prepare FFmpeg", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1250, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Load Configuration", "type": "main", "index": 0}]]}, "Load Configuration": {"main": [[{"node": "Scan Videos", "type": "main", "index": 0}]]}, "Scan Videos": {"main": [[{"node": "Create FFmpeg Commands", "type": "main", "index": 0}]]}, "Create FFmpeg Commands": {"main": [[{"node": "Prepare FFmpeg", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}