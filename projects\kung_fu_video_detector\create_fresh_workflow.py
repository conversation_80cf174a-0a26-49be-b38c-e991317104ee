#!/usr/bin/env python3
"""Create a completely fresh workflow from scratch to eliminate connection issues"""

import json
import sys
from pathlib import Path

def create_fresh_minimal_workflow():
    """Create a brand new minimal workflow with guaranteed connections."""
    print("🔧 CREATING FRESH MINIMAL WORKFLOW FROM SCRATCH")
    print("=" * 50)
    
    # Define the workflow structure from scratch
    fresh_workflow = {
        "name": "Kung Fu Video Detector - Fresh Minimal",
        "nodes": [
            {
                "parameters": {},
                "id": "manual-trigger-1",
                "name": "Start",
                "type": "n8n-nodes-base.manualTrigger",
                "typeVersion": 1,
                "position": [0, 0]
            },
            {
                "parameters": {
                    "jsCode": "// Load configuration for kung fu video detection\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',\n  recursive_search: true,\n  file_extensions: ['.mp4', '.avi', '.mov', '.mkv'],\n  lm_studio: {\n    endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n    model: 'mimo-vl-7b-rl@q8_k_xl',\n    timeout: 30000,\n    temperature: 0.1,\n    max_tokens: 50\n  },\n  ai_prompt: 'Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training.',\n  ffmpeg_path: 'ffmpeg',\n  detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n};\n\nconsole.log('=== CONFIGURATION LOADED ===');\nconsole.log('Video folder:', config.video_folder);\nconsole.log('Extensions:', config.file_extensions);\nconsole.log('LM Studio endpoint:', config.lm_studio.endpoint);\n\nreturn [{ json: { config: config } }];"
                },
                "id": "load-config-2",
                "name": "Load Config",
                "type": "n8n-nodes-base.code",
                "typeVersion": 2,
                "position": [200, 0]
            },
            {
                "parameters": {
                    "command": "find \"/home/<USER>/shared/kung_fu_videos\" -type f \\( -iname \"*.mp4\" -o -iname \"*.avi\" -o -iname \"*.mov\" -o -iname \"*.mkv\" \\)"
                },
                "id": "scan-videos-3",
                "name": "Scan Videos",
                "type": "n8n-nodes-base.executeCommand",
                "typeVersion": 2,
                "position": [400, 0]
            },
            {
                "parameters": {
                    "jsCode": "// Process the find command output to create file list\nconst input = $input.first().json;\nconst commandOutput = input.stdout || '';\nconst exitCode = input.exitCode || 0;\n\nconsole.log('=== PROCESSING VIDEO FILE SCAN ===');\nconsole.log('Command output length:', commandOutput.length);\nconsole.log('Exit code:', exitCode);\n\nif (exitCode !== 0 || !commandOutput || commandOutput.trim() === '') {\n  console.log('❌ No video files found');\n  return [{ json: { error: 'No video files found' } }];\n}\n\n// Split output into individual files\nconst fileLines = commandOutput.trim().split('\\n').filter(line => line.trim() !== '');\nconst videoFiles = [];\n\nfor (const line of fileLines) {\n  const cleanLine = line.trim();\n  if (cleanLine) {\n    const filename = cleanLine.split('/').pop();\n    videoFiles.push({\n      filename: filename,\n      fullPath: cleanLine\n    });\n  }\n}\n\nconsole.log(`Found ${videoFiles.length} video files`);\n\n// Return each file as a separate item\nreturn videoFiles.map(file => ({ json: file }));"
                },
                "id": "process-files-4",
                "name": "Process Files",
                "type": "n8n-nodes-base.code",
                "typeVersion": 2,
                "position": [600, 0]
            },
            {
                "parameters": {
                    "command": "ffmpeg -i \"{{ $json.fullPath }}\" -ss 00:00:10 -vframes 1 -f image2pipe -vcodec png -"
                },
                "id": "extract-thumbnail-5",
                "name": "Extract Thumbnail",
                "type": "n8n-nodes-base.executeCommand",
                "typeVersion": 2,
                "position": [800, 0]
            },
            {
                "parameters": {
                    "jsCode": "// Process FFmpeg thumbnail extraction\nconst input = $input.first().json;\nconst rawImageData = input.stdout || '';\nconst filename = input.filename;\nconst exitCode = input.exitCode || 0;\n\nconsole.log('=== PROCESSING THUMBNAIL ===');\nconsole.log(`File: ${filename}`);\nconsole.log(`Exit code: ${exitCode}`);\nconsole.log(`Raw data length: ${rawImageData.length}`);\n\nif (exitCode !== 0 || !rawImageData || rawImageData.length < 1000) {\n  console.log('❌ Thumbnail extraction failed');\n  return [{ json: { filename: filename, error: 'Thumbnail extraction failed', success: false } }];\n}\n\n// Convert to base64\nconst thumbnailBase64 = Buffer.from(rawImageData, 'binary').toString('base64');\nconsole.log('✅ Thumbnail extracted successfully');\n\nreturn [{ json: { filename: filename, thumbnailBase64: thumbnailBase64, success: true } }];"
                },
                "id": "process-thumbnail-6",
                "name": "Process Thumbnail",
                "type": "n8n-nodes-base.code",
                "typeVersion": 2,
                "position": [1000, 0]
            }
        ],
        "connections": {
            "Start": {
                "main": [
                    [
                        {
                            "node": "Load Config",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            },
            "Load Config": {
                "main": [
                    [
                        {
                            "node": "Scan Videos",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            },
            "Scan Videos": {
                "main": [
                    [
                        {
                            "node": "Process Files",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            },
            "Process Files": {
                "main": [
                    [
                        {
                            "node": "Extract Thumbnail",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            },
            "Extract Thumbnail": {
                "main": [
                    [
                        {
                            "node": "Process Thumbnail",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            }
        },
        "pinData": {},
        "active": False,
        "settings": {},
        "versionId": "1"
    }
    
    # Save the fresh workflow
    output_path = Path("kung_fu_video_workflow_fresh.json")
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(fresh_workflow, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Fresh workflow created: {output_path}")
    print(f"   Nodes: {len(fresh_workflow['nodes'])}")
    print(f"   Connections: {len(fresh_workflow['connections'])}")
    
    print(f"\n📋 WORKFLOW PATH:")
    print(f"   Start → Load Config → Scan Videos → Process Files → Extract Thumbnail → Process Thumbnail")
    
    # Verify connections
    print(f"\n🔗 CONNECTION VERIFICATION:")
    for source, targets in fresh_workflow['connections'].items():
        target_names = [t['node'] for target_list in targets['main'] for t in target_list]
        print(f"   ✅ {source} → {', '.join(target_names)}")
    
    return True

if __name__ == "__main__":
    success = create_fresh_minimal_workflow()
    
    if success:
        print(f"\n🎉 SUCCESS!")
        print(f"   Import 'kung_fu_video_workflow_fresh.json' into N8N")
        print(f"   This is a completely fresh workflow with simple node names")
        print(f"   Should show as a single connected chain with NO disconnected nodes")
    else:
        print(f"\n❌ Failed to create fresh workflow")
    
    sys.exit(0 if success else 1)
