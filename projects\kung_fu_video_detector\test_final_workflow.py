#!/usr/bin/env python3
"""Test the final kung fu video workflow before N8N import"""

import json
import sys
from pathlib import Path

def test_workflow_structure():
    """Test the workflow structure and readiness."""
    print("🧪 Testing Final Kung Fu Video Workflow")
    print("=" * 50)
    
    try:
        # Load workflow
        workflow_path = Path("kung_fu_video_workflow.json")
        with open(workflow_path, 'r', encoding='utf-8') as f:
            workflow = json.load(f)
        
        print(f"✅ Workflow loaded successfully")
        print(f"   Nodes: {len(workflow['nodes'])}")
        print(f"   Connections: {len(workflow['connections'])}")
        
        # Check critical nodes
        node_names = [node['name'] for node in workflow['nodes']]
        critical_nodes = [
            'Load Configuration',
            'Scan Folder for Videos', 
            'Process File List',
            'Extract Video Thumbnail',
            'Process Thumbnail',
            'AI Video Analysis',
            'Process AI Response',
            'Filter Kung Fu Videos'
        ]
        
        print("\n🔍 Critical Nodes Check:")
        all_present = True
        for node in critical_nodes:
            status = '✅' if node in node_names else '❌'
            print(f"  {status} {node}")
            if node not in node_names:
                all_present = False
        
        # Check node types
        print("\n🔧 Node Types:")
        ai_node = next((node for node in workflow['nodes'] if node['name'] == 'AI Video Analysis'), None)
        if ai_node:
            node_type = ai_node['type'].split('.')[-1]
            print(f"  🤖 AI Video Analysis: {node_type} (should be httpRequest)")
        
        scan_node = next((node for node in workflow['nodes'] if node['name'] == 'Scan Folder for Videos'), None)
        if scan_node:
            node_type = scan_node['type'].split('.')[-1]
            print(f"  📁 Scan Folder: {node_type} (should be executeCommand)")
        
        # Check for common issues
        issues = []
        
        # Check for templating in executeCommand nodes
        for node in workflow['nodes']:
            if node['type'] == 'n8n-nodes-base.executeCommand':
                command = node.get('parameters', {}).get('command', '')
                if '{{' in command and '}}' in command:
                    issues.append(f"Node '{node['name']}' uses templating in executeCommand")
        
        # Check for $http usage in code nodes
        for node in workflow['nodes']:
            if node['type'] == 'n8n-nodes-base.code':
                js_code = node.get('parameters', {}).get('jsCode', '')
                if '$http' in js_code:
                    issues.append(f"Node '{node['name']}' uses $http in code node")
        
        if issues:
            print("\n⚠️  Potential Issues:")
            for issue in issues:
                print(f"  - {issue}")
        else:
            print("\n✅ No obvious issues detected")
        
        # Summary
        print(f"\n📋 Workflow Summary:")
        print(f"  - All critical nodes present: {'✅' if all_present else '❌'}")
        print(f"  - Uses HTTP Request for AI calls: {'✅' if ai_node and 'httpRequest' in ai_node['type'] else '❌'}")
        print(f"  - Uses hardcoded commands: {'✅' if not issues else '❌'}")
        
        print(f"\n🎯 Next Steps:")
        print(f"  1. Import kung_fu_video_workflow.json into N8N")
        print(f"  2. Ensure LM Studio is running with mimo-vl-7b-rl@q8_k_xl")
        print(f"  3. Run workflow - should take 30-60 seconds")
        print(f"  4. Check AI Video Analysis node for real LM Studio responses")
        
        return all_present and not issues
        
    except Exception as e:
        print(f"❌ Error testing workflow: {e}")
        return False

if __name__ == "__main__":
    success = test_workflow_structure()
    print(f"\n{'🎉 Workflow ready for testing!' if success else '🔧 Workflow needs fixes before testing'}")
    sys.exit(0 if success else 1)
