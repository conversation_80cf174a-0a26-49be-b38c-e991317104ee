#!/usr/bin/env python3
"""
Production Vision API Proxy for Kung Fu Video Detection
Handles vision API calls with base64 images that N8N HTTP Request node can't handle
"""

import json
import requests
import base64
from flask import Flask, request, jsonify
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vision_api_proxy.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Configuration
LM_STUDIO_URL = "http://localhost:1234/v1/chat/completions"
DEFAULT_MODEL = "mimo-vl-7b-rl@q8_k_xl"
REQUEST_TIMEOUT = 60

# Statistics
stats = {
    'total_requests': 0,
    'successful_requests': 0,
    'failed_requests': 0,
    'start_time': datetime.now()
}

@app.route('/health', methods=['GET'])
def health_check():
    """Health check with statistics"""
    uptime = datetime.now() - stats['start_time']
    return jsonify({
        "status": "healthy",
        "service": "Vision API Proxy",
        "uptime_seconds": int(uptime.total_seconds()),
        "statistics": stats,
        "lm_studio_url": LM_STUDIO_URL,
        "model": DEFAULT_MODEL
    })

@app.route('/analyze-video-thumbnail', methods=['POST'])
def analyze_video_thumbnail():
    """
    Analyze video thumbnail for kung fu/martial arts content
    Expected HTTP headers (workaround for N8N POST body bug):
        X-Thumbnail: base64_image_data
        X-Filename: video.mp4
        X-Prompt: optional_custom_prompt
    """
    stats['total_requests'] += 1

    try:
        # Get data from HTTP headers (workaround for N8N POST body bug)
        thumbnail_base64 = request.headers.get('X-Thumbnail', '')
        filename = request.headers.get('X-Filename', 'unknown.mp4')
        custom_prompt = request.headers.get('X-Prompt', '')

        logger.info(f"Received video analysis request via HTTP headers")
        logger.info(f"Filename: {filename}, Thumbnail length: {len(thumbnail_base64)} chars")

        if not thumbnail_base64:
            logger.error("No thumbnail data received in HTTP headers")
            stats['failed_requests'] += 1
            return jsonify({
                "success": False,
                "error": "X-Thumbnail header is required",
                "details": "Request must include X-Thumbnail, X-Filename, and optionally X-Prompt headers",
                "received_headers": {
                    "thumbnail_length": len(thumbnail_base64),
                    "filename": filename,
                    "has_prompt": bool(custom_prompt)
                }
            }), 400
        
        # Default kung fu detection prompt
        default_prompt = "Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training. Look for martial arts poses, fighting stances, training equipment, or combat movements. Please respond with only YES if it shows kung fu/martial arts practice, or NO if it does not."
        
        text_prompt = custom_prompt if custom_prompt else default_prompt
        
        logger.info(f"Processing: {filename}, image_size: {len(thumbnail_base64)} chars")
        
        if not thumbnail_base64:
            stats['failed_requests'] += 1
            return jsonify({"error": "thumbnailBase64 is required"}), 400
        
        # Validate base64 image
        try:
            # Remove data URL prefix if present
            if thumbnail_base64.startswith('data:image'):
                thumbnail_base64 = thumbnail_base64.split(',')[1]
            
            # Test decode
            base64.b64decode(thumbnail_base64)
        except Exception as e:
            logger.error(f"Invalid base64 image data: {str(e)}")
            stats['failed_requests'] += 1
            return jsonify({"error": f"Invalid base64 image: {str(e)}"}), 400
        
        # Prepare LM Studio vision request
        lm_studio_payload = {
            "model": DEFAULT_MODEL,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": text_prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{thumbnail_base64}"
                            }
                        }
                    ]
                }
            ],
            "temperature": 0.1,
            "max_tokens": 50,
            "stream": False
        }
        
        logger.info(f"Sending vision request to LM Studio for {filename}")
        
        # Make request to LM Studio
        response = requests.post(
            LM_STUDIO_URL,
            json=lm_studio_payload,
            headers={"Content-Type": "application/json"},
            timeout=REQUEST_TIMEOUT
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            # Extract YES/NO from response
            analysis_result = "NO"
            content_upper = content.upper()
            if "YES" in content_upper:
                analysis_result = "YES"
            
            logger.info(f"Analysis result for {filename}: {analysis_result}")
            
            stats['successful_requests'] += 1
            
            return jsonify({
                "success": True,
                "filename": filename,
                "analysis_result": analysis_result,
                "full_response": content,
                "contains_kung_fu": analysis_result == "YES",
                "model_used": DEFAULT_MODEL,
                "prompt_used": text_prompt[:100] + "..." if len(text_prompt) > 100 else text_prompt
            })
        else:
            logger.error(f"LM Studio error: {response.status_code} - {response.text}")
            stats['failed_requests'] += 1
            return jsonify({
                "success": False,
                "error": f"LM Studio error: {response.status_code}",
                "details": response.text
            }), response.status_code
            
    except requests.exceptions.RequestException as e:
        logger.error(f"Request error: {str(e)}")
        stats['failed_requests'] += 1
        return jsonify({
            "success": False,
            "error": f"Request failed: {str(e)}"
        }), 500
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        stats['failed_requests'] += 1
        return jsonify({
            "success": False,
            "error": f"Internal error: {str(e)}"
        }), 500

if __name__ == '__main__':
    print("🥋 Starting Vision API Proxy for Kung Fu Video Detection")
    print(f"📡 LM Studio URL: {LM_STUDIO_URL}")
    print(f"🤖 Model: {DEFAULT_MODEL}")
    print("🌐 Proxy running on http://localhost:8081")
    print("\nEndpoints:")
    print("  GET  /health                    - Health check and statistics")
    print("  POST /analyze-video-thumbnail   - Analyze video thumbnail for kung fu content")
    print("       Headers: X-Thumbnail=base64_data, X-Filename=video.mp4, X-Prompt=optional")
    print("\nReady to process kung fu video analysis requests!")
    print("📝 Note: Using HTTP headers to work around N8N 1.109.2 POST body bug")
    
    app.run(host='127.0.0.1', port=8081, debug=True)
