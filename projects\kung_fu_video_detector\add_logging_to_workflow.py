#!/usr/bin/env python3
"""Add logging nodes to the kung fu video workflow for debugging"""

import json
import sys
from pathlib import Path

def add_logging_nodes():
    """Add comprehensive logging to the workflow."""
    print("🔧 Adding Execution Logging to Kung Fu Video Workflow")
    print("=" * 60)
    
    try:
        # Load current workflow
        workflow_path = Path("kung_fu_video_workflow.json")
        with open(workflow_path, 'r', encoding='utf-8') as f:
            workflow = json.load(f)
        
        print(f"✅ Current workflow loaded: {len(workflow['nodes'])} nodes")
        
        # Create logging nodes
        logging_node = {
            "parameters": {
                "jsCode": """// Create comprehensive execution log for debugging
const allInputs = $input.all();
const timestamp = new Date().toISOString();

const executionLog = {
  timestamp: timestamp,
  workflow_name: 'Kung Fu Video Detector',
  execution_summary: {
    total_inputs: allInputs.length,
    execution_time: timestamp,
    status: 'completed'
  },
  node_outputs: [],
  debug_info: {
    docker_mount: '/home/<USER>/shared/kung_fu_videos',
    lm_studio_endpoint: 'http://host.docker.internal:1234/v1/chat/completions',
    expected_files: 5
  }
};

// Process each input and log details
allInputs.forEach((input, index) => {
  const data = input.json;
  
  executionLog.node_outputs.push({
    input_index: index,
    filename: data.filename || 'unknown',
    fullPath: data.fullPath || 'unknown',
    hasConfig: !!data.config,
    hasThumbnail: !!data.thumbnailBase64,
    isKungFu: data.isKungFu || false,
    aiResponse: data.aiResponse || 'no response',
    error: data.error || null,
    timestamp: data.timestamp || 'unknown'
  });
});

// Add summary statistics
executionLog.summary = {
  total_videos_processed: allInputs.length,
  kung_fu_videos_found: allInputs.filter(input => input.json.isKungFu).length,
  errors_encountered: allInputs.filter(input => input.json.error).length,
  successful_ai_calls: allInputs.filter(input => input.json.aiResponse && !input.json.error).length
};

console.log('=== EXECUTION LOG SUMMARY ===');
console.log(`Total videos processed: ${executionLog.summary.total_videos_processed}`);
console.log(`Kung fu videos found: ${executionLog.summary.kung_fu_videos_found}`);
console.log(`Errors encountered: ${executionLog.summary.errors_encountered}`);
console.log(`Successful AI calls: ${executionLog.summary.successful_ai_calls}`);

// Create log content for file
const logContent = JSON.stringify(executionLog, null, 2);

return [{
  json: {
    logContent: logContent,
    filename: `kung_fu_detector_log_${timestamp.replace(/[:.]/g, '-')}.json`,
    summary: executionLog.summary,
    timestamp: timestamp
  }
}];"""
            },
            "id": "create-execution-log",
            "name": "Create Execution Log",
            "type": "n8n-nodes-base.code",
            "typeVersion": 2,
            "position": [-380, -80]
        }
        
        write_log_node = {
            "parameters": {
                "fileName": "={{ $json.filename }}",
                "data": "={{ $json.logContent }}",
                "options": {}
            },
            "id": "write-log-file",
            "name": "Write Log to Shared Folder",
            "type": "n8n-nodes-base.writeFile",
            "typeVersion": 1,
            "position": [-160, -80]
        }
        
        # Add nodes to workflow
        workflow['nodes'].extend([logging_node, write_log_node])
        
        # Add connections (connect from "Collect Final Results" to logging)
        if "Collect Final Results" not in workflow['connections']:
            workflow['connections']["Collect Final Results"] = {"main": [[]]}
        
        workflow['connections']["Collect Final Results"]["main"][0].append({
            "node": "Create Execution Log",
            "type": "main",
            "index": 0
        })
        
        workflow['connections']["Create Execution Log"] = {
            "main": [[{
                "node": "Write Log to Shared Folder",
                "type": "main",
                "index": 0
            }]]
        }
        
        # Save updated workflow
        output_path = Path("kung_fu_video_workflow_with_logging.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(workflow, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Updated workflow saved: {output_path}")
        print(f"   Total nodes: {len(workflow['nodes'])}")
        print(f"   Added logging nodes: Create Execution Log, Write Log to Shared Folder")
        
        print(f"\n🎯 How Logging Works:")
        print(f"   1. Collects data from all workflow nodes")
        print(f"   2. Creates comprehensive execution log with statistics")
        print(f"   3. Writes log file to Docker shared folder")
        print(f"   4. Log file can be reviewed externally for debugging")
        
        print(f"\n📋 Log File Location:")
        print(f"   Windows: C:\\Docker_Share\\N8N\\kung_fu_detector_log_[timestamp].json")
        print(f"   Container: /home/<USER>/shared/kung_fu_detector_log_[timestamp].json")
        
        print(f"\n🚀 Next Steps:")
        print(f"   1. Import kung_fu_video_workflow_with_logging.json into N8N")
        print(f"   2. Run workflow")
        print(f"   3. Check Docker shared folder for log file")
        print(f"   4. Review log file to see exactly what happened")
        
        return True
        
    except Exception as e:
        print(f"❌ Error adding logging: {e}")
        return False

if __name__ == "__main__":
    success = add_logging_nodes()
    sys.exit(0 if success else 1)
