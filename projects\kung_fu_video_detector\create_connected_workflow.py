#!/usr/bin/env python3
"""
Create a properly connected N8N workflow for kung fu video detection.
This script creates a workflow with proper JSON formatting and verified connections.
"""

import json
import os

def create_connected_workflow():
    """Create a properly connected N8N workflow with comprehensive logging."""
    
    workflow = {
        "name": "Kung Fu Video Detector - Connected",
        "nodes": [
            {
                "parameters": {},
                "id": "manual-trigger-1",
                "name": "Start",
                "type": "n8n-nodes-base.manualTrigger",
                "typeVersion": 1,
                "position": [250, 300]
            },
            {
                "parameters": {
                    "jsCode": """// Load configuration for kung fu video detection
const config = {
  video_folder: '/home/<USER>/shared/kung_fu_videos',
  recursive_search: true,
  file_extensions: ['.mp4', '.avi', '.mov', '.mkv'],
  lm_studio: {
    endpoint: 'http://host.docker.internal:1234/v1/chat/completions',
    model: 'mimo-vl-7b-rl@q8_k_xl',
    timeout: 30000,
    temperature: 0.1,
    max_tokens: 50
  },
  ai_prompt: 'Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training.',
  ffmpeg_path: 'ffmpeg',
  detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']
};

console.log('=== CONFIGURATION LOADED ===');
console.log('Video folder:', config.video_folder);
console.log('Extensions:', config.file_extensions);
console.log('LM Studio endpoint:', config.lm_studio.endpoint);

// Add per-node logging
const logData = {
  step: '1_config_load',
  timestamp: new Date().toISOString(),
  config: config
};

const binaryData = {
  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),
  mimeType: 'application/json',
  fileName: '1_config_log.json',
  fileExtension: 'json'
};

return [{
  json: { config: config, filename: '1_config_log.json' },
  binary: { data: binaryData }
}];"""
                },
                "id": "load-config-2",
                "name": "Load Config",
                "type": "n8n-nodes-base.code",
                "typeVersion": 2,
                "position": [500, 300]
            },
            {
                "parameters": {
                    "fileName": "={{ '/home/<USER>/shared/' + $json.filename }}",
                    "options": {
                        "overwrite": True
                    }
                },
                "id": "write-config-log-3",
                "name": "Write Config Log",
                "type": "n8n-nodes-base.writeBinaryFile",
                "typeVersion": 1,
                "position": [750, 300]
            },
            {
                "parameters": {
                    "command": 'find "/home/<USER>/shared/kung_fu_videos" -type f \\( -iname "*.mp4" -o -iname "*.avi" -o -iname "*.mov" -o -iname "*.mkv" \\)'
                },
                "id": "scan-videos-4",
                "name": "Scan Videos",
                "type": "n8n-nodes-base.executeCommand",
                "typeVersion": 2,
                "position": [1000, 300]
            },
            {
                "parameters": {
                    "jsCode": """// Process the find command output to create file list
const input = $input.first().json;
const commandOutput = input.stdout || '';
const exitCode = input.exitCode || 0;

console.log('=== PROCESSING VIDEO FILE SCAN ===');
console.log('Command output length:', commandOutput.length);
console.log('Exit code:', exitCode);

if (exitCode !== 0 || !commandOutput || commandOutput.trim() === '') {
  console.log('❌ No video files found');
  const errorLog = {
    step: '2_scan_error',
    timestamp: new Date().toISOString(),
    error: 'No video files found',
    exitCode: exitCode,
    stdout: commandOutput
  };
  
  const binaryData = {
    data: Buffer.from(JSON.stringify(errorLog, null, 2), 'utf8').toString('base64'),
    mimeType: 'application/json',
    fileName: '2_scan_error_log.json',
    fileExtension: 'json'
  };
  
  return [{ json: { error: 'No video files found', filename: '2_scan_error_log.json' }, binary: { data: binaryData } }];
}

// Split output into individual files
const fileLines = commandOutput.trim().split('\\n').filter(line => line.trim() !== '');
const videoFiles = [];

for (const line of fileLines) {
  const cleanLine = line.trim();
  if (cleanLine) {
    const filename = cleanLine.split('/').pop();
    videoFiles.push({
      filename: filename,
      fullPath: cleanLine
    });
  }
}

console.log(`Found ${videoFiles.length} video files`);

// Create scan success log
const scanLog = {
  step: '2_scan_success',
  timestamp: new Date().toISOString(),
  total_files_found: videoFiles.length,
  files: videoFiles,
  raw_output: commandOutput
};

const binaryData = {
  data: Buffer.from(JSON.stringify(scanLog, null, 2), 'utf8').toString('base64'),
  mimeType: 'application/json',
  fileName: '2_scan_log.json',
  fileExtension: 'json'
};

// Return each file as a separate item with logging data
const results = videoFiles.map(file => ({ json: file }));
results.push({ json: { filename: '2_scan_log.json' }, binary: { data: binaryData } });

return results;"""
                },
                "id": "process-files-5",
                "name": "Process Files",
                "type": "n8n-nodes-base.code",
                "typeVersion": 2,
                "position": [1250, 300]
            },
            {
                "parameters": {
                    "command": 'ffmpeg -i "{{ $json.fullPath }}" -ss 00:00:10 -vframes 1 -f image2pipe -vcodec png -'
                },
                "id": "extract-thumbnail-6",
                "name": "Extract Thumbnail",
                "type": "n8n-nodes-base.executeCommand",
                "typeVersion": 2,
                "position": [1500, 300]
            },
            {
                "parameters": {
                    "jsCode": """// Process FFmpeg thumbnail extraction
const input = $input.first().json;
const rawImageData = input.stdout || '';
const filename = input.filename;
const exitCode = input.exitCode || 0;

console.log('=== PROCESSING THUMBNAIL ===');
console.log(`File: ${filename}`);
console.log(`Exit code: ${exitCode}`);
console.log(`Raw data length: ${rawImageData.length}`);

if (exitCode !== 0 || !rawImageData || rawImageData.length < 1000) {
  console.log('❌ Thumbnail extraction failed');

  const errorLog = {
    step: '3_thumbnail_error',
    timestamp: new Date().toISOString(),
    filename: filename,
    error: 'Thumbnail extraction failed',
    exitCode: exitCode,
    data_length: rawImageData.length
  };

  const binaryData = {
    data: Buffer.from(JSON.stringify(errorLog, null, 2), 'utf8').toString('base64'),
    mimeType: 'application/json',
    fileName: `3_thumbnail_error_${filename}.json`,
    fileExtension: 'json'
  };

  return [{ json: { filename: filename, error: 'Thumbnail extraction failed', success: false, log_filename: `3_thumbnail_error_${filename}.json` }, binary: { data: binaryData } }];
}

// Convert to base64
const thumbnailBase64 = Buffer.from(rawImageData, 'binary').toString('base64');
console.log('✅ Thumbnail extracted successfully');

// Create success log
const successLog = {
  step: '3_thumbnail_success',
  timestamp: new Date().toISOString(),
  filename: filename,
  thumbnail_size_bytes: thumbnailBase64.length,
  success: true
};

const binaryData = {
  data: Buffer.from(JSON.stringify(successLog, null, 2), 'utf8').toString('base64'),
  mimeType: 'application/json',
  fileName: `3_thumbnail_success_${filename}.json`,
  fileExtension: 'json'
};

return [{ json: { filename: filename, thumbnailBase64: thumbnailBase64, success: true, log_filename: `3_thumbnail_success_${filename}.json` }, binary: { data: binaryData } }];"""
                },
                "id": "process-thumbnail-7",
                "name": "Process Thumbnail",
                "type": "n8n-nodes-base.code",
                "typeVersion": 2,
                "position": [1750, 300]
            },
            {
                "parameters": {
                    "url": "http://host.docker.internal:1234/v1/chat/completions",
                    "method": "POST",
                    "sendHeaders": True,
                    "headerParameters": {
                        "parameters": [
                            {
                                "name": "Content-Type",
                                "value": "application/json"
                            }
                        ]
                    },
                    "sendBody": True,
                    "bodyParameters": {
                        "parameters": []
                    },
                    "jsonBody": """={{ $json.success && $json.thumbnailBase64 ? {
  "model": "mimo-vl-7b-rl@q8_k_xl",
  "messages": [{
    "role": "user",
    "content": [
      {
        "type": "text",
        "text": "Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training. Answer with YES if kung fu/martial arts are present, NO if not present."
      },
      {
        "type": "image_url",
        "image_url": {
          "url": "data:image/png;base64," + $json.thumbnailBase64
        }
      }
    ]
  }],
  "temperature": 0.1,
  "max_tokens": 50
} : null }}""",
                    "options": {
                        "timeout": 30000
                    }
                },
                "id": "ai-analysis-8",
                "name": "AI Analysis",
                "type": "n8n-nodes-base.httpRequest",
                "typeVersion": 2,
                "position": [2000, 300]
            },
            {
                "parameters": {
                    "jsCode": """// Process AI analysis response
const input = $input.first().json;
const filename = input.filename;
const aiResponse = input.choices?.[0]?.message?.content || '';

console.log('=== PROCESSING AI ANALYSIS ===');
console.log(`File: ${filename}`);
console.log(`AI Response: ${aiResponse}`);

// Keywords that indicate kung fu/martial arts
const detectionKeywords = ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice'];

// Check if response contains kung fu indicators
const responseText = aiResponse.toLowerCase();
const isKungFu = detectionKeywords.some(keyword => responseText.includes(keyword));

const analysisResult = {
  filename: filename,
  ai_response: aiResponse,
  kung_fu_detected: isKungFu,
  confidence: isKungFu ? 'high' : 'low',
  timestamp: new Date().toISOString()
};

console.log(`Kung Fu Detected: ${isKungFu ? 'YES' : 'NO'}`);

// Create analysis log
const analysisLog = {
  step: '4_ai_analysis',
  timestamp: new Date().toISOString(),
  filename: filename,
  ai_response: aiResponse,
  kung_fu_detected: isKungFu,
  detection_keywords: detectionKeywords
};

const binaryData = {
  data: Buffer.from(JSON.stringify(analysisLog, null, 2), 'utf8').toString('base64'),
  mimeType: 'application/json',
  fileName: `4_ai_analysis_${filename}.json`,
  fileExtension: 'json'
};

return [{
  json: analysisResult,
  binary: { data: binaryData }
}];"""
                },
                "id": "process-ai-response-9",
                "name": "Process AI Response",
                "type": "n8n-nodes-base.code",
                "typeVersion": 2,
                "position": [2250, 300]
            },
            {
                "parameters": {
                    "jsCode": """// Collect and filter kung fu detection results
const allInputs = $input.all();
const kungFuVideos = [];
const allResults = [];

console.log('=== COLLECTING RESULTS ===');
console.log(`Processing ${allInputs.length} analysis results`);

for (const input of allInputs) {
  const result = input.json;

  if (result.filename && result.ai_response !== undefined) {
    allResults.push(result);

    if (result.kung_fu_detected) {
      kungFuVideos.push(result);
      console.log(`✅ Kung Fu detected in: ${result.filename}`);
    } else {
      console.log(`❌ No Kung Fu in: ${result.filename}`);
    }
  }
}

// Generate final report
const finalReport = {
  scan_timestamp: new Date().toISOString(),
  total_videos_analyzed: allResults.length,
  kung_fu_videos_found: kungFuVideos.length,
  kung_fu_detection_rate: allResults.length > 0 ? (kungFuVideos.length / allResults.length * 100).toFixed(1) + '%' : '0%',
  kung_fu_videos: kungFuVideos.map(video => ({
    filename: video.filename,
    ai_response: video.ai_response,
    confidence: video.confidence
  })),
  all_results: allResults
};

console.log(`=== FINAL RESULTS ===`);
console.log(`Total videos: ${finalReport.total_videos_analyzed}`);
console.log(`Kung Fu videos: ${finalReport.kung_fu_videos_found}`);
console.log(`Detection rate: ${finalReport.kung_fu_detection_rate}`);

// Create final report log
const reportLog = {
  step: '5_final_report',
  timestamp: new Date().toISOString(),
  report: finalReport
};

const binaryData = {
  data: Buffer.from(JSON.stringify(reportLog, null, 2), 'utf8').toString('base64'),
  mimeType: 'application/json',
  fileName: '5_final_report.json',
  fileExtension: 'json'
};

return [{
  json: {
    report: finalReport,
    filename: '5_final_report.json'
  },
  binary: { data: binaryData }
}];"""
                },
                "id": "generate-report-10",
                "name": "Generate Report",
                "type": "n8n-nodes-base.code",
                "typeVersion": 2,
                "position": [2500, 300]
            },
            {
                "parameters": {
                    "fileName": "={{ '/home/<USER>/shared/' + $json.filename }}",
                    "options": {
                        "overwrite": True
                    }
                },
                "id": "write-final-report-11",
                "name": "Write Final Report",
                "type": "n8n-nodes-base.writeBinaryFile",
                "typeVersion": 1,
                "position": [2750, 300]
            }
        ],
        "connections": {
            "Start": {
                "main": [
                    [
                        {
                            "node": "Load Config",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            },
            "Load Config": {
                "main": [
                    [
                        {
                            "node": "Write Config Log",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            },
            "Write Config Log": {
                "main": [
                    [
                        {
                            "node": "Scan Videos",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            },
            "Scan Videos": {
                "main": [
                    [
                        {
                            "node": "Process Files",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            },
            "Process Files": {
                "main": [
                    [
                        {
                            "node": "Extract Thumbnail",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            },
            "Extract Thumbnail": {
                "main": [
                    [
                        {
                            "node": "Process Thumbnail",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            },
            "Process Thumbnail": {
                "main": [
                    [
                        {
                            "node": "AI Analysis",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            },
            "AI Analysis": {
                "main": [
                    [
                        {
                            "node": "Process AI Response",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            },
            "Process AI Response": {
                "main": [
                    [
                        {
                            "node": "Generate Report",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            },
            "Generate Report": {
                "main": [
                    [
                        {
                            "node": "Write Final Report",
                            "type": "main",
                            "index": 0
                        }
                    ]
                ]
            }
        },
        "pinData": {},
        "active": False,
        "settings": {
            "executionOrder": "v1"
        },
        "staticData": {},
        "tags": [],
        "triggerCount": 0,
        "updatedAt": "2025-01-05T00:00:00.000Z",
        "versionId": "1"
    }
    
    return workflow

def main():
    """Main function to create and save the workflow."""
    print("Creating connected N8N workflow...")
    
    workflow = create_connected_workflow()
    
    # Save the workflow
    output_file = "kung_fu_video_workflow_complete.json"

    with open(output_file, 'w') as f:
        json.dump(workflow, f, indent=2)

    print(f"✅ Complete workflow saved to: {output_file}")

    # Validate the workflow structure
    print("\n=== WORKFLOW VALIDATION ===")
    print(f"Name: {workflow['name']}")
    print(f"Nodes: {len(workflow['nodes'])}")
    print(f"Connections: {len(workflow['connections'])}")

    # Check each node has proper structure
    for i, node in enumerate(workflow['nodes'], 1):
        print(f"  {i}. {node['name']} ({node['type']}) - ID: {node['id']}")

    # Verify connections
    print("\n=== CONNECTION VERIFICATION ===")
    for source_node, connections in workflow['connections'].items():
        for connection_list in connections['main']:
            for connection in connection_list:
                target_node = connection['node']
                print(f"  {source_node} → {target_node}")

    print("\n✅ Complete kung fu detection workflow created successfully!")
    print("This workflow includes:")
    print("  - Video file scanning")
    print("  - FFmpeg thumbnail extraction")
    print("  - AI vision analysis via LM Studio")
    print("  - Kung fu detection and reporting")
    print("  - Comprehensive per-node logging")
    print("\nImport this file into N8N to test the complete detection system.")

if __name__ == "__main__":
    main()
