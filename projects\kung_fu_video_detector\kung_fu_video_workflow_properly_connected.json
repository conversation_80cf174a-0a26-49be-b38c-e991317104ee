{"name": "Kung Fu Video Detector - Connected", "nodes": [{"parameters": {}, "id": "manual-trigger-1", "name": "Start", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"jsCode": "// Load configuration for kung fu video detection\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',\n  recursive_search: true,\n  file_extensions: ['.mp4', '.avi', '.mov', '.mkv'],\n  lm_studio: {\n    endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n    model: 'mimo-vl-7b-rl@q8_k_xl',\n    timeout: 30000,\n    temperature: 0.1,\n    max_tokens: 50\n  },\n  ai_prompt: 'Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training.',\n  ffmpeg_path: 'ffmpeg',\n  detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n};\n\nconsole.log('=== CONFIGURATION LOADED ===');\nconsole.log('Video folder:', config.video_folder);\nconsole.log('Extensions:', config.file_extensions);\nconsole.log('LM Studio endpoint:', config.lm_studio.endpoint);\n\n// Add per-node logging\nconst logData = {\n  step: '1_config_load',\n  timestamp: new Date().toISOString(),\n  config: config\n};\n\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: '1_config_log.json',\n  fileExtension: 'json'\n};\n\nreturn [{\n  json: { config: config, filename: '1_config_log.json' },\n  binary: { data: binaryData }\n}];"}, "id": "load-config-2", "name": "<PERSON><PERSON> Config", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [500, 300]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/' + $json.filename }}", "options": {"overwrite": true}}, "id": "write-config-log-3", "name": "Write Config Log", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [750, 300]}, {"parameters": {"command": "find \"/home/<USER>/shared/kung_fu_videos\" -type f \\( -iname \"*.mp4\" -o -iname \"*.avi\" -o -iname \"*.mov\" -o -iname \"*.mkv\" \\)"}, "id": "scan-videos-4", "name": "Scan Videos", "type": "n8n-nodes-base.executeCommand", "typeVersion": 2, "position": [1000, 300]}, {"parameters": {"jsCode": "// Process the find command output to create file list\nconst input = $input.first().json;\nconst commandOutput = input.stdout || '';\nconst exitCode = input.exitCode || 0;\n\nconsole.log('=== PROCESSING VIDEO FILE SCAN ===');\nconsole.log('Command output length:', commandOutput.length);\nconsole.log('Exit code:', exitCode);\n\nif (exitCode !== 0 || !commandOutput || commandOutput.trim() === '') {\n  console.log('❌ No video files found');\n  const errorLog = {\n    step: '2_scan_error',\n    timestamp: new Date().toISOString(),\n    error: 'No video files found',\n    exitCode: exitCode,\n    stdout: commandOutput\n  };\n  \n  const binaryData = {\n    data: Buffer.from(JSON.stringify(errorLog, null, 2), 'utf8').toString('base64'),\n    mimeType: 'application/json',\n    fileName: '2_scan_error_log.json',\n    fileExtension: 'json'\n  };\n  \n  return [{ json: { error: 'No video files found', filename: '2_scan_error_log.json' }, binary: { data: binaryData } }];\n}\n\n// Split output into individual files\nconst fileLines = commandOutput.trim().split('\\n').filter(line => line.trim() !== '');\nconst videoFiles = [];\n\nfor (const line of fileLines) {\n  const cleanLine = line.trim();\n  if (cleanLine) {\n    const filename = cleanLine.split('/').pop();\n    videoFiles.push({\n      filename: filename,\n      fullPath: cleanLine\n    });\n  }\n}\n\nconsole.log(`Found ${videoFiles.length} video files`);\n\n// Create scan success log\nconst scanLog = {\n  step: '2_scan_success',\n  timestamp: new Date().toISOString(),\n  total_files_found: videoFiles.length,\n  files: videoFiles,\n  raw_output: commandOutput\n};\n\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(scanLog, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: '2_scan_log.json',\n  fileExtension: 'json'\n};\n\n// Return each file as a separate item with logging data\nconst results = videoFiles.map(file => ({ json: file }));\nresults.push({ json: { filename: '2_scan_log.json' }, binary: { data: binaryData } });\n\nreturn results;"}, "id": "process-files-5", "name": "Process Files", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1250, 300]}, {"parameters": {"command": "ffmpeg -i \"{{ $json.fullPath }}\" -ss 00:00:10 -vframes 1 -f image2pipe -vcodec png -"}, "id": "extract-thumbnail-6", "name": "Extract Thumbnail", "type": "n8n-nodes-base.executeCommand", "typeVersion": 2, "position": [1500, 300]}, {"parameters": {"jsCode": "// Process FFmpeg thumbnail extraction\nconst input = $input.first().json;\nconst rawImageData = input.stdout || '';\nconst filename = input.filename;\nconst exitCode = input.exitCode || 0;\n\nconsole.log('=== PROCESSING THUMBNAIL ===');\nconsole.log(`File: ${filename}`);\nconsole.log(`Exit code: ${exitCode}`);\nconsole.log(`Raw data length: ${rawImageData.length}`);\n\nif (exitCode !== 0 || !rawImageData || rawImageData.length < 1000) {\n  console.log('❌ Thumbnail extraction failed');\n  \n  const errorLog = {\n    step: '3_thumbnail_error',\n    timestamp: new Date().toISOString(),\n    filename: filename,\n    error: 'Thumbnail extraction failed',\n    exitCode: exitCode,\n    data_length: rawImageData.length\n  };\n  \n  const binaryData = {\n    data: Buffer.from(JSON.stringify(errorLog, null, 2), 'utf8').toString('base64'),\n    mimeType: 'application/json',\n    fileName: `3_thumbnail_error_${filename}.json`,\n    fileExtension: 'json'\n  };\n  \n  return [{ json: { filename: filename, error: 'Thumbnail extraction failed', success: false, log_filename: `3_thumbnail_error_${filename}.json` }, binary: { data: binaryData } }];\n}\n\n// Convert to base64\nconst thumbnailBase64 = Buffer.from(rawImageData, 'binary').toString('base64');\nconsole.log('✅ Thumbnail extracted successfully');\n\n// Create success log\nconst successLog = {\n  step: '3_thumbnail_success',\n  timestamp: new Date().toISOString(),\n  filename: filename,\n  thumbnail_size_bytes: thumbnailBase64.length,\n  success: true\n};\n\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(successLog, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: `3_thumbnail_success_${filename}.json`,\n  fileExtension: 'json'\n};\n\nreturn [{ json: { filename: filename, thumbnailBase64: thumbnailBase64, success: true, log_filename: `3_thumbnail_success_${filename}.json` }, binary: { data: binaryData } }];"}, "id": "process-thumbnail-7", "name": "Process Thumbnail", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1750, 300]}], "connections": {"Start": {"main": [[{"node": "<PERSON><PERSON> Config", "type": "main", "index": 0}]]}, "Load Config": {"main": [[{"node": "Write Config Log", "type": "main", "index": 0}]]}, "Write Config Log": {"main": [[{"node": "Scan Videos", "type": "main", "index": 0}]]}, "Scan Videos": {"main": [[{"node": "Process Files", "type": "main", "index": 0}]]}, "Process Files": {"main": [[{"node": "Extract Thumbnail", "type": "main", "index": 0}]]}, "Extract Thumbnail": {"main": [[{"node": "Process Thumbnail", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}