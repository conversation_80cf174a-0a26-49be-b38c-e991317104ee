#!/usr/bin/env python3
"""Check if FFmpeg is available in the N8N Docker container"""

import subprocess
import sys

def check_ffmpeg():
    """Check FFmpeg availability in N8N container."""
    print("🔍 CHECKING FFMPEG AVAILABILITY IN N8N CONTAINER")
    print("=" * 60)
    
    try:
        # Check if N8N container is running
        print("1. Checking if N8N container is running...")
        result = subprocess.run(
            ["powershell", "-Command", "docker ps --filter name=n8n --format '{{.Names}}'"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0 and result.stdout.strip():
            container_name = result.stdout.strip()
            print(f"✅ N8N container found: {container_name}")
        else:
            print("❌ N8N container not running")
            return False
            
        # Check if FFmpeg is installed in container
        print("\n2. Checking FFmpeg installation in container...")
        result = subprocess.run(
            ["powershell", "-Command", f"docker exec {container_name} which ffmpeg"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0 and result.stdout.strip():
            ffmpeg_path = result.stdout.strip()
            print(f"✅ FFmpeg found at: {ffmpeg_path}")
        else:
            print("❌ FFmpeg not installed in N8N container")
            print("   This explains why thumbnails are falling back to mock!")
            return False
            
        # Check FFmpeg version
        print("\n3. Checking FFmpeg version...")
        result = subprocess.run(
            ["powershell", "-Command", f"docker exec {container_name} ffmpeg -version"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            version_info = result.stdout.split('\n')[0]
            print(f"✅ FFmpeg version: {version_info}")
        else:
            print("❌ Could not get FFmpeg version")
            
        # Test FFmpeg with a simple command
        print("\n4. Testing FFmpeg basic functionality...")
        result = subprocess.run(
            ["powershell", "-Command", f"docker exec {container_name} ffmpeg -f lavfi -i testsrc=duration=1:size=320x240:rate=1 -f null -"],
            capture_output=True,
            text=True,
            timeout=15
        )
        
        if result.returncode == 0:
            print("✅ FFmpeg basic test passed")
            return True
        else:
            print("❌ FFmpeg basic test failed")
            print(f"   Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Command timed out")
        return False
    except Exception as e:
        print(f"❌ Error checking FFmpeg: {e}")
        return False

def suggest_solutions():
    """Suggest solutions for FFmpeg issues."""
    print("\n🔧 SOLUTIONS FOR FFMPEG ISSUES")
    print("=" * 40)
    
    print("If FFmpeg is not installed in N8N container:")
    print("1. Install FFmpeg in the container:")
    print("   docker exec -u root n8n apt-get update")
    print("   docker exec -u root n8n apt-get install -y ffmpeg")
    print()
    print("2. Or use a custom N8N Docker image with FFmpeg pre-installed")
    print()
    print("3. Or modify docker-compose.yml to install FFmpeg on startup")
    print()
    print("Current behavior:")
    print("✅ Workflow detects FFmpeg failure and falls back to mock thumbnails")
    print("✅ This allows testing workflow logic without FFmpeg")
    print("❌ But AI analysis gets meaningless mock images instead of real video frames")

if __name__ == "__main__":
    ffmpeg_available = check_ffmpeg()
    
    if not ffmpeg_available:
        suggest_solutions()
    else:
        print("\n🎉 FFmpeg is working! The issue might be elsewhere.")
        print("   Check the N8N workflow logs for specific FFmpeg errors.")
