#!/usr/bin/env python3
"""Fix the disconnected nodes in the kung fu video workflow"""

import json
import sys
from pathlib import Path

def fix_workflow_connections():
    """Fix all the disconnected nodes in the workflow."""
    print("🔧 FIXING WORKFLOW CONNECTIONS")
    print("=" * 40)
    
    try:
        # Load current workflow
        workflow_path = Path("kung_fu_video_workflow_full_logging.json")
        with open(workflow_path, 'r', encoding='utf-8') as f:
            workflow = json.load(f)
        
        print(f"✅ Current workflow loaded: {len(workflow['nodes'])} nodes")
        
        # Clean up connections and rebuild properly
        workflow['connections'] = {}
        
        # Main workflow path connections
        main_connections = [
            ("Load Configuration", ["Scan Folder for Videos", "Log 1 Config"]),
            ("Scan Folder for Videos", ["Process File List", "Log 2 Scan"]),
            ("Process File List", ["Extract Video Thumbnail", "Log 3 Process"]),
            ("Extract Video Thumbnail", ["Process Thumbnail", "Log 4 Thumbnail"]),
            ("Process Thumbnail", ["AI Video Analysis"]),
            ("AI Video Analysis", ["Process AI Response", "Log 5 AI"]),
            ("Process AI Response", ["Generate Final Report"])
        ]
        
        # Logging connections
        logging_connections = [
            ("Log 1 Config", ["Write Log 1"]),
            ("Log 2 Scan", ["Write Log 2"]),
            ("Log 3 Process", ["Write Log 3"]),
            ("Log 4 Thumbnail", ["Write Log 4"]),
            ("Log 5 AI", ["Write Log 5"])
        ]
        
        # Add all connections
        all_connections = main_connections + logging_connections
        
        for source_node, target_nodes in all_connections:
            if source_node not in workflow['connections']:
                workflow['connections'][source_node] = {"main": [[]]}
            
            for target_node in target_nodes:
                workflow['connections'][source_node]["main"][0].append({
                    "node": target_node,
                    "type": "main",
                    "index": 0
                })
        
        # Remove any nodes that don't exist in the actual workflow
        existing_node_names = [node['name'] for node in workflow['nodes']]
        
        # Clean up connections to only include existing nodes
        cleaned_connections = {}
        for source, targets in workflow['connections'].items():
            if source in existing_node_names:
                cleaned_targets = []
                for target_list in targets["main"]:
                    valid_targets = []
                    for target in target_list:
                        if target["node"] in existing_node_names:
                            valid_targets.append(target)
                        else:
                            print(f"⚠️  Removing connection to non-existent node: {target['node']}")
                    if valid_targets:
                        cleaned_targets.append(valid_targets)
                
                if cleaned_targets:
                    cleaned_connections[source] = {"main": cleaned_targets}
            else:
                print(f"⚠️  Removing connections from non-existent node: {source}")
        
        workflow['connections'] = cleaned_connections
        
        # Save the fixed workflow
        output_path = Path("kung_fu_video_workflow_connected.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(workflow, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Fixed workflow saved: {output_path}")
        print(f"   Total nodes: {len(workflow['nodes'])}")
        print(f"   Total connections: {len(workflow['connections'])}")
        
        # Show connection summary
        print(f"\n📋 CONNECTION SUMMARY:")
        for source, targets in workflow['connections'].items():
            target_names = [t['node'] for target_list in targets['main'] for t in target_list]
            print(f"   {source} → {', '.join(target_names)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error fixing connections: {e}")
        return False

def create_simple_connected_workflow():
    """Create a simpler workflow with just the essential nodes properly connected."""
    print(f"\n🎯 CREATING SIMPLE CONNECTED WORKFLOW")
    print("=" * 45)
    
    try:
        # Load the current workflow to get the node definitions
        workflow_path = Path("kung_fu_video_workflow_full_logging.json")
        with open(workflow_path, 'r', encoding='utf-8') as f:
            full_workflow = json.load(f)
        
        # Find essential nodes
        essential_node_names = [
            "Load Configuration",
            "Scan Folder for Videos", 
            "Process File List",
            "Extract Video Thumbnail",
            "Process Thumbnail",
            "AI Video Analysis",
            "Process AI Response"
        ]
        
        # Extract essential nodes
        essential_nodes = []
        for node in full_workflow['nodes']:
            if node['name'] in essential_node_names:
                essential_nodes.append(node)
        
        # Create simple workflow structure
        simple_workflow = {
            "name": "Kung Fu Video Detector - Simple Connected",
            "nodes": essential_nodes,
            "pinData": {},
            "connections": {
                "Load Configuration": {
                    "main": [[{"node": "Scan Folder for Videos", "type": "main", "index": 0}]]
                },
                "Scan Folder for Videos": {
                    "main": [[{"node": "Process File List", "type": "main", "index": 0}]]
                },
                "Process File List": {
                    "main": [[{"node": "Extract Video Thumbnail", "type": "main", "index": 0}]]
                },
                "Extract Video Thumbnail": {
                    "main": [[{"node": "Process Thumbnail", "type": "main", "index": 0}]]
                },
                "Process Thumbnail": {
                    "main": [[{"node": "AI Video Analysis", "type": "main", "index": 0}]]
                },
                "AI Video Analysis": {
                    "main": [[{"node": "Process AI Response", "type": "main", "index": 0}]]
                }
            },
            "active": False,
            "settings": {},
            "versionId": "1"
        }
        
        # Save simple workflow
        simple_path = Path("kung_fu_video_workflow_simple.json")
        with open(simple_path, 'w', encoding='utf-8') as f:
            json.dump(simple_workflow, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Simple workflow created: {simple_path}")
        print(f"   Essential nodes: {len(essential_nodes)}")
        print(f"   Clean connections: {len(simple_workflow['connections'])}")
        
        print(f"\n📋 SIMPLE WORKFLOW PATH:")
        print(f"   Load Configuration → Scan Folder → Process File List")
        print(f"   → Extract Thumbnail → Process Thumbnail → AI Analysis → Process Response")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating simple workflow: {e}")
        return False

if __name__ == "__main__":
    print("🎯 WORKFLOW CONNECTION REPAIR")
    print("=" * 35)
    
    # Try to fix the full workflow first
    success1 = fix_workflow_connections()
    
    # Also create a simple version for testing
    success2 = create_simple_connected_workflow()
    
    if success1 and success2:
        print(f"\n🎉 SUCCESS!")
        print(f"   1. kung_fu_video_workflow_connected.json - Full workflow with fixed connections")
        print(f"   2. kung_fu_video_workflow_simple.json - Essential nodes only")
        print(f"\n💡 Recommendation: Try the simple workflow first to test basic functionality")
    else:
        print(f"\n❌ Some operations failed - check the errors above")
    
    sys.exit(0 if (success1 and success2) else 1)
