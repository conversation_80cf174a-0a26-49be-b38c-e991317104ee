#!/usr/bin/env python3
"""Fix case sensitivity and add logging after Process File List"""

import json
import sys
from pathlib import Path

def fix_workflow():
    """Fix case sensitivity and add process file list logging."""
    print("🔧 Fixing Case Sensitivity and Adding Process File List Logging")
    print("=" * 70)
    
    try:
        # Load current workflow
        workflow_path = Path("kung_fu_video_workflow_immediate_logging.json")
        with open(workflow_path, 'r', encoding='utf-8') as f:
            workflow = json.load(f)
        
        print(f"✅ Current workflow loaded: {len(workflow['nodes'])} nodes")
        
        # Fix 1: Update find command to use -iname (case-insensitive)
        for node in workflow['nodes']:
            if node['name'] == 'Scan Folder for Videos':
                old_command = node['parameters']['command']
                new_command = old_command.replace('-name', '-iname')
                node['parameters']['command'] = new_command
                print(f"✅ Fixed case sensitivity: -name → -iname")
                break
        
        # Fix 2: Add logging after Process File List
        process_log_node = {
            "parameters": {
                "jsCode": """// Log process file list results
const allInputs = $input.all();
const timestamp = new Date().toISOString();

const logData = {
  step: 'process_file_list',
  timestamp: timestamp,
  total_inputs: allInputs.length,
  processed_files: allInputs.map((input, index) => ({
    index: index,
    filename: input.json.filename || 'unknown',
    fullPath: input.json.fullPath || 'unknown',
    hasConfig: !!input.json.config,
    error: input.json.error || null
  }))
};

console.log('=== PROCESS FILE LIST LOG ===');
console.log(`Total files processed: ${logData.total_inputs}`);

const binaryData = {
  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),
  mimeType: 'application/json',
  fileName: 'process_file_list_log.json',
  fileExtension: 'json'
};

return [{
  json: {
    filename: 'process_file_list_log.json'
  },
  binary: {
    data: binaryData
  }
}];"""
            },
            "id": "log-process-file-list",
            "name": "Log Process File List",
            "type": "n8n-nodes-base.code",
            "typeVersion": 2,
            "position": [-1600, 200]
        }
        
        process_write_node = {
            "parameters": {
                "fileName": "={{ '/home/<USER>/shared/' + $json.filename }}",
                "options": {"overwrite": True}
            },
            "id": "write-process-log",
            "name": "Write Process Log",
            "type": "n8n-nodes-base.writeBinaryFile",
            "typeVersion": 1,
            "position": [-1400, 300]
        }
        
        # Add nodes
        workflow['nodes'].extend([process_log_node, process_write_node])
        
        # Add connections from Process File List to logging
        if "Process File List" not in workflow['connections']:
            workflow['connections']["Process File List"] = {"main": [[]]}
        
        workflow['connections']["Process File List"]["main"][0].append({
            "node": "Log Process File List",
            "type": "main",
            "index": 0
        })
        
        workflow['connections']["Log Process File List"] = {
            "main": [[{"node": "Write Process Log", "type": "main", "index": 0}]]
        }
        
        # Save updated workflow
        output_path = Path("kung_fu_video_workflow_fixed_logging.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(workflow, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Updated workflow saved: {output_path}")
        print(f"   Total nodes: {len(workflow['nodes'])}")
        print(f"   Added process file list logging")
        
        print(f"\n🎯 Fixes Applied:")
        print(f"   1. Case-insensitive file search: -iname instead of -name")
        print(f"   2. Logging after Process File List step")
        
        print(f"\n📋 Expected Results:")
        print(f"   - Should now find all 5 video files (including .MP4)")
        print(f"   - Will create process_file_list_log.json")
        print(f"   - Shows exactly what Process File List produces")
        
        return True
        
    except Exception as e:
        print(f"❌ Error fixing workflow: {e}")
        return False

if __name__ == "__main__":
    success = fix_workflow()
    sys.exit(0 if success else 1)
