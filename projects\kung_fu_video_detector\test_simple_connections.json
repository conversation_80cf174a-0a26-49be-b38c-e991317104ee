{"name": "Simple Connection Test", "nodes": [{"parameters": {}, "id": "1", "name": "Start", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"jsCode": "return [{ json: { message: 'Hello from node 2' } }];"}, "id": "2", "name": "Node 2", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [500, 300]}, {"parameters": {"jsCode": "return [{ json: { message: 'Hello from node 3' } }];"}, "id": "3", "name": "Node 3", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [750, 300]}], "connections": {"Start": {"main": [[{"node": "Node 2", "type": "main", "index": 0}]]}, "Node 2": {"main": [[{"node": "Node 3", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}