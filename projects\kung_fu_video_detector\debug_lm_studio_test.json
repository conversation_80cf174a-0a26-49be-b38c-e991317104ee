{"name": "Debug LM Studio Test", "nodes": [{"parameters": {}, "id": "manual-trigger-debug", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"jsCode": "// Create and log the exact request data\nconst requestData = {\n  model: \"mimo-vl-7b-rl@q8_k_xl\",\n  messages: [\n    {\n      role: \"user\",\n      content: \"Hello, please respond with just OK\"\n    }\n  ],\n  temperature: 0.1,\n  max_tokens: 5\n};\n\nconst jsonString = JSON.stringify(requestData);\n\nconsole.log('=== DEBUG REQUEST DATA ===');\nconsole.log('Request object:', requestData);\nconsole.log('JSON string:', jsonString);\nconsole.log('JSON string length:', jsonString.length);\nconsole.log('Model field:', requestData.model);\nconsole.log('Messages field:', requestData.messages);\nconsole.log('Messages length:', requestData.messages.length);\nconsole.log('First message:', requestData.messages[0]);\n\nreturn [{ \n  json: {\n    requestData: requestData,\n    jsonString: jsonString,\n    debugInfo: {\n      modelPresent: !!requestData.model,\n      messagesPresent: !!requestData.messages,\n      messagesIsArray: Array.isArray(requestData.messages),\n      messagesLength: requestData.messages ? requestData.messages.length : 0\n    }\n  }\n}];"}, "id": "debug-prepare-data", "name": "Debug Prepare Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [450, 300]}, {"parameters": {"method": "POST", "url": "http://host.docker.internal:1234/v1/chat/completions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "body": "={{ $json.jsonString }}", "options": {"timeout": 30000}}, "id": "debug-http-request", "name": "Debug HTTP Request", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [650, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Debug Prepare Data", "type": "main", "index": 0}]]}, "Debug Prepare Data": {"main": [[{"node": "Debug HTTP Request", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}