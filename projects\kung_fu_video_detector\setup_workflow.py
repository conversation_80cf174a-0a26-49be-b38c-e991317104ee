#!/usr/bin/env python3
"""
Setup script for the Kung Fu Video Detector workflow
Helps configure the workflow for your specific environment
"""

import json
import os
from pathlib import Path

def get_user_input():
    """Get configuration from user."""
    print("🛠️  Kung Fu Video Detector - Setup Configuration")
    print("=" * 50)
    
    # Get video folder path
    default_folder = "C:/Videos"
    video_folder = input(f"Enter your video folder path (default: {default_folder}): ").strip()
    if not video_folder:
        video_folder = default_folder
    
    # Get LM Studio endpoint
    default_endpoint = "http://localhost:1234/v1/chat/completions"
    endpoint = input(f"Enter LM Studio endpoint (default: {default_endpoint}): ").strip()
    if not endpoint:
        endpoint = default_endpoint
    
    # Get model name
    default_model = "mimo-vl-7b-rl"
    model_name = input(f"Enter model name (default: {default_model}): ").strip()
    if not model_name:
        model_name = default_model
    
    return {
        'video_folder': video_folder,
        'endpoint': endpoint,
        'model_name': model_name
    }

def update_workflow_config(config):
    """Update the workflow JSON with user configuration."""
    workflow_path = Path(__file__).parent / "kung_fu_video_workflow.json"
    
    try:
        # Load existing workflow with UTF-8 encoding
        with open(workflow_path, 'r', encoding='utf-8') as f:
            workflow = json.load(f)
        
        # Update folder path in the scan node
        for node in workflow['nodes']:
            if node['name'] == 'Scan Folder for MP4s':
                # Update the function code with new folder path
                old_code = node['parameters']['functionCode']
                new_code = old_code.replace(
                    "const folderPath = $input.first().json.folderPath || 'C:/Videos/KungFu';",
                    f"const folderPath = $input.first().json.folderPath || '{config['video_folder']}';"
                )
                node['parameters']['functionCode'] = new_code
                print(f"✅ Updated folder path to: {config['video_folder']}")
            
            elif node['name'] == 'AI Video Analysis':
                # Update endpoint URL
                node['parameters']['url'] = config['endpoint']
                
                # Update model name in JSON body
                json_body = json.loads(node['parameters']['jsonBody'])
                json_body['model'] = config['model_name']
                node['parameters']['jsonBody'] = json.dumps(json_body, indent=2)
                
                print(f"✅ Updated LM Studio endpoint to: {config['endpoint']}")
                print(f"✅ Updated model name to: {config['model_name']}")
        
        # Save updated workflow with UTF-8 encoding
        output_path = Path(__file__).parent / "kung_fu_video_workflow_configured.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(workflow, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Configured workflow saved to: {output_path}")
        return output_path
        
    except Exception as e:
        print(f"❌ Failed to update workflow: {e}")
        return None

def create_import_instructions(workflow_path, config):
    """Create instructions for importing the workflow."""
    instructions = f"""
# 📋 Import Instructions

## 1. Import into N8N
1. Open your N8N interface
2. Go to **Workflows** → **Import from File**
3. Select: `{workflow_path.name}`
4. Click **Import**

## 2. Verify Configuration
The workflow has been pre-configured with your settings:
- **Video Folder**: `{config['video_folder']}`
- **LM Studio Endpoint**: `{config['endpoint']}`
- **Model Name**: `{config['model_name']}`

## 3. Test the Workflow
1. Make sure LM Studio is running with your model loaded
2. Place some MP4 files in your video folder
3. Click **Execute Workflow** in N8N
4. Check the results in the "Collect Final Results" node

## 4. Customize Further (Optional)
- **Change folder path**: Edit the "Scan Folder for MP4s" node
- **Modify AI prompt**: Edit the "AI Video Analysis" node
- **Adjust filtering**: Edit the "Process AI Response" node

## 5. Troubleshooting
If you encounter issues:
- Run `python test_workflow.py` to verify setup
- Check LM Studio is running and model is loaded
- Verify folder permissions and file access
- Check N8N logs for detailed error messages

Happy kung fu video detecting! 🥋
"""
    
    instructions_path = Path(__file__).parent / "IMPORT_INSTRUCTIONS.md"
    with open(instructions_path, 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print(f"✅ Import instructions saved to: {instructions_path}")
    return instructions_path

def main():
    """Run the setup process."""
    try:
        # Get user configuration
        config = get_user_input()
        
        print(f"\n📝 Configuration Summary:")
        print(f"   Video Folder: {config['video_folder']}")
        print(f"   LM Studio Endpoint: {config['endpoint']}")
        print(f"   Model Name: {config['model_name']}")
        
        confirm = input(f"\nProceed with this configuration? (y/N): ").strip().lower()
        if confirm != 'y':
            print("Setup cancelled.")
            return
        
        # Update workflow
        print(f"\n🔧 Updating workflow configuration...")
        workflow_path = update_workflow_config(config)
        
        if workflow_path:
            # Create import instructions
            print(f"\n📋 Creating import instructions...")
            instructions_path = create_import_instructions(workflow_path, config)
            
            print(f"\n🎉 Setup Complete!")
            print(f"Files created:")
            print(f"   - Configured workflow: {workflow_path}")
            print(f"   - Import instructions: {instructions_path}")
            print(f"\nNext: Follow the instructions in {instructions_path.name}")
        
    except KeyboardInterrupt:
        print(f"\n\nSetup cancelled by user.")
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")

if __name__ == "__main__":
    main()
