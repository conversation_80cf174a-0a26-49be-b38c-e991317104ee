{"name": "Kung Fu Video Detector - No Templating", "nodes": [{"parameters": {}, "id": "ed85e2e5-dc5e-4575-88eb-afd38180a7c8", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"jsCode": "// Load configuration\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',\n  file_extensions: ['.mp4', '.avi', '.mov', '.mkv']\n};\n\nconsole.log('=== CONFIGURATION LOADED ===');\nreturn [{ json: { config: config } }];"}, "id": "load-config", "name": "Load Configuration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [500, 300]}, {"parameters": {"command": "find \"/home/<USER>/shared/kung_fu_videos\" -type f \\( -iname \"*.mp4\" -o -iname \"*.avi\" -o -iname \"*.mov\" -o -iname \"*.mkv\" \\) | head -1"}, "id": "scan-first-video", "name": "<PERSON>an First Video", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [750, 300]}, {"parameters": {"jsCode": "// Process scan result and create hardcoded FFmpeg command\nconst input = $input.first().json;\nconst commandOutput = input.stdout || '';\nconst exitCode = input.exitCode || 0;\n\nconsole.log('=== PROCESSING SCAN RESULT ===');\nconsole.log('Exit code:', exitCode);\nconsole.log('Output:', commandOutput);\n\nif (exitCode !== 0 || !commandOutput || commandOutput.trim() === '') {\n  console.log('No video files found');\n  return [{ json: { error: 'No video files found' } }];\n}\n\nconst filePath = commandOutput.trim();\nconst filename = filePath.split('/').pop();\n\nconsole.log(`Found video: ${filename}`);\nconsole.log(`Full path: ${filePath}`);\n\n// Create hardcoded FFmpeg command for this specific file\nconst ffmpegCommand = `ffmpeg -i \"${filePath}\" -ss 00:00:10 -vframes 1 -f image2pipe -vcodec png -`;\n\nconsole.log(`FFmpeg command: ${ffmpegCommand}`);\n\nreturn [{ json: { filename: filename, fullPath: filePath, ffmpegCommand: ffmpegCommand } }];"}, "id": "prepare-ffmpeg", "name": "Prepare FFmpeg Command", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1000, 300]}, {"parameters": {"command": "ffmpeg -i \"/home/<USER>/shared/kung_fu_videos/20250406_110016_1.mp4\" -ss 00:00:10 -vframes 1 -f image2pipe -vcodec png -"}, "id": "extract-thumbnail-hardcoded", "name": "Extract Thumbnail (Hardcoded)", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1250, 300]}, {"parameters": {"jsCode": "// Process FFmpeg result\nconst input = $input.first().json;\nconst rawImageData = input.stdout || '';\nconst exitCode = input.exitCode || 0;\n\nconsole.log('=== PROCESSING FFMPEG RESULT ===');\nconsole.log(`Exit code: ${exitCode}`);\nconsole.log(`Raw data length: ${rawImageData.length}`);\n\nif (exitCode !== 0 || !rawImageData || rawImageData.length < 1000) {\n  console.log('FFmpeg extraction failed');\n  return [{ json: { error: 'FFmpeg extraction failed', success: false } }];\n}\n\n// Convert to base64\nconst thumbnailBase64 = Buffer.from(rawImageData, 'binary').toString('base64');\nconsole.log('Thumbnail extracted successfully!');\nconsole.log(`Thumbnail size: ${Math.round(thumbnailBase64.length / 1024)}KB`);\n\nreturn [{ json: { thumbnailBase64: thumbnailBase64, success: true, sizeKB: Math.round(thumbnailBase64.length / 1024) } }];"}, "id": "process-result", "name": "Process Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1500, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Load Configuration", "type": "main", "index": 0}]]}, "Load Configuration": {"main": [[{"node": "<PERSON>an First Video", "type": "main", "index": 0}]]}, "Scan First Video": {"main": [[{"node": "Prepare FFmpeg Command", "type": "main", "index": 0}]]}, "Prepare FFmpeg Command": {"main": [[{"node": "Extract Thumbnail (Hardcoded)", "type": "main", "index": 0}]]}, "Extract Thumbnail (Hardcoded)": {"main": [[{"node": "Process Result", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}