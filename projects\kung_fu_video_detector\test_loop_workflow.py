#!/usr/bin/env python3
"""
Test the loop-based kung fu video detection workflow.
"""

import json
import sys
import os

def load_workflow():
    """Load the updated workflow JSON."""
    workflow_path = os.path.join(os.path.dirname(__file__), 'kung_fu_workflow_complete_file_based.json')
    
    with open(workflow_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def validate_loop_structure():
    """Validate that the loop structure is correctly implemented."""
    print("🔍 VALIDATING LOOP-BASED WORKFLOW STRUCTURE")
    print("=" * 50)
    
    workflow = load_workflow()
    nodes = workflow.get('nodes', [])
    connections = workflow.get('connections', {})
    
    # Find key nodes
    split_node = None
    extract_node = None
    process_node = None
    
    for node in nodes:
        if node.get('name') == 'Split Videos for Processing':
            split_node = node
        elif node.get('name') == 'Extract Video Thumbnail':
            extract_node = node
        elif node.get('name') == 'Process Thumbnail':
            process_node = node
    
    print(f"📊 Workflow Analysis:")
    print(f"  - Total nodes: {len(nodes)}")
    print(f"  - Total connections: {len(connections)}")
    
    # Validate SplitInBatches node
    if split_node:
        print(f"  ✅ Found SplitInBatches node: '{split_node.get('name')}'")
        print(f"    - Type: {split_node.get('type')}")
        print(f"    - Batch size: {split_node.get('parameters', {}).get('batchSize', 'not set')}")
        
        # Check if it has loop-back connection
        split_connections = connections.get('Log Write Log to Shared Folder', {}).get('main', [])
        has_loop_back = False
        for conn_group in split_connections:
            for conn in conn_group:
                if conn.get('node') == 'Split Videos for Processing':
                    has_loop_back = True
                    break
        
        if has_loop_back:
            print("    ✅ Loop-back connection found")
        else:
            print("    ❌ Loop-back connection missing")
    else:
        print("  ❌ SplitInBatches node not found")
    
    # Validate Extract Video Thumbnail node
    if extract_node:
        print(f"  ✅ Found Extract node: '{extract_node.get('name')}'")
        print(f"    - Type: {extract_node.get('type')}")
        
        if extract_node.get('type') == 'n8n-nodes-base.executeCommand':
            command = extract_node.get('parameters', {}).get('command', '')
            if '{{ $json.fullPath }}' in command:
                print("    ✅ Uses dynamic templating for file path")
            elif '20250406_110016_1.mp4' in command:
                print("    ❌ Still uses hardcoded filename")
            else:
                print("    ⚠️  Command structure unclear")
        else:
            print(f"    ⚠️  Unexpected node type: {extract_node.get('type')}")
    else:
        print("  ❌ Extract Video Thumbnail node not found")
    
    # Validate Process Thumbnail node
    if process_node:
        print(f"  ✅ Found Process node: '{process_node.get('name')}'")
        js_code = process_node.get('parameters', {}).get('jsCode', '')
        if 'input.filename' in js_code:
            print("    ✅ Uses dynamic filename from input")
        elif "'20250406_110016_1.mp4'" in js_code:
            print("    ❌ Still uses hardcoded filename")
        else:
            print("    ⚠️  Filename handling unclear")
    else:
        print("  ❌ Process Thumbnail node not found")
    
    return split_node is not None and extract_node is not None and process_node is not None

def suggest_next_steps():
    """Suggest next steps for testing."""
    print("\n🚀 NEXT STEPS FOR TESTING")
    print("=" * 30)
    
    print("1. **Import Updated Workflow into N8N:**")
    print("   - Copy kung_fu_workflow_complete_file_based.json")
    print("   - Import into N8N UI")
    print("   - Verify all nodes are connected properly")
    
    print("\n2. **Start File Processor:**")
    print("   - Ensure file_vision_processor.py is running")
    print("   - Check shared folders are accessible")
    
    print("\n3. **Test Execution:**")
    print("   - Execute workflow manually")
    print("   - Monitor execution logs for loop behavior")
    print("   - Check that all 5 videos are processed")
    
    print("\n4. **Validation Checks:**")
    print("   - Verify 5 vision request files are created")
    print("   - Confirm 5 kung fu detection results")
    print("   - Check execution time (should be longer than 1 second)")
    
    print("\n📋 SUCCESS CRITERIA:")
    print("   ✅ Workflow processes all 5 videos")
    print("   ✅ Each video gets individual thumbnail extraction")
    print("   ✅ Each video gets individual kung fu analysis")
    print("   ✅ Final log shows 5 videos processed")

def main():
    """Main function."""
    try:
        is_valid = validate_loop_structure()
        
        if is_valid:
            print("\n✅ WORKFLOW STRUCTURE VALIDATION PASSED")
            suggest_next_steps()
        else:
            print("\n❌ WORKFLOW STRUCTURE VALIDATION FAILED")
            print("Please check the workflow configuration.")
        
        return 0 if is_valid else 1
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
