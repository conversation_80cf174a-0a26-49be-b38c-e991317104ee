#!/usr/bin/env python3
"""
File-Based Notes Processor for N8N
Monitors shared folder for notes generation data and creates YYYYMMDD_Notes.txt files
Handles multiple notes files creation from grouped video data
"""

import json
import time
import os
from datetime import datetime
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('file_notes_processor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Configuration
SHARED_FOLDER = "C:/Docker_Share/N8N/logs"  # Monitor logs folder for notes generation data
NOTES_FOLDER = "C:/Docker_Share/N8N/notes"  # Output folder for notes files
POLL_INTERVAL = 2  # seconds

# Statistics
stats = {
    'total_requests': 0,
    'successful_requests': 0,
    'failed_requests': 0,
    'notes_files_created': 0,
    'start_time': datetime.now()
}

# Ensure folders exist
os.makedirs(SHARED_FOLDER, exist_ok=True)
os.makedirs(NOTES_FOLDER, exist_ok=True)

def read_processor_results(results_folder):
    """Read all JSON result files from a processor results folder"""
    results = []
    if not os.path.exists(results_folder):
        logger.warning(f"Results folder not found: {results_folder}")
        return results

    try:
        for filename in os.listdir(results_folder):
            if filename.endswith('.json'):
                file_path = os.path.join(results_folder, filename)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        result = json.load(f)
                        results.append(result)
                except Exception as e:
                    logger.warning(f"Failed to read result file {filename}: {e}")
    except Exception as e:
        logger.error(f"Error reading results folder {results_folder}: {e}")

    return results

def find_result_by_filename(results, target_filename):
    """Find a result that matches the target filename"""
    for result in results:
        result_filename = result.get('filename', '')
        if result_filename == target_filename:
            return result
    return None

def extract_date_from_filename(filename, date_extraction_config):
    """Extract date from filename using configuration"""
    pattern = date_extraction_config.get('filename_pattern', '^(20\\d{6})')
    fallback_date = date_extraction_config.get('fallback_date', '20120125')

    # Try to extract date from filename
    import re
    match = re.match(pattern, filename)
    if match:
        return match.group(1)

    # Use fallback date
    return fallback_date

def clean_description(description):
    """Clean up AI-generated description by removing thinking tags and extra content"""
    if not description:
        return "No description available"

    # Remove <think> tags and content
    import re
    cleaned = re.sub(r'<think>.*?</think>', '', description, flags=re.DOTALL)

    # Remove extra whitespace and newlines
    cleaned = ' '.join(cleaned.split())

    # Truncate if too long
    if len(cleaned) > 200:
        cleaned = cleaned[:200] + "..."

    return cleaned.strip() or "No description available"

def process_notes_from_processor_results(data, data_file):
    """New approach: Read actual results from processor folders and create notes"""
    logger.info("=== PROCESSING NOTES FROM PROCESSOR RESULTS ===")

    processing_config = data.get('processing', {})
    output_config = data.get('output', {})

    expected_files = processing_config.get('expected_files', [])
    result_folders = processing_config.get('result_folders', {})
    date_extraction = processing_config.get('date_extraction', {})

    notes_folder = output_config.get('notes_folder', NOTES_FOLDER)

    logger.info(f"Expected files: {len(expected_files)}")
    logger.info(f"Result folders: {result_folders}")
    logger.info(f"Notes folder: {notes_folder}")

    # Ensure notes folder exists
    os.makedirs(notes_folder, exist_ok=True)

    # Read results from each processor
    ffmpeg_results = read_processor_results(result_folders.get('ffmpeg_results', ''))
    vision_results = read_processor_results(result_folders.get('vision_results', ''))
    description_results = read_processor_results(result_folders.get('description_results', ''))

    logger.info(f"FFmpeg results: {len(ffmpeg_results)}")
    logger.info(f"Vision results: {len(vision_results)}")
    logger.info(f"Description results: {len(description_results)}")

    # Match and process results by filename
    processed_videos = []
    skipped_files = []

    for expected_file in expected_files:
        try:
            # Find matching results for this file
            ffmpeg_result = find_result_by_filename(ffmpeg_results, expected_file)
            vision_result = find_result_by_filename(vision_results, expected_file)
            description_result = find_result_by_filename(description_results, expected_file)

            if not ffmpeg_result or not ffmpeg_result.get('success'):
                logger.warning(f"No successful FFmpeg result for {expected_file}")
                skipped_files.append({
                    'filename': expected_file,
                    'reason': 'FFmpeg processing failed or missing'
                })
                continue

            if not vision_result or not vision_result.get('success'):
                logger.warning(f"No successful Vision result for {expected_file}")
                skipped_files.append({
                    'filename': expected_file,
                    'reason': 'Vision analysis failed or missing'
                })
                continue

            if not description_result or not description_result.get('success'):
                logger.warning(f"No successful Description result for {expected_file}")
                skipped_files.append({
                    'filename': expected_file,
                    'reason': 'Description analysis failed or missing'
                })
                continue

            # Extract date from filename
            extracted_date = extract_date_from_filename(expected_file, date_extraction)

            # Get description from result
            description = description_result.get('description', 'No description available')

            # Clean up description (remove AI thinking tags if present)
            description = clean_description(description)

            processed_videos.append({
                'filename': expected_file,
                'date': extracted_date,
                'description': description,
                'is_kung_fu': vision_result.get('analysis_result') == 'YES'
            })

            logger.info(f"✅ Processed {expected_file} → {extracted_date} → {description[:50]}...")

        except Exception as e:
            logger.error(f"Error processing {expected_file}: {str(e)}")
            skipped_files.append({
                'filename': expected_file,
                'reason': f'Processing error: {str(e)}'
            })

    # Group videos by date
    videos_by_date = {}
    for video in processed_videos:
        date = video['date']
        if date not in videos_by_date:
            videos_by_date[date] = []
        videos_by_date[date].append(video)

    logger.info(f"Grouped videos into {len(videos_by_date)} date groups")

    # Create notes files
    created_files = []
    errors = []

    for date, videos in videos_by_date.items():
        try:
            filename = f"{date}_Notes.txt"
            notes_path = os.path.join(notes_folder, filename)

            # Create notes content
            notes_lines = []
            for video in videos:
                line = f"{video['filename']} - {video['description']}"
                notes_lines.append(line)

            content = '\n'.join(notes_lines) + '\n'

            # Write notes file
            with open(notes_path, 'w', encoding='utf-8') as f:
                f.write(content)

            created_files.append({
                'filename': filename,
                'path': notes_path,
                'video_count': len(videos),
                'content_length': len(content),
                'date': date
            })

            stats['notes_files_created'] += 1
            logger.info(f"✅ Created notes file: {filename} ({len(videos)} videos)")

        except Exception as e:
            error_msg = f"Failed to create notes file for date {date}: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)

    # Create processing summary
    summary = {
        'success': len(errors) == 0,
        'processed_at': datetime.now().isoformat(),
        'source_file': os.path.basename(data_file),
        'notes_files_created': len(created_files),
        'total_videos_processed': len(processed_videos),
        'skipped_files_count': len(skipped_files),
        'created_files': created_files,
        'processing_errors': errors,
        'skipped_files_details': {
            'count': len(skipped_files),
            'files': skipped_files,
            'error_summary': {
                'ffmpeg_failures': len([f for f in skipped_files if 'ffmpeg' in str(f).lower()]),
                'vision_failures': len([f for f in skipped_files if 'vision' in str(f).lower()]),
                'description_failures': len([f for f in skipped_files if 'description' in str(f).lower()]),
                'other_failures': len([f for f in skipped_files if not any(x in str(f).lower() for x in ['ffmpeg', 'vision', 'description'])])
            }
        },
        'processing_success_rate': f"{(len(processed_videos) / len(expected_files) * 100):.1f}%" if expected_files else "0%"
    }

    # Write processing summary
    summary_filename = f"notes_processing_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    summary_path = os.path.join(output_config.get('log_folder', SHARED_FOLDER), summary_filename)

    try:
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        logger.info(f"Processing summary written to: {summary_filename}")
    except Exception as e:
        logger.error(f"Failed to write processing summary: {e}")

    # Clean up source file
    try:
        os.remove(data_file)
        logger.info(f"Cleaned up source file: {os.path.basename(data_file)}")
    except Exception as e:
        logger.warning(f"Failed to clean up source file: {e}")

    logger.info(f"✅ Successfully created {len(created_files)} notes files")
    return summary

def process_notes_generation_data(data_file):
    """Process notes generation data and create individual notes files"""
    try:
        # Read notes generation data
        with open(data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.info(f"Processing notes generation data: {os.path.basename(data_file)}")
        stats['total_requests'] += 1

        # Debug: Log the received data structure
        logger.info(f"Received data keys: {list(data.keys())}")
        logger.info(f"Data structure preview: {json.dumps(data, indent=2)[:500]}...")

        # Check if this is the new processor-results-based approach
        action = data.get('action', '')
        logger.info(f"Action field value: '{action}'")

        if action == 'generate_notes_from_processor_results':
            logger.info("✅ Using new processor-results-based approach")
            return process_notes_from_processor_results(data, data_file)
        else:
            logger.info(f"⚠️ Using legacy approach (action='{action}')")

        # Legacy approach (for backward compatibility)
        logger.info("Using legacy notes processing format")
        notes_generation_data = data.get('notesGenerationData', data)  # Handle both formats
        notes_files = notes_generation_data.get('notesFiles', [])
        total_files = notes_generation_data.get('totalNotesFiles', 0)
        total_videos = notes_generation_data.get('totalVideosProcessed', 0)
        skipped_files = notes_generation_data.get('skippedFiles', [])
        
        logger.info(f"Notes files to create: {total_files}")
        logger.info(f"Total videos processed: {total_videos}")
        logger.info(f"Skipped files: {len(skipped_files)}")
        
        created_files = []
        errors = []
        
        # Create each notes file
        for notes_file_data in notes_files:
            try:
                date = notes_file_data.get('date', 'unknown')
                filename = notes_file_data.get('filename', f'{date}_Notes.txt')
                content = notes_file_data.get('content', '')
                video_count = notes_file_data.get('videoCount', 0)
                
                logger.info(f"Creating notes file: {filename} ({video_count} videos)")
                
                # Write notes file
                notes_file_path = os.path.join(NOTES_FOLDER, filename)
                with open(notes_file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                created_files.append({
                    'filename': filename,
                    'date': date,
                    'video_count': video_count,
                    'content_length': len(content),
                    'path': notes_file_path
                })
                
                stats['notes_files_created'] += 1
                logger.info(f"✅ Created: {filename} ({len(content)} chars, {video_count} videos)")
                
            except Exception as e:
                error_msg = f"Failed to create notes file {notes_file_data.get('filename', 'unknown')}: {e}"
                logger.error(error_msg)
                errors.append(error_msg)
        
        # Create comprehensive processing summary with enhanced error details
        summary = {
            'success': len(errors) == 0,
            'processed_at': datetime.now().isoformat(),
            'source_file': os.path.basename(data_file),
            'notes_files_created': len(created_files),
            'total_videos_processed': total_videos,
            'skipped_files_count': len(skipped_files),
            'created_files': created_files,
            'processing_errors': errors,
            'skipped_files_details': {
                'count': len(skipped_files),
                'files': skipped_files,
                'error_summary': {
                    'ffmpeg_failures': len([f for f in skipped_files if 'ffmpeg' in str(f).lower()]),
                    'vision_failures': len([f for f in skipped_files if 'vision' in str(f).lower()]),
                    'description_failures': len([f for f in skipped_files if 'description' in str(f).lower()]),
                    'other_failures': len([f for f in skipped_files if not any(x in str(f).lower() for x in ['ffmpeg', 'vision', 'description'])])
                }
            }
        }
        
        # Write processing summary
        summary_filename = f"notes_processing_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        summary_path = os.path.join(SHARED_FOLDER, summary_filename)
        
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Processing summary written to: {summary_filename}")
        
        if len(errors) == 0:
            stats['successful_requests'] += 1
            logger.info(f"✅ Successfully created {len(created_files)} notes files")
        else:
            stats['failed_requests'] += 1
            logger.error(f"❌ Completed with {len(errors)} errors")
        
        # Clean up source data file
        try:
            os.remove(data_file)
            logger.info(f"Cleaned up source file: {os.path.basename(data_file)}")
        except Exception as e:
            logger.warning(f"Failed to clean up source file: {e}")
            
    except Exception as e:
        logger.error(f"Error processing notes generation data {data_file}: {e}")
        stats['failed_requests'] += 1
        
        # Write error summary
        try:
            error_summary = {
                'success': False,
                'processed_at': datetime.now().isoformat(),
                'source_file': os.path.basename(data_file) if 'data_file' in locals() else 'unknown',
                'error': str(e),
                'notes_files_created': 0
            }
            
            error_filename = f"notes_processing_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            error_path = os.path.join(SHARED_FOLDER, error_filename)
            
            with open(error_path, 'w', encoding='utf-8') as f:
                json.dump(error_summary, f, indent=2)
                
            logger.info(f"Error summary written to: {error_filename}")
                
        except Exception as write_error:
            logger.error(f"Failed to write error summary: {write_error}")

def scan_execution_logs_for_video_data():
    """Scan execution logs to extract video processing results"""
    logger.info("Scanning execution logs for video processing results")

    video_results = []
    log_files_processed = 0

    try:
        # Look for execution log files
        for file in os.listdir(SHARED_FOLDER):
            if file.startswith('video_processor_log_') and file.endswith('.json'):
                log_path = os.path.join(SHARED_FOLDER, file)
                try:
                    with open(log_path, 'r', encoding='utf-8') as f:
                        log_data = json.load(f)

                    # Extract video processing results from log
                    if 'node_outputs' in log_data:
                        for output in log_data['node_outputs']:
                            if (output.get('filename') and
                                output.get('extractedDate') and
                                output.get('description')):

                                video_results.append({
                                    'filename': output['filename'],
                                    'extractedDate': output['extractedDate'],
                                    'dateSource': output.get('dateSource', 'unknown'),
                                    'description': output['description'],
                                    'isKungFu': output.get('isKungFu', False),
                                    'processedAt': output.get('timestamp', datetime.now().isoformat())
                                })

                    log_files_processed += 1

                except Exception as e:
                    logger.warning(f"Error processing log file {file}: {e}")
                    continue

        logger.info(f"Processed {log_files_processed} log files, found {len(video_results)} video results")
        return video_results

    except Exception as e:
        logger.error(f"Error scanning execution logs: {e}")
        return []

def create_notes_files_from_video_data(video_results):
    """Create YYYYMMDD_Notes.txt files from video processing results"""
    if not video_results:
        logger.warning("No video results to process")
        return

    # Group videos by date
    videos_by_date = {}
    for video in video_results:
        date = video['extractedDate']
        if date not in videos_by_date:
            videos_by_date[date] = []
        videos_by_date[date].append(video)

    logger.info(f"Grouped videos into {len(videos_by_date)} date groups")

    created_files = []

    # Create notes file for each date
    for date, videos in videos_by_date.items():
        try:
            notes_filename = f"{date}_Notes.txt"
            notes_path = os.path.join(NOTES_FOLDER, notes_filename)

            # Create notes content
            notes_lines = []
            for video in videos:
                notes_line = f"{video['filename']} - {video['description']}"
                notes_lines.append(notes_line)

            notes_content = '\n'.join(notes_lines) + '\n'

            # Write notes file
            with open(notes_path, 'w', encoding='utf-8') as f:
                f.write(notes_content)

            created_files.append({
                'filename': notes_filename,
                'date': date,
                'video_count': len(videos),
                'content_length': len(notes_content)
            })

            stats['notes_files_created'] += 1
            logger.info(f"✅ Created: {notes_filename} ({len(videos)} videos)")

        except Exception as e:
            logger.error(f"Error creating notes file for date {date}: {e}")

    return created_files

def monitor_requests():
    """Monitor for processing completion and create notes files"""
    logger.info("Starting File-Based Notes Processor")
    logger.info(f"Monitoring: {SHARED_FOLDER}")
    logger.info(f"Notes output: {NOTES_FOLDER}")
    logger.info(f"Poll interval: {POLL_INTERVAL} seconds")

    last_scan_time = 0

    while True:
        try:
            current_time = time.time()

            # Look for trigger files or recent log activity
            trigger_files = []
            recent_logs = []

            for file in os.listdir(SHARED_FOLDER):
                file_path = os.path.join(SHARED_FOLDER, file)
                file_mtime = os.path.getmtime(file_path)

                # Look for notes generation data files (original approach)
                if file.startswith('notes_generation_data_') and file.endswith('.json'):
                    trigger_files.append(file)

                # Look for recent execution logs
                elif (file.startswith('video_processor_log_') and file.endswith('.json') and
                      file_mtime > last_scan_time):
                    recent_logs.append(file)

            # Process notes generation data files (if any)
            if trigger_files:
                logger.info(f"Found {len(trigger_files)} notes generation data file(s)")
                for data_file in trigger_files:
                    data_path = os.path.join(SHARED_FOLDER, data_file)
                    process_notes_generation_data(data_path)

            # Process recent execution logs
            elif recent_logs:
                logger.info(f"Found {len(recent_logs)} recent execution log(s)")
                video_results = scan_execution_logs_for_video_data()
                if video_results:
                    created_files = create_notes_files_from_video_data(video_results)
                    if created_files:
                        stats['successful_requests'] += 1
                        logger.info(f"✅ Successfully created {len(created_files)} notes files from execution logs")

                last_scan_time = current_time

            # Log statistics periodically
            if current_time - stats.get('last_stats_time', 0) > 60:  # Every minute
                uptime = datetime.now() - stats['start_time']
                logger.info(f"Stats - Total: {stats['total_requests']}, Success: {stats['successful_requests']}, "
                          f"Failed: {stats['failed_requests']}, Notes Files: {stats['notes_files_created']}, "
                          f"Uptime: {uptime}")
                stats['last_stats_time'] = current_time

            time.sleep(POLL_INTERVAL)

        except KeyboardInterrupt:
            logger.info("Shutting down File-Based Notes Processor")
            break
        except Exception as e:
            logger.error(f"Error in monitor loop: {e}")
            time.sleep(POLL_INTERVAL)

if __name__ == "__main__":
    monitor_requests()
