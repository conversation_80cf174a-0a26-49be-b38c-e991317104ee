{"name": "File-Based Vision Analysis Nodes", "description": "Replacement nodes for AI Video Analysis using file-based communication", "nodes": [{"parameters": {"jsCode": "// Write Vision Request to Shared Folder\nconst input = $input.first().json;\nconst thumbnailBase64 = input.thumbnailBase64;\nconst filename = input.filename;\n\n// Generate unique request ID\nconst requestId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n\n// Prepare request data\nconst requestData = {\n  request_id: requestId,\n  filename: filename,\n  thumbnailBase64: thumbnailBase64,\n  prompt: \"Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training. Look for martial arts poses, fighting stances, training equipment, or combat movements. Please respond with only YES if it shows kung fu/martial arts practice, or NO if it does not.\",\n  created_at: new Date().toISOString()\n};\n\nconsole.log('=== WRITING VISION REQUEST ===');\nconsole.log('Request ID:', requestId);\nconsole.log('Filename:', filename);\nconsole.log('Thumbnail length:', thumbnailBase64 ? thumbnailBase64.length : 0);\n\n// Prepare data for file writing\nconst jsonString = JSON.stringify(requestData, null, 2);\nconst binaryData = {\n  data: Buffer.from(jsonString, 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: `request_${requestId}.json`,\n  fileExtension: 'json'\n};\n\nreturn [{\n  json: {\n    request_id: requestId,\n    filename: filename,\n    request_file: `request_${requestId}.json`,\n    data_size: jsonString.length\n  },\n  binary: {\n    data: binaryData\n  }\n}];"}, "id": "write-vision-request", "name": "Write Vision Request", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [650, 300]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/vision_requests/' + $json.request_file }}", "options": {"overwrite": true}}, "id": "save-vision-request", "name": "Save Vision Request", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [850, 300]}, {"parameters": {"jsCode": "// Wait for Vision Result\nconst input = $input.first().json;\nconst requestId = input.request_id;\nconst filename = input.filename;\n\nconsole.log('=== WAITING FOR VISION RESULT ===');\nconsole.log('Request ID:', requestId);\nconsole.log('Filename:', filename);\n\n// Wait a moment for processing\nconst waitTime = 3000; // 3 seconds\nconsole.log(`Waiting ${waitTime}ms for vision processing...`);\n\n// Return data for next node to check results\nreturn [{\n  json: {\n    request_id: requestId,\n    filename: filename,\n    result_file: `result_${requestId}.json`,\n    wait_completed: true\n  }\n}];"}, "id": "wait-for-result", "name": "Wait for Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1050, 300]}, {"parameters": {"command": "=sleep 3 && if [ -f \"/home/<USER>/shared/vision_results/result_{{ $json.request_id }}.json\" ]; then cat \"/home/<USER>/shared/vision_results/result_{{ $json.request_id }}.json\"; else echo '{\"success\": false, \"error\": \"Result not found\", \"request_id\": \"{{ $json.request_id }}\"}'; fi"}, "id": "read-vision-result", "name": "Read Vision Result", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1250, 300]}, {"parameters": {"jsCode": "// Parse Vision Result\nconst input = $input.first().json;\nconst stdout = input.stdout;\n\nconsole.log('=== PARSING VISION RESULT ===');\nconsole.log('Raw stdout:', stdout);\n\ntry {\n  // Parse the JSON result\n  const result = JSON.parse(stdout);\n  \n  console.log('Parsed result:', result);\n  console.log('Success:', result.success);\n  console.log('Analysis result:', result.analysis_result);\n  \n  // Return standardized result format\n  return [{\n    json: {\n      success: result.success || false,\n      filename: result.filename || 'unknown',\n      analysis_result: result.analysis_result || 'NO',\n      contains_kung_fu: result.contains_kung_fu || false,\n      full_response: result.full_response || '',\n      model_used: result.model_used || 'unknown',\n      processed_at: result.processed_at || new Date().toISOString(),\n      request_id: result.request_id || 'unknown'\n    }\n  }];\n  \n} catch (error) {\n  console.log('Error parsing result:', error.message);\n  \n  // Return error result\n  return [{\n    json: {\n      success: false,\n      filename: 'unknown',\n      analysis_result: 'ERROR',\n      contains_kung_fu: false,\n      full_response: '',\n      error: `Failed to parse result: ${error.message}`,\n      raw_stdout: stdout\n    }\n  }];\n}"}, "id": "parse-vision-result", "name": "Parse Vision Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1450, 300]}], "connections": {"Write Vision Request": {"main": [[{"node": "Save Vision Request", "type": "main", "index": 0}]]}, "Save Vision Request": {"main": [[{"node": "Wait for Result", "type": "main", "index": 0}]]}, "Wait for Result": {"main": [[{"node": "Read Vision Result", "type": "main", "index": 0}]]}, "Read Vision Result": {"main": [[{"node": "Parse Vision Result", "type": "main", "index": 0}]]}}}