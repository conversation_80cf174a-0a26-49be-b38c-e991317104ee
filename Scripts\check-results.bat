@echo off
REM Quick Results Checker for Video Processor Workflow
REM Usage: check-results.bat [detailed] [errors] [open]

cd /d "%~dp0.."

if "%1"=="detailed" (
    powershell -ExecutionPolicy Bypass -File "Scripts\Check-VideoProcessorResults.ps1" -Detailed
) else if "%1"=="errors" (
    powershell -ExecutionPolicy Bypass -File "Scripts\Check-VideoProcessorResults.ps1" -ShowErrors
) else if "%1"=="open" (
    powershell -ExecutionPolicy Bypass -File "Scripts\Check-VideoProcessorResults.ps1" -OpenNotes
) else if "%1"=="all" (
    powershell -ExecutionPolicy Bypass -File "Scripts\Check-VideoProcessorResults.ps1" -Detailed -ShowErrors -OpenNotes
) else (
    powershell -ExecutionPolicy Bypass -File "Scripts\Check-VideoProcessorResults.ps1"
)

pause
