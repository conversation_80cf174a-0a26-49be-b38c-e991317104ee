# N8N Upgrade Script
# Upgrades N8N from 1.99.0 to latest version

Write-Host "🚀 N8N Upgrade Script" -ForegroundColor Green
Write-Host "Current Version: 1.99.0" -ForegroundColor Yellow
Write-Host "Target: Latest N8N Version" -ForegroundColor Green

# Step 1: Backup current data
Write-Host "`n📋 Step 1: Creating Backup..." -ForegroundColor Cyan
$backupDate = Get-Date -Format "yyyyMMdd_HHmmss"
$backupDir = "backups/upgrade_$backupDate"

# Create backup directory
New-Item -ItemType Directory -Path $backupDir -Force | Out-Null

# Backup workflows
Write-Host "  - Backing up workflows..." -ForegroundColor White
docker exec n8n n8n export:workflow --all --output=/home/<USER>/shared/workflows_backup_$backupDate.json

# Backup credentials  
Write-Host "  - Backing up credentials..." -ForegroundColor White
docker exec n8n n8n export:credentials --all --output=/home/<USER>/shared/credentials_backup_$backupDate.json

# Copy data directory
Write-Host "  - Backing up data directory..." -ForegroundColor White
Copy-Item -Path "data" -Destination "$backupDir/data" -Recurse -Force

# Backup docker-compose.yml
Copy-Item -Path "docker-compose.yml" -Destination "$backupDir/docker-compose.yml.backup" -Force

Write-Host "✅ Backup completed in: $backupDir" -ForegroundColor Green

# Step 2: Update docker-compose.yml
Write-Host "`n🔧 Step 2: Updating docker-compose.yml..." -ForegroundColor Cyan

# Read current docker-compose.yml
$dockerCompose = Get-Content "docker-compose.yml" -Raw

# Replace version
$newDockerCompose = $dockerCompose -replace "n8nio/n8n:1.99.0", "n8nio/n8n:latest"

# Write updated docker-compose.yml
Set-Content -Path "docker-compose.yml" -Value $newDockerCompose

Write-Host "✅ Updated docker-compose.yml to use n8nio/n8n:latest" -ForegroundColor Green

# Step 3: Stop current container
Write-Host "`n⏹️ Step 3: Stopping current N8N container..." -ForegroundColor Cyan
docker-compose down

# Step 4: Pull latest image
Write-Host "`n📥 Step 4: Pulling latest N8N image..." -ForegroundColor Cyan
docker pull n8nio/n8n:latest

# Step 5: Start with new version
Write-Host "`n🚀 Step 5: Starting N8N with latest version..." -ForegroundColor Cyan
docker-compose up -d

# Step 6: Wait for startup
Write-Host "`n⏳ Step 6: Waiting for N8N to start..." -ForegroundColor Cyan
Start-Sleep -Seconds 30

# Step 7: Check status
Write-Host "`n🔍 Step 7: Checking N8N status..." -ForegroundColor Cyan
$containerStatus = docker ps --filter "name=n8n" --format "table {{.Names}}\t{{.Status}}\t{{.Image}}"
Write-Host $containerStatus -ForegroundColor White

# Step 8: Test connection
Write-Host "`n🧪 Step 8: Testing N8N connection..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5678" -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ N8N is accessible at http://localhost:5678" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ N8N connection test failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Check docker logs: docker logs n8n" -ForegroundColor Yellow
}

Write-Host "`n🎉 N8N Upgrade Complete!" -ForegroundColor Green
Write-Host "📋 Backup location: $backupDir" -ForegroundColor Yellow
Write-Host "🌐 Access N8N at: http://localhost:5678" -ForegroundColor Cyan
Write-Host "📊 Check version in N8N UI: Settings > About" -ForegroundColor Cyan

Write-Host "`n🔧 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Test the HTTP Request node with POST method" -ForegroundColor White
Write-Host "2. Import your kung fu video workflow" -ForegroundColor White  
Write-Host "3. Verify LM Studio logs show POST requests" -ForegroundColor White
Write-Host "4. If issues persist, check docker logs: docker logs n8n" -ForegroundColor White
