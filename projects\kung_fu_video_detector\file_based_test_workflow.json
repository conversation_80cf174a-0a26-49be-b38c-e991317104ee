{"name": "File-Based Vision Test", "nodes": [{"parameters": {}, "id": "manual-trigger-file-test", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"jsCode": "// Create test data with a real base64 thumbnail\n// This simulates what the FFmpeg node would produce\nconst testBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';\n\nconst testData = {\n  thumbnailBase64: testBase64,\n  filename: 'test_kung_fu_video.mp4'\n};\n\nconsole.log('=== FILE-BASED TEST DATA ===');\nconsole.log('Thumbnail length:', testData.thumbnailBase64.length);\nconsole.log('Filename:', testData.filename);\n\nreturn [{ json: testData }];"}, "id": "create-test-thumbnail", "name": "Create Test Thumbnail", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [450, 300]}, {"parameters": {"jsCode": "// Write Vision Request to Shared Folder\nconst input = $input.first().json;\nconst thumbnailBase64 = input.thumbnailBase64;\nconst filename = input.filename;\n\n// Generate unique request ID\nconst requestId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n\n// Prepare request data\nconst requestData = {\n  request_id: requestId,\n  filename: filename,\n  thumbnailBase64: thumbnailBase64,\n  prompt: \"Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training. Look for martial arts poses, fighting stances, training equipment, or combat movements. Please respond with only YES if it shows kung fu/martial arts practice, or NO if it does not.\",\n  created_at: new Date().toISOString()\n};\n\nconsole.log('=== WRITING VISION REQUEST ===');\nconsole.log('Request ID:', requestId);\nconsole.log('Filename:', filename);\nconsole.log('Thumbnail length:', thumbnailBase64 ? thumbnailBase64.length : 0);\n\n// Prepare data for file writing\nconst jsonString = JSON.stringify(requestData, null, 2);\nconst binaryData = {\n  data: Buffer.from(jsonString, 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: `request_${requestId}.json`,\n  fileExtension: 'json'\n};\n\nreturn [{\n  json: {\n    request_id: requestId,\n    filename: filename,\n    request_file: `request_${requestId}.json`,\n    data_size: jsonString.length\n  },\n  binary: {\n    data: binaryData\n  }\n}];"}, "id": "write-vision-request-test", "name": "Write Vision Request", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [650, 300]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/vision_requests/' + $json.request_file }}", "options": {"overwrite": true}}, "id": "save-vision-request-test", "name": "Save Vision Request", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [850, 300]}, {"parameters": {"command": "=sleep 5 && if [ -f \"/home/<USER>/shared/vision_results/result_{{ $json.request_id }}.json\" ]; then cat \"/home/<USER>/shared/vision_results/result_{{ $json.request_id }}.json\"; else echo '{\"success\": false, \"error\": \"Result not found after 5 seconds\", \"request_id\": \"{{ $json.request_id }}\"}'; fi"}, "id": "read-vision-result-test", "name": "Read Vision Result", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1050, 300]}, {"parameters": {"jsCode": "// Parse Vision Result\nconst input = $input.first().json;\nconst stdout = input.stdout;\n\nconsole.log('=== PARSING VISION RESULT ===');\nconsole.log('Raw stdout:', stdout);\n\ntry {\n  // Parse the JSON result\n  const result = JSON.parse(stdout);\n  \n  console.log('Parsed result:', result);\n  console.log('Success:', result.success);\n  console.log('Analysis result:', result.analysis_result);\n  \n  // Return standardized result format\n  return [{\n    json: {\n      success: result.success || false,\n      filename: result.filename || 'unknown',\n      analysis_result: result.analysis_result || 'NO',\n      contains_kung_fu: result.contains_kung_fu || false,\n      full_response: result.full_response || '',\n      model_used: result.model_used || 'unknown',\n      processed_at: result.processed_at || new Date().toISOString(),\n      request_id: result.request_id || 'unknown'\n    }\n  }];\n  \n} catch (error) {\n  console.log('Error parsing result:', error.message);\n  \n  // Return error result\n  return [{\n    json: {\n      success: false,\n      filename: 'unknown',\n      analysis_result: 'ERROR',\n      contains_kung_fu: false,\n      full_response: '',\n      error: `Failed to parse result: ${error.message}`,\n      raw_stdout: stdout\n    }\n  }];\n}"}, "id": "parse-vision-result-test", "name": "Parse Vision Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1250, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Create Test Thumbnail", "type": "main", "index": 0}]]}, "Create Test Thumbnail": {"main": [[{"node": "Write Vision Request", "type": "main", "index": 0}]]}, "Write Vision Request": {"main": [[{"node": "Save Vision Request", "type": "main", "index": 0}]]}, "Save Vision Request": {"main": [[{"node": "Read Vision Result", "type": "main", "index": 0}]]}, "Read Vision Result": {"main": [[{"node": "Parse Vision Result", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}