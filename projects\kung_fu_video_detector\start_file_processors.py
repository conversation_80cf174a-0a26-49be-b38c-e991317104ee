#!/usr/bin/env python3
"""
Start all file processors for the Kung Fu Video Detection system.
Runs vision processor, FFmpeg processor, description processor, and notes processor in parallel.
"""

import subprocess
import sys
import os
import time
from pathlib import Path

def start_processors():
    """Start all file processors."""
    print("🚀 Starting Kung Fu Video Detection File Processors")
    print("=" * 50)

    # Get current directory
    current_dir = Path(__file__).parent

    # Define processor scripts
    vision_processor = current_dir / "file_vision_processor.py"
    ffmpeg_processor = current_dir / "file_ffmpeg_processor.py"
    description_processor = current_dir / "file_description_processor.py"
    notes_processor = current_dir / "file_notes_processor.py"

    # Check if scripts exist
    if not vision_processor.exists():
        print(f"❌ Vision processor not found: {vision_processor}")
        return False

    if not ffmpeg_processor.exists():
        print(f"❌ FFmpeg processor not found: {ffmpeg_processor}")
        return False

    if not description_processor.exists():
        print(f"❌ Description processor not found: {description_processor}")
        return False

    if not notes_processor.exists():
        print(f"❌ Notes processor not found: {notes_processor}")
        return False

    print(f"✅ Vision processor: {vision_processor}")
    print(f"✅ FFmpeg processor: {ffmpeg_processor}")
    print(f"✅ Description processor: {description_processor}")
    print(f"✅ Notes processor: {notes_processor}")
    
    try:
        # Start vision processor
        print("\n🔍 Starting Vision Processor...")
        vision_process = subprocess.Popen([
            sys.executable, str(vision_processor)
        ], cwd=str(current_dir))

        print(f"✅ Vision processor started (PID: {vision_process.pid})")

        # Start FFmpeg processor
        print("\n🎬 Starting FFmpeg Processor...")
        ffmpeg_process = subprocess.Popen([
            sys.executable, str(ffmpeg_processor)
        ], cwd=str(current_dir))

        print(f"✅ FFmpeg processor started (PID: {ffmpeg_process.pid})")

        # Start description processor
        print("\n📝 Starting Description Processor...")
        description_process = subprocess.Popen([
            sys.executable, str(description_processor)
        ], cwd=str(current_dir))

        print(f"✅ Description processor started (PID: {description_process.pid})")

        # Start notes processor
        print("\n📄 Starting Notes Processor...")
        notes_process = subprocess.Popen([
            sys.executable, str(notes_processor)
        ], cwd=str(current_dir))

        print(f"✅ Notes processor started (PID: {notes_process.pid})")

        print("\n🎯 All processors are running!")
        print("📁 Monitoring folders:")
        print("   - Vision requests: C:/Docker_Share/N8N/vision_requests")
        print("   - Vision results: C:/Docker_Share/N8N/vision_results")
        print("   - FFmpeg requests: C:/Docker_Share/N8N/ffmpeg_requests")
        print("   - FFmpeg results: C:/Docker_Share/N8N/ffmpeg_results")
        print("   - Description requests: C:/Docker_Share/N8N/description_requests")
        print("   - Description results: C:/Docker_Share/N8N/description_results")
        print("   - Notes generation data: C:/Docker_Share/N8N/logs")
        print("   - Notes output: C:/Docker_Share/N8N/notes")
        print("\n💡 Press Ctrl+C to stop all processors")
        
        # Wait for processes
        try:
            while True:
                # Check if processes are still running
                vision_running = vision_process.poll() is None
                ffmpeg_running = ffmpeg_process.poll() is None
                description_running = description_process.poll() is None
                notes_running = notes_process.poll() is None

                if not vision_running:
                    print("⚠️  Vision processor stopped unexpectedly")
                    break

                if not ffmpeg_running:
                    print("⚠️  FFmpeg processor stopped unexpectedly")
                    break

                if not description_running:
                    print("⚠️  Description processor stopped unexpectedly")
                    break

                if not notes_running:
                    print("⚠️  Notes processor stopped unexpectedly")
                    break

                time.sleep(1)

        except KeyboardInterrupt:
            print("\n🛑 Stopping processors...")

            # Terminate processes
            if vision_process.poll() is None:
                vision_process.terminate()
                print("✅ Vision processor stopped")

            if ffmpeg_process.poll() is None:
                ffmpeg_process.terminate()
                print("✅ FFmpeg processor stopped")

            if description_process.poll() is None:
                description_process.terminate()
                print("✅ Description processor stopped")

            if notes_process.poll() is None:
                notes_process.terminate()
                print("✅ Notes processor stopped")

            print("🏁 All processors stopped")
            
    except Exception as e:
        print(f"❌ Error starting processors: {e}")
        return False
    
    return True

def main():
    """Main function."""
    print("Kung Fu Video Detection - File Processors")
    print("=========================================")
    
    success = start_processors()
    
    if not success:
        print("\n❌ Failed to start processors")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
