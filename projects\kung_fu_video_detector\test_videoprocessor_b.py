#!/usr/bin/env python3
"""
Test VideoProcessor_B.json workflow with SplitInBatches fix
This script monitors the file processors and tests the complete workflow
"""

import os
import time
import json
import requests
from datetime import datetime

# Configuration
SHARED_FOLDER = "C:/Docker_Share/N8N"
N8N_WEBHOOK_URL = "http://localhost:5678/webhook/kung-fu-video-processor"  # Update with actual webhook URL
TEST_VIDEOS_PATH = "C:/Users/<USER>/source/Augment_Workspaces/N8N_Builder/projects/kung_fu_video_detector/test_videos"

def check_processors_running():
    """Check if all file processors are running by looking for their log files"""
    print("🔍 Checking if processors are running...")
    
    # Check for recent log activity (within last 30 seconds)
    log_files = [
        f"{SHARED_FOLDER}/ffmpeg_processor.log",
        "projects/kung_fu_video_detector/file_vision_processor.log",
        "projects/kung_fu_video_detector/file_description_processor.log", 
        "projects/kung_fu_video_detector/file_notes_processor.log"
    ]
    
    running_count = 0
    for log_file in log_files:
        if os.path.exists(log_file):
            # Check if file was modified recently (within 60 seconds)
            mod_time = os.path.getmtime(log_file)
            if time.time() - mod_time < 60:
                running_count += 1
                print(f"✅ {os.path.basename(log_file)} - Active")
            else:
                print(f"⚠️  {os.path.basename(log_file)} - Stale")
        else:
            print(f"❌ {os.path.basename(log_file)} - Not found")
    
    print(f"📊 {running_count}/4 processors appear to be running")
    return running_count >= 3  # At least 3 processors should be running

def clean_shared_folders():
    """Clean up any existing request/result files"""
    print("🧹 Cleaning shared folders...")
    
    folders_to_clean = [
        f"{SHARED_FOLDER}/ffmpeg_requests",
        f"{SHARED_FOLDER}/ffmpeg_results", 
        f"{SHARED_FOLDER}/vision_requests",
        f"{SHARED_FOLDER}/vision_results",
        f"{SHARED_FOLDER}/description_requests",
        f"{SHARED_FOLDER}/description_results"
    ]
    
    cleaned_count = 0
    for folder in folders_to_clean:
        if os.path.exists(folder):
            for file in os.listdir(folder):
                if file.endswith('.json'):
                    os.remove(os.path.join(folder, file))
                    cleaned_count += 1
    
    print(f"🗑️  Cleaned {cleaned_count} files from shared folders")

def list_test_videos():
    """List available test videos"""
    print("📹 Available test videos:")
    
    if not os.path.exists(TEST_VIDEOS_PATH):
        print(f"❌ Test videos directory not found: {TEST_VIDEOS_PATH}")
        return []
    
    videos = []
    for file in os.listdir(TEST_VIDEOS_PATH):
        if file.lower().endswith(('.mp4', '.avi', '.mov')):
            full_path = os.path.join(TEST_VIDEOS_PATH, file)
            size_mb = os.path.getsize(full_path) / (1024 * 1024)
            videos.append(file)
            print(f"  📁 {file} ({size_mb:.1f} MB)")
    
    print(f"📊 Total videos found: {len(videos)}")
    return videos

def monitor_processing():
    """Monitor the processing by checking shared folders"""
    print("👀 Monitoring processing...")
    
    start_time = time.time()
    max_wait_time = 300  # 5 minutes maximum
    
    folders_to_monitor = {
        "ffmpeg_requests": f"{SHARED_FOLDER}/ffmpeg_requests",
        "ffmpeg_results": f"{SHARED_FOLDER}/ffmpeg_results",
        "vision_requests": f"{SHARED_FOLDER}/vision_requests", 
        "vision_results": f"{SHARED_FOLDER}/vision_results",
        "description_requests": f"{SHARED_FOLDER}/description_requests",
        "description_results": f"{SHARED_FOLDER}/description_results",
        "notes": f"{SHARED_FOLDER}/notes"
    }
    
    last_counts = {name: 0 for name in folders_to_monitor}
    
    while time.time() - start_time < max_wait_time:
        current_counts = {}
        activity_detected = False
        
        for name, folder in folders_to_monitor.items():
            if os.path.exists(folder):
                count = len([f for f in os.listdir(folder) if f.endswith('.json') or f.endswith('.txt')])
                current_counts[name] = count
                
                if count != last_counts[name]:
                    activity_detected = True
                    if count > last_counts[name]:
                        print(f"📈 {name}: {last_counts[name]} → {count} files")
                    else:
                        print(f"📉 {name}: {last_counts[name]} → {count} files")
            else:
                current_counts[name] = 0
        
        # Check for completion indicators
        if current_counts.get("notes", 0) > 0:
            print("🎉 Notes files detected - Processing appears complete!")
            break
            
        if activity_detected:
            print(f"⏱️  Processing active... ({int(time.time() - start_time)}s elapsed)")
        
        last_counts = current_counts
        time.sleep(2)
    
    elapsed = int(time.time() - start_time)
    print(f"⏰ Monitoring completed after {elapsed} seconds")
    
    return current_counts

def check_results():
    """Check the final results"""
    print("📋 Checking results...")
    
    # Check notes files
    notes_folder = f"{SHARED_FOLDER}/notes"
    if os.path.exists(notes_folder):
        notes_files = [f for f in os.listdir(notes_folder) if f.endswith('.txt')]
        print(f"📝 Notes files created: {len(notes_files)}")
        for file in notes_files:
            print(f"  📄 {file}")
            
            # Show first few lines of each notes file
            file_path = os.path.join(notes_folder, file)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()[:3]  # First 3 lines
                    for line in lines:
                        print(f"    {line.strip()}")
                    if len(lines) > 3:
                        print("    ...")
            except Exception as e:
                print(f"    Error reading file: {e}")
    else:
        print("❌ Notes folder not found")
    
    # Check processor results
    result_folders = ["ffmpeg_results", "vision_results", "description_results"]
    for folder_name in result_folders:
        folder_path = f"{SHARED_FOLDER}/{folder_name}"
        if os.path.exists(folder_path):
            count = len([f for f in os.listdir(folder_path) if f.endswith('.json')])
            print(f"📊 {folder_name}: {count} result files")
        else:
            print(f"❌ {folder_name} folder not found")

def main():
    """Main test function"""
    print("🚀 Testing VideoProcessor_B.json with SplitInBatches Fix")
    print("=" * 60)
    
    # Step 1: Check processors
    if not check_processors_running():
        print("❌ Not all processors are running. Please start them first:")
        print("   cd projects/kung_fu_video_detector")
        print("   python start_file_processors.py")
        return
    
    # Step 2: List test videos
    videos = list_test_videos()
    if not videos:
        print("❌ No test videos found")
        return
    
    # Step 3: Clean shared folders
    clean_shared_folders()
    
    # Step 4: Instructions for manual workflow trigger
    print("\n🎯 NEXT STEPS:")
    print("1. Open N8N in your browser (http://localhost:5678)")
    print("2. Import VideoProcessor_B.json workflow")
    print("3. Click 'Execute Workflow' to trigger the manual trigger")
    print("4. This script will monitor the processing...")
    
    input("\nPress Enter when you've triggered the workflow in N8N...")
    
    # Step 5: Monitor processing
    results = monitor_processing()
    
    # Step 6: Check results
    check_results()
    
    print("\n✅ Test completed!")
    print("Expected success indicators:")
    print("- All 5 videos processed through FFmpeg, Vision, and Description")
    print("- Notes files created with AI analysis results")
    print("- Processing time: 2-3 minutes (not 1 second)")

if __name__ == "__main__":
    main()
