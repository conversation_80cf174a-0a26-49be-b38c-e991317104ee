#!/usr/bin/env python3
"""Debug why FFmpeg is failing in N8N workflow but working in direct tests"""

import subprocess
import json
from pathlib import Path

def debug_ffmpeg_execution():
    """Debug FFmpeg execution issues."""
    print("🔍 DEBUGGING FFMPEG EXECUTION IN N8N WORKFLOW")
    print("=" * 60)
    
    # Test 1: Direct FFmpeg command (we know this works)
    print("1. Testing direct FFmpeg command...")
    try:
        result = subprocess.run([
            "powershell", "-Command", 
            "docker exec n8n sh -c 'ffmpeg -i /home/<USER>/shared/kung_fu_videos/20250406_110016_1.mp4 -ss 00:00:10 -vframes 1 -f image2pipe -vcodec png - 2>/dev/null | base64 -w 0 | head -c 100'"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and result.stdout.strip():
            print(f"✅ Direct FFmpeg works: {len(result.stdout.strip())} chars")
            print(f"   Preview: {result.stdout.strip()[:50]}...")
        else:
            print(f"❌ Direct FFmpeg failed: {result.stderr}")
    except Exception as e:
        print(f"❌ Direct FFmpeg test error: {e}")
    
    # Test 2: Check if the issue is with N8N templating
    print("\n2. Testing N8N executeCommand format...")
    test_command = 'ffmpeg -i "/home/<USER>/shared/kung_fu_videos/20250406_110016_1.mp4" -ss 00:00:10 -vframes 1 -f image2pipe -vcodec png - 2>/dev/null | base64 -w 0'
    
    try:
        result = subprocess.run([
            "powershell", "-Command", 
            f"docker exec n8n sh -c '{test_command} | head -c 100'"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and result.stdout.strip():
            print(f"✅ N8N format works: {len(result.stdout.strip())} chars")
            print(f"   Preview: {result.stdout.strip()[:50]}...")
        else:
            print(f"❌ N8N format failed: {result.stderr}")
    except Exception as e:
        print(f"❌ N8N format test error: {e}")
    
    # Test 3: Check if the issue is with exit codes
    print("\n3. Testing exit code handling...")
    try:
        result = subprocess.run([
            "powershell", "-Command", 
            f"docker exec n8n sh -c '{test_command}; echo EXIT_CODE:$?'"
        ], capture_output=True, text=True, timeout=30)
        
        print(f"Exit code test result: {result.returncode}")
        if "EXIT_CODE:" in result.stdout:
            exit_code = result.stdout.split("EXIT_CODE:")[-1].strip()
            print(f"FFmpeg exit code: {exit_code}")
        else:
            print("Could not determine FFmpeg exit code")
    except Exception as e:
        print(f"❌ Exit code test error: {e}")
    
    # Test 4: Check the actual log files for clues
    print("\n4. Checking recent log files for clues...")
    log_files = [
        "C:/Docker_Share/N8N/2_scan_log.json",
        "C:/Docker_Share/N8N/3_process_log.json"
    ]
    
    for log_file in log_files:
        log_path = Path(log_file)
        if log_path.exists():
            try:
                with open(log_path, 'r') as f:
                    log_data = json.load(f)
                print(f"✅ {log_path.name}:")
                if 'files' in log_data:
                    for file_info in log_data['files'][:2]:
                        print(f"   File: {file_info.get('filename', 'unknown')}")
                        print(f"   Path: {file_info.get('fullPath', 'unknown')}")
            except Exception as e:
                print(f"❌ Error reading {log_file}: {e}")
        else:
            print(f"❌ {log_file} not found")

def suggest_solutions():
    """Suggest potential solutions based on findings."""
    print("\n🔧 POTENTIAL SOLUTIONS")
    print("=" * 30)
    
    print("1. **Command Format Issue:**")
    print("   - N8N might not handle complex shell commands with pipes")
    print("   - Try splitting into separate nodes")
    print()
    
    print("2. **Templating Issue:**")
    print("   - {{ $json.fullPath }} might not be resolving correctly")
    print("   - Check if fullPath contains the correct file path")
    print()
    
    print("3. **Exit Code Issue:**")
    print("   - FFmpeg might be returning non-zero exit code even on success")
    print("   - Adjust exit code validation in Process Thumbnail node")
    print()
    
    print("4. **Pipe/Redirect Issue:**")
    print("   - Complex shell pipes might not work in N8N executeCommand")
    print("   - Consider using a shell script file instead")

if __name__ == "__main__":
    debug_ffmpeg_execution()
    suggest_solutions()
