# Setup Docker Share for Kung Fu Video Detector
# Following the DNS reports pattern

Write-Host "Setting up Docker Share for Kung Fu Video Detector" -ForegroundColor Cyan
Write-Host "=" * 50

# Create kung fu videos folder in Docker share (following DNS reports pattern)
$dockerSharePath = 'C:\Docker_Share\N8N\kung_fu_videos'
Write-Host "Creating Docker share folder for kung fu videos..." -ForegroundColor Yellow

if (-not (Test-Path $dockerSharePath)) {
    New-Item -ItemType Directory -Path $dockerSharePath -Force | Out-Null
    Write-Host "Created Docker share folder: $dockerSharePath" -ForegroundColor Green
} else {
    Write-Host "Docker share folder exists: $dockerSharePath" -ForegroundColor Yellow
}

# Copy test videos from project folder to Docker share
$sourceFolder = 'test_videos'
if (Test-Path $sourceFolder) {
    $videoFiles = Get-ChildItem -Path $sourceFolder -Filter '*.mp4'
    Write-Host "Copying video files to Docker share..." -ForegroundColor Yellow
    
    foreach ($file in $videoFiles) {
        $destPath = Join-Path $dockerSharePath $file.Name
        Copy-Item $file.FullName $destPath -Force
        Write-Host "Copied: $($file.Name)" -ForegroundColor Green
    }
    Write-Host "Copied $($videoFiles.Count) video files to Docker share" -ForegroundColor Green
} else {
    Write-Host "Source folder not found: $sourceFolder" -ForegroundColor Red
}

# List contents of Docker share folder
Write-Host ""
Write-Host "Contents of Docker share kung_fu_videos folder:" -ForegroundColor Cyan
if (Test-Path $dockerSharePath) {
    Get-ChildItem -Path $dockerSharePath | ForEach-Object { 
        $sizeKB = [math]::Round($_.Length/1KB, 2)
        Write-Host "   - $($_.Name) ($sizeKB KB)" -ForegroundColor White
    }
} else {
    Write-Host "   No files found" -ForegroundColor Red
}

Write-Host ""
Write-Host "Docker share setup complete!" -ForegroundColor Green
Write-Host "Videos are now available in N8N container at: /home/<USER>/shared/kung_fu_videos/" -ForegroundColor Cyan
