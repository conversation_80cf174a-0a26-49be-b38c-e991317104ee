{"name": "N8N Proxy Test", "nodes": [{"parameters": {}, "id": "manual-trigger-proxy-test", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"method": "GET", "url": "http://host.docker.internal:8081/health", "options": {"timeout": 10000}}, "id": "proxy-health-check", "name": "Proxy Health Check", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [450, 200]}, {"parameters": {"method": "POST", "url": "http://host.docker.internal:8081/simple-test", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyContentType": "raw", "body": "{\"text\": \"Hello from N8N, please respond with OK\"}", "options": {"timeout": 30000}}, "id": "proxy-simple-test", "name": "Proxy Simple Test", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [450, 400]}], "connections": {"Manual Trigger": {"main": [[{"node": "Proxy Health Check", "type": "main", "index": 0}, {"node": "Proxy Simple Test", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}