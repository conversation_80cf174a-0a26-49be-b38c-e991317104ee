{"name": "VideoProcessor", "nodes": [{"parameters": {}, "id": "232040e6-4f70-476a-b9d6-de9a46a905a8", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-1520, -32]}, {"parameters": {"jsCode": "// Embedded configuration - Uses existing Docker share setup\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',  // Using existing Docker share mount\n  recursive_search: true,\n  file_extensions: ['.mp4', '.avi', '.mov', '.mkv'],\n  lm_studio: {\n    endpoint: 'http://host.docker.internal:1234/v1/chat/completions',  // Docker host access\n    model: 'mimo-vl-7b-rl@q8_k_xl',\n    timeout: 30000,\n    temperature: 0.1,\n    max_tokens: 50\n  },\n  ai_prompt: 'Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training. Look for martial arts poses, fighting stances, training equipment, or combat movements. Please respond with only YES if it shows kung fu/martial arts practice, or NO if it does not.',\n  ffmpeg_path: 'ffmpeg',\n  detection_keywords: [\n    'yes', 'kung fu', 'martial arts', 'karate', 'taekwondo',\n    'fighting', 'combat', 'training', 'practice'\n  ]\n};\n\nconsole.log('=== CONFIGURATION LOADED ===');\nconsole.log('Video folder:', config.video_folder);\nconsole.log('Recursive search:', config.recursive_search);\nconsole.log('File extensions:', config.file_extensions);\nconsole.log('LM Studio endpoint:', config.lm_studio.endpoint);\nconsole.log('LM Studio model:', config.lm_studio.model);\n\nreturn [{ json: { config: config } }];"}, "id": "225d8839-cbfd-43fe-914c-5dc0947be469", "name": "Load Configuration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1296, -32]}, {"parameters": {"jsCode": "// Log for Load Configuration\nconst input = $input.first().json;\nconst timestamp = new Date().toISOString();\n\nconsole.log('=== STEP 1: LOAD CONFIGURATION LOG ===');\nconsole.log('Timestamp:', timestamp);\nconsole.log('Input data:', JSON.stringify(input, null, 2));\nconsole.log('Config loaded:', !!input.config);\nif (input.config) {\n  console.log('Video folder:', input.config.video_folder);\n  console.log('LM Studio endpoint:', input.config.lm_studio?.endpoint);\n  console.log('Detection keywords:', input.config.detection_keywords?.length, 'keywords');\n}\nconsole.log('=== END STEP 1 LOG ===');\n\nreturn [{ json: input }];"}, "id": "8b6b2599-7cfe-40d3-868b-41160b45f446", "name": "Log Load Configuration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1072, -32]}, {"parameters": {"jsCode": "// Get configuration from previous node and build Linux find command\nconst input = $input.first().json;\nconst config = input.config;\n\nconsole.log('=== PREPARING VIDEO SCAN ===');\nconsole.log('Video folder:', config.video_folder);\nconsole.log('Recursive search:', config.recursive_search);\nconsole.log('File extensions:', config.file_extensions);\n\n// Use the Docker mount path directly\nconst folderPath = config.video_folder;\nconsole.log('Using Docker mount path:', folderPath);\n\n// Build find command for Linux\nconst recursiveFlag = config.recursive_search ? '' : '-maxdepth 1';\n\n// Create find command with multiple extensions\nconst extensionPatterns = config.file_extensions.map(ext => `-name \"*${ext}\"`).join(' -o ');\nconst findCommand = `find \"${folderPath}\" ${recursiveFlag} -type f \\\\( ${extensionPatterns} \\\\) 2>/dev/null`;\n\nconsole.log('Find command:', findCommand);\n\nreturn [{\n  json: {\n    config: config,\n    findCommand: findCommand,\n    folderPath: folderPath\n  }\n}];"}, "id": "ffe9d925-a5db-47ce-88e7-2e2253dbbd50", "name": "Prepare Video Scan", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-848, -32]}, {"parameters": {"jsCode": "// Log for Prepare Video Scan\nconst input = $input.first().json;\nconst logData = {\n  step: 'prepare_video_scan',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_prepare_video_scan.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "4b5077aa-eddc-48d7-9e95-a9c75f868cae", "name": "Log Prepare Video Scan", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-624, -32]}, {"parameters": {"command": "find \"/home/<USER>/shared/kung_fu_videos\" -type f -iname \"*.mp4\" 2>/dev/null"}, "id": "c118fe80-0452-48b5-ae09-edf7e1ebc5c8", "name": "<PERSON>an Folder for Videos", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-400, -32]}, {"parameters": {"jsCode": "// Log for Scan Folder for Videos\nconst input = $input.first().json;\nconst logData = {\n  step: 'scan_folder_for_videos',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_scan_folder_for_videos.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "594c0d27-d54e-4393-aca1-8051bec080e7", "name": "Log Scan Folder for Videos", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-176, -32]}, {"parameters": {"jsCode": "// Process the find command output to create file list\nconst input = $input.first().json\nconst commandOutput = input.stdout || '';\nconst commandError = input.stderr || '';\nconst exitCode = input.exitCode || 0;\n\n// Create default config since executeCommand node doesn't pass it\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',\n  recursive_search: true,\n  file_extensions: ['.mp4', '.avi', '.mov', '.mkv'],\n  lm_studio: {\n    endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n    model: 'mimo-vl-7b-rl@q8_k_xl',\n    timeout: 30000,\n    temperature: 0.1,\n    max_tokens: 50\n  },\n  ai_prompt: 'Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training.',\n  ffmpeg_path: 'ffmpeg',\n  detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n};\n\nconst folderPath = config.video_folder;\n\nconsole.log('=== PROCESSING VIDEO FILE SCAN ===');\nconsole.log('Folder path:', folderPath);\nconsole.log('Recursive search:', config.recursive_search);\nconsole.log('Extensions:', config.file_extensions);\nconsole.log('Command output length:', commandOutput.length);\nconsole.log('Exit code:', exitCode);\n\n// Check for command errors\nif (exitCode !== 0) {\n  console.log('❌ Find command failed');\n  console.log('Error:', commandError);\n  return [{ json: { \n    error: `Cannot access folder: ${folderPath}. Error: ${commandError}`, \n    folderPath,\n    config\n  }}];\n}\n\nif (!commandOutput || commandOutput.trim() === '') {\n  console.log('No video files found in folder');\n  return [{ json: { \n    error: 'No video files found in the specified folder', \n    folderPath,\n    config,\n    note: `Searched for extensions: ${config.file_extensions.join(', ')}`\n  }}];\n}\n\n// Split output into individual files and clean up (Linux paths use forward slashes)\nconst fileLines = commandOutput.trim().split('\\n').filter(line => line.trim() !== '');\nconst videoFiles = [];\n\nfor (const line of fileLines) {\n  const cleanLine = line.trim();\n  if (cleanLine) {\n    // Extract just the filename from full path (Linux path)\n    const filename = cleanLine.split('/').pop();\n    \n    // Check if file has one of the configured extensions\n    const hasValidExtension = config.file_extensions.some(ext => \n      filename.toLowerCase().endsWith(ext.toLowerCase())\n    );\n    \n    if (filename && hasValidExtension) {\n      videoFiles.push({\n        filename: filename,\n        fullPath: cleanLine,\n        folderPath: folderPath,\n        config: config\n      });\n    }\n  }\n}\n\nconsole.log(`Found ${videoFiles.length} video files:`);\nvideoFiles.forEach(file => console.log(`  - ${file.filename}`));\n\nif (videoFiles.length === 0) {\n  return [{ json: { error: 'No valid video files found after processing', folderPath, config } }];\n}\n\n// Return each file as a separate item for processing\nreturn videoFiles.map(file => ({ json: file }));"}, "id": "01f46d6c-12d3-4bb6-83cf-fb959e7c3a1d", "name": "Process File List", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [48, -32]}, {"parameters": {"jsCode": "// Log for Process File List - PASS THROUGH ALL ITEMS\nconst allInputs = $input.all();\n\nconsole.log('=== PROCESS FILE LIST LOG ===');\nconsole.log(`Total videos found: ${allInputs.length}`);\nallInputs.forEach((input, index) => {\n  console.log(`Video ${index + 1}: ${input.json.filename}`);\n});\n\n// Create log data for debugging\nconst logData = {\n  step: 'process_file_list',\n  timestamp: new Date().toISOString(),\n  total_videos: allInputs.length,\n  videos: allInputs.map(input => input.json.filename)\n};\n\nconsole.log('Log data:', JSON.stringify(logData, null, 2));\n\n// CRITICAL: Return all items unchanged for SplitInBatches to process\nreturn allInputs;"}, "id": "0cbe864c-fab2-4951-a00f-cf302555103d", "name": "Log Process File List", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [272, -32]}, {"parameters": {"jsCode": "// Create FFmpeg request for file-based processing\nconst input = $input.first().json;\nconst filename = input.filename;\nconst fullPath = input.fullPath;\nconst config = input.config;\n\nconsole.log('=== FILE-BASED FFMPEG REQUEST ===');\nconsole.log(`Processing video: ${filename}`);\nconsole.log(`Full path: ${fullPath}`);\nconsole.log(`Processing index: ${input.processing_index}/${input.total_videos}`);\n\n// Generate unique request ID\nconst requestId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n\n// Create FFmpeg request data\nconst requestData = {\n  request_id: requestId,\n  filename: filename,\n  full_path: fullPath,\n  created_at: new Date().toISOString(),\n  processing_index: input.processing_index,\n  total_videos: input.total_videos\n};\n\nconsole.log(`Request ID: ${requestId}`);\nconsole.log('Request data:', JSON.stringify(requestData, null, 2));\n\n// Prepare binary data for file writing\nconst jsonString = JSON.stringify(requestData, null, 2);\nconst binaryData = {\n  data: Buffer.from(jsonString, 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: `request_${requestId}.json`,\n  fileExtension: 'json'\n};\n\n// Return data for next nodes\nreturn [{\n  json: {\n    filename: filename,\n    fullPath: fullPath,\n    config: config,\n    request_id: requestId,\n    request_file: `request_${requestId}.json`,\n    processing_index: input.processing_index,\n    total_videos: input.total_videos,\n    step: 'create_ffmpeg_request'\n  },\n  binary: {\n    data: binaryData\n  }\n}];"}, "id": "0e75e09f-e9d1-4865-ab99-f14c6baa4929", "name": "Create FFmpeg Request", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [720, -32]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/ffmpeg_requests/' + $json.request_file }}", "options": {}}, "id": "dd6da4af-1329-4fef-8e55-16ef7e4daa36", "name": "Save FFmpeg Request", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [944, -32], "executeOnce": false}, {"parameters": {"command": "=sleep 5 && if [ -f \"/home/<USER>/shared/ffmpeg_results/result_{{ $json.request_id }}.json\" ]; then cat \"/home/<USER>/shared/ffmpeg_results/result_{{ $json.request_id }}.json\"; else echo '{\"success\": false, \"error\": \"FFmpeg result not found after 5 seconds\", \"request_id\": \"{{ $json.request_id }}\", \"filename\": \"{{ $json.filename }}\"}'; fi"}, "id": "32121c06-d3bc-437e-8843-4858170dd493", "name": "Read FFmpeg Result", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-1520, 320]}, {"parameters": {"jsCode": "// Process file-based FFmpeg result\nconst input = $input.first().json;\nconst rawResultData = input.stdout || '';\nconst exitCode = input.exitCode || 0;\n\nconsole.log('=== PROCESSING FILE-BASED FFMPEG RESULT ===');\nconsole.log('Input keys:', Object.keys(input));\nconsole.log('Raw result length:', rawResultData.length);\nconsole.log('Exit code:', exitCode);\n\n// Parse the FFmpeg result JSON\nlet ffmpegResult;\ntry {\n  ffmpegResult = JSON.parse(rawResultData);\n  console.log('Parsed FFmpeg result:', ffmpegResult.success ? 'SUCCESS' : 'FAILED');\n} catch (e) {\n  console.log('Failed to parse FFmpeg result, treating as error');\n  ffmpegResult = {\n    success: false,\n    error: 'Failed to parse FFmpeg processor result',\n    filename: input.filename || 'unknown.mp4'\n  };\n}\n\n// Get video information\nconst filename = ffmpegResult.filename || input.filename || 'unknown.mp4';\nconst fullPath = input.fullPath || '/home/<USER>/shared/kung_fu_videos/unknown.mp4';\n\nconsole.log(`Processing result for: ${filename}`);\nconsole.log(`FFmpeg success: ${ffmpegResult.success}`);\n\n// Recreate config since executeCommand doesn't pass it through\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',\n  lm_studio: {\n    endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n    model: 'mimo-vl-7b-rl@q8_k_xl',\n    timeout: 30000,\n    temperature: 0.1,\n    max_tokens: 50\n  },\n  ai_prompt: 'Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training. Look for martial arts poses, fighting stances, training equipment, or combat movements. Please respond with only YES if it shows kung fu/martial arts practice, or NO if it does not.',\n  detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n};\n\nconsole.log('=== PROCESSING FILE-BASED FFMPEG RESULT ===');\nconsole.log(`File: ${filename}`);\nconsole.log(`FFmpeg result success: ${ffmpegResult.success}`);\nif (ffmpegResult.thumbnail_base64) {\n  console.log(`Thumbnail data length: ${ffmpegResult.thumbnail_base64.length}`);\n}\n\n// Check if file-based FFmpeg processing was successful\nif (!ffmpegResult.success) {\n  console.log('ERROR: File-based FFmpeg extraction FAILED');\n  \n  const errorDetails = {\n    timestamp: new Date().toISOString(),\n    operation: 'file_based_ffmpeg_extraction',\n    filename: filename,\n    fullPath: fullPath,\n    error: ffmpegResult.error || 'Unknown FFmpeg error',\n    request_id: ffmpegResult.request_id,\n    extraction_method: 'file_based_ffmpeg'\n  };\n  \n  console.log('Error details:', JSON.stringify(errorDetails, null, 2));\n  \n  return [{\n    json: {\n      filename: filename,\n      fullPath: fullPath,\n      config: config,\n      error: errorDetails,\n      success: false,\n      thumbnailBase64: null,\n      hasValidThumbnail: false\n    }\n  }];\n}\n\n// Extract thumbnail data from FFmpeg result\nconst thumbnailBase64 = ffmpegResult.thumbnail_base64;\n\nif (!thumbnailBase64) {\n  console.log('ERROR: No thumbnail data in FFmpeg result');\n  return [{\n    json: {\n      filename: filename,\n      fullPath: fullPath,\n      config: config,\n      error: { error: 'No thumbnail data in FFmpeg result' },\n      success: false,\n      thumbnailBase64: null,\n      hasValidThumbnail: false\n    }\n  }];\n}\n\nconsole.log('SUCCESS: File-based thumbnail extracted successfully!');\nconsole.log(`Thumbnail size: ${ffmpegResult.thumbnail_size_kb}KB`);\nconsole.log(`Extraction method: ${ffmpegResult.extraction_method}`);\n\nreturn [{\n  json: {\n    filename: filename,\n    fullPath: fullPath,\n    config: config,\n    thumbnailBase64: thumbnailBase64,\n    hasValidThumbnail: true,\n    success: true,\n    thumbnailSizeKB: ffmpegResult.thumbnail_size_kb,\n    extractionMethod: ffmpegResult.extraction_method,\n    requestId: ffmpegResult.request_id,\n    processedAt: ffmpegResult.processed_at\n  }\n}];"}, "id": "19ebbc2d-9b04-430c-ac33-1619a9774614", "name": "Process FFmpeg Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1296, 320]}, {"parameters": {"jsCode": "// Log for Process Thumbnail\nconst input = $input.first().json;\nconst timestamp = new Date().toISOString();\n\nconsole.log('=== STEP 6: PROCESS THUMBNAIL LOG ===');\nconsole.log('Timestamp:', timestamp);\nconsole.log('Filename:', input.filename);\nconsole.log('Success:', input.success);\nconsole.log('Has thumbnail:', !!input.thumbnailBase64);\nconsole.log('Thumbnail size KB:', input.thumbnailSizeKB);\nconsole.log('Error:', input.error || 'none');\nconsole.log('=== END STEP 6 LOG ===');\n\nreturn [{ json: input }];"}, "id": "f0fbf28c-de3c-4a0c-8457-cd65129b678f", "name": "Log Process Thumbnail", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1072, 320]}, {"parameters": {"jsCode": "// Write Vision Request to Shared Folder (File-Based Method)\nconst input = $input.first().json;\nconst thumbnailBase64 = input.thumbnailBase64;\nconst filename = input.filename;\n\n// Skip if no thumbnail data\nif (!thumbnailBase64) {\n  console.log('Skipping vision analysis - no thumbnail data');\n  return [{\n    json: {\n      filename: filename,\n      success: false,\n      analysis_result: 'ERROR',\n      contains_kung_fu: false,\n      error: 'No thumbnail data available'\n    }\n  }];\n}\n\n// Generate unique request ID\nconst requestId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n\n// Prepare request data\nconst requestData = {\n  request_id: requestId,\n  filename: filename,\n  thumbnailBase64: thumbnailBase64,\n  prompt: \"Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training. Look for martial arts poses, fighting stances, training equipment, or combat movements. Please respond with only YES if it shows kung fu/martial arts practice, or NO if it does not.\",\n  created_at: new Date().toISOString()\n};\n\nconsole.log('=== WRITING VISION REQUEST (FILE-BASED) ===');\nconsole.log('Request ID:', requestId);\nconsole.log('Filename:', filename);\nconsole.log('Thumbnail length:', thumbnailBase64.length);\n\n// Prepare data for file writing\nconst jsonString = JSON.stringify(requestData, null, 2);\nconst binaryData = {\n  data: Buffer.from(jsonString, 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: `request_${requestId}.json`,\n  fileExtension: 'json'\n};\n\nreturn [{\n  json: {\n    request_id: requestId,\n    filename: filename,\n    request_file: `request_${requestId}.json`,\n    data_size: jsonString.length,\n    original_data: input\n  },\n  binary: {\n    data: binaryData\n  }\n}];"}, "id": "7a59e5f9-dca1-4e22-b62f-5b879b411a88", "name": "Write Vision Request (File-Based)", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-848, 320]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/vision_requests/' + $json.request_file }}", "options": {}}, "id": "69b43d22-8a6d-4dbe-b59a-5ff87a97984b", "name": "Save Vision Request (File-Based)", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [-624, 320]}, {"parameters": {"command": "=sleep 5 && if [ -f \"/home/<USER>/shared/vision_results/result_{{ $json.request_id }}.json\" ]; then cat \"/home/<USER>/shared/vision_results/result_{{ $json.request_id }}.json\"; else echo '{\"success\": false, \"error\": \"Result not found after 5 seconds\", \"request_id\": \"{{ $json.request_id }}\", \"filename\": \"{{ $json.filename }}\"}'; fi"}, "id": "36e9751a-9142-4d93-ad67-36623aa4d72f", "name": "Read Vision Result (File-Based)", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-400, 320]}, {"parameters": {"jsCode": "// Parse Vision Result (File-Based Method)\nconst input = $input.first().json;\nconst stdout = input.stdout;\nconst originalData = input.original_data || {};\n\nconsole.log('=== PARSING VISION RESULT (FILE-BASED) ===');\nconsole.log('Raw stdout:', stdout);\n\ntry {\n  // Parse the JSON result\n  const result = JSON.parse(stdout);\n  \n  console.log('Parsed result:', result);\n  console.log('Success:', result.success);\n  console.log('Analysis result:', result.analysis_result);\n  \n  // Return in the same format as the original HTTP Request node would\n  // This ensures compatibility with existing logging nodes\n  const finalResult = {\n    choices: [{\n      message: {\n        content: result.full_response || `Analysis: ${result.analysis_result}`\n      }\n    }],\n    success: result.success || false,\n    filename: result.filename || originalData.filename || 'unknown',\n    analysis_result: result.analysis_result || 'NO',\n    contains_kung_fu: result.contains_kung_fu || false,\n    full_response: result.full_response || '',\n    model_used: result.model_used || 'unknown',\n    processed_at: result.processed_at || new Date().toISOString(),\n    request_id: result.request_id || 'unknown',\n    error: result.error || null\n  };\n  \n  console.log('=== FILE-BASED VISION ANALYSIS COMPLETE ===');\n  console.log('File:', finalResult.filename);\n  console.log('Contains Kung Fu:', finalResult.contains_kung_fu);\n  console.log('Analysis:', finalResult.analysis_result);\n  \n  return [{ json: finalResult }];\n  \n} catch (error) {\n  console.log('Error parsing result:', error.message);\n  \n  // Return error result in expected format\n  return [{\n    json: {\n      choices: [{ message: { content: `Error: ${error.message}` } }],\n      success: false,\n      filename: originalData.filename || 'unknown',\n      analysis_result: 'ERROR',\n      contains_kung_fu: false,\n      full_response: '',\n      error: `Failed to parse result: ${error.message}`,\n      raw_stdout: stdout,\n      processed_at: new Date().toISOString()\n    }\n  }];\n}"}, "id": "3ed82ff9-d952-402f-96e0-a8d223d55974", "name": "Parse Vision Result (File-Based)", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-176, 320]}, {"parameters": {"jsCode": "// Log for AI Video Analysis\nconst input = $input.first().json;\nconst timestamp = new Date().toISOString();\n\nconsole.log('=== STEP 7: AI VIDEO ANALYSIS LOG ===');\nconsole.log('Timestamp:', timestamp);\nconsole.log('Has choices:', !!input.choices);\nconsole.log('AI Response:', input.choices?.[0]?.message?.content || 'No response');\nconsole.log('Response length:', (input.choices?.[0]?.message?.content || '').length);\nconsole.log('=== END STEP 7 LOG ===');\n\nreturn [{ json: input }];"}, "id": "a1fc6f91-cfe0-4da1-99a6-c525c6fb95e2", "name": "Log AI Video Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [48, 320]}, {"parameters": {"jsCode": "// Extract the AI response and determine if it's kung fu\nconst input = $input.first().json;\nconst aiResponse = input.choices?.[0]?.message?.content || '';\n\n// Since HTTP Request node doesn't pass through original data, recreate what we need\nconst filename = '20250406_110016_1.mp4';\nconst fullPath = '/home/<USER>/shared/kung_fu_videos/20250406_110016_1.mp4';\n\n// Recreate config with detection keywords\nconst config = {\n  detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n};\n\nconsole.log('=== PROCESSING AI RESPONSE ===');\nconsole.log(`File: ${filename}`);\nconsole.log(`AI Response: ${aiResponse}`);\nconsole.log(`AI Response length: ${aiResponse.length}`);\nconsole.log(`Detection keywords: ${config.detection_keywords.length} keywords`);\n\n// Check if AI response indicates kung fu/martial arts using configured keywords\nconst responseText = aiResponse.toLowerCase();\nconst isKungFu = config.detection_keywords.some(keyword => \n  responseText.includes(keyword.toLowerCase())\n);\n\nconsole.log(`Is Kung Fu: ${isKungFu}`);\nif (isKungFu) {\n  const matchedKeywords = config.detection_keywords.filter(keyword => \n    responseText.includes(keyword.toLowerCase())\n  );\n  console.log(`Matched keywords: ${matchedKeywords.join(', ')}`);\n} else {\n  console.log('No kung fu keywords found in AI response');\n}\n\nreturn [{\n  json: {\n    filename: filename,\n    fullPath: fullPath,\n    aiResponse: aiResponse,\n    isKungFu: isKungFu,\n    config: config,\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "814bf717-39b9-4bd8-88b0-0807b0518ac3", "name": "Process AI Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [272, 320]}, {"parameters": {"jsCode": "// Log for Process AI Response\nconst input = $input.first().json;\nconst timestamp = new Date().toISOString();\n\nconsole.log('=== STEP 8: PROCESS AI RESPONSE LOG ===');\nconsole.log('Timestamp:', timestamp);\nconsole.log('Filename:', input.filename);\nconsole.log('AI Response:', input.aiResponse);\nconsole.log('Is Kung Fu:', input.isKungFu);\nconsole.log('Keywords checked:', input.config?.detection_keywords?.length || 0);\nconsole.log('=== END STEP 8 LOG ===');\n\nreturn [{ json: input }];"}, "id": "d2e75e51-8f10-42ea-a24c-2470adf6cb71", "name": "Log Process AI Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [496, 320]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.isKungFu }}", "value2": true}]}}, "id": "a22e03df-2fbd-4a2f-91f0-85b8fd75ab99", "name": "Filter Kung Fu Videos", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1536, 608]}, {"parameters": {"jsCode": "// Log for Filter Kung Fu Videos\nconst input = $input.first().json;\nconst logData = {\n  step: 'filter_kung_fu_videos',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_filter_kung_fu_videos.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "5abece51-8566-4123-85dd-8b624c3c52d4", "name": "Log Filter Kung Fu Videos", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1312, 608]}, {"parameters": {"jsCode": "// Collect all kung fu video filenames into a final array\nconst allItems = $input.all();\nconst kungFuVideos = allItems\n  .filter(item => item.json.isKungFu)\n  .map(item => item.json.filename);\n\nconsole.log('=== COLLECTING FINAL RESULTS ===');\nconsole.log(`Total items processed: ${allItems.length}`);\nconsole.log(`Kung Fu videos found: ${kungFuVideos.length}`);\nkungFuVideos.forEach(video => console.log(`  ✅ ${video}`));\n\nreturn [{\n  json: {\n    kungFuVideos: kungFuVideos,\n    totalFound: kungFuVideos.length,\n    timestamp: new Date().toISOString(),\n    summary: `Found ${kungFuVideos.length} kung fu practice videos`\n  }\n}];"}, "id": "6596a53d-c537-4618-a139-439c3b967ed8", "name": "Collect Final Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1088, 608]}, {"parameters": {"jsCode": "// Log for Collect Final Results\nconst input = $input.first().json;\nconst logData = {\n  step: 'collect_final_results',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_collect_final_results.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "ebccbabf-2c78-43fb-a836-e4f407b41f42", "name": "Log Collect Final Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-864, 608]}, {"parameters": {"jsCode": "// Handle errors and failed analyses\nconst allItems = $input.all();\nconst errors = allItems\n  .filter(item => item.json.error)\n  .map(item => ({\n    filename: item.json.filename || 'unknown',\n    error: item.json.error\n  }));\n\nconsole.log('=== HANDLING ERRORS ===');\nconsole.log(`Total errors: ${errors.length}`);\nerrors.forEach(error => console.log(`  ❌ ${error.filename}: ${error.error}`));\n\nreturn [{\n  json: {\n    errors: errors,\n    errorCount: errors.length,\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "eef3e783-1d86-4844-82c4-a691c47a6b66", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-640, 608]}, {"parameters": {"jsCode": "// Log for Handle Errors\nconst input = $input.first().json;\nconst logData = {\n  step: 'handle_errors',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_handle_errors.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "fa5df981-8abb-4a77-b7b8-6b343f450c53", "name": "Log Handle Errors", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-416, 608]}, {"parameters": {"jsCode": "// Create comprehensive execution log for debugging\nconst allInputs = $input.all();\nconst timestamp = new Date().toISOString();\n\nconst executionLog = {\n  timestamp: timestamp,\n  workflow_name: 'Kung Fu Video Detector',\n  execution_summary: {\n    total_inputs: allInputs.length,\n    execution_time: timestamp,\n    status: 'completed'\n  },\n  node_outputs: [],\n  debug_info: {\n    docker_mount: '/home/<USER>/shared/kung_fu_videos',\n    lm_studio_endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n    expected_files: 5\n  }\n};\n\n// Process each input and log details\nallInputs.forEach((input, index) => {\n  const data = input.json;\n  \n  executionLog.node_outputs.push({\n    input_index: index,\n    filename: data.filename || 'unknown',\n    fullPath: data.fullPath || 'unknown',\n    hasConfig: !!data.config,\n    hasThumbnail: !!data.thumbnailBase64,\n    isKungFu: data.isKungFu || false,\n    aiResponse: data.aiResponse || 'no response',\n    error: data.error || null,\n    timestamp: data.timestamp || 'unknown'\n  });\n});\n\n// Add summary statistics\nexecutionLog.summary = {\n  total_videos_processed: allInputs.length,\n  kung_fu_videos_found: allInputs.filter(input => input.json.isKungFu).length,\n  errors_encountered: allInputs.filter(input => input.json.error).length,\n  successful_ai_calls: allInputs.filter(input => input.json.aiResponse && !input.json.error).length\n};\n\nconsole.log('=== EXECUTION LOG SUMMARY ===');\nconsole.log(`Total videos processed: ${executionLog.summary.total_videos_processed}`);\nconsole.log(`Kung fu videos found: ${executionLog.summary.kung_fu_videos_found}`);\nconsole.log(`Errors encountered: ${executionLog.summary.errors_encountered}`);\nconsole.log(`Successful AI calls: ${executionLog.summary.successful_ai_calls}`);\n\n// Create log content for file (writeBinaryFile expects 'data' property)\nconst logContent = JSON.stringify(executionLog, null, 2);\nconst filename = `kung_fu_detector_log_${timestamp.replace(/[:.]/g, '-')}.json`;\n\n// Prepare data for writeBinaryFile (correct pattern)\nconst binaryData = {\n  data: Buffer.from(logContent, 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: filename,\n  fileExtension: 'json'\n};\n\nreturn [{\n  json: {\n    filename: filename,\n    summary: executionLog.summary,\n    timestamp: timestamp\n  },\n  binary: {\n    data: binaryData\n  }\n}];"}, "id": "88560d3e-8205-425f-9780-90a748164b54", "name": "Create Execution Log", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-192, 608]}, {"parameters": {"jsCode": "// Log for Create Execution Log\nconst input = $input.first().json;\nconst logData = {\n  step: 'create_execution_log',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_create_execution_log.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "eb1fd6af-ae91-4d85-ba61-e172b5128107", "name": "Log Create Execution Log", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [32, 608]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/' + $json.filename }}", "options": {}}, "id": "72a8ac59-59a4-461c-9802-15f6d48370ee", "name": "Write Log to Shared Folder", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [256, 608]}, {"parameters": {"jsCode": "// Log for Write Log to Shared Folder\nconst input = $input.first().json;\nconst logData = {\n  step: 'write_log_to_shared_folder',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_write_log_to_shared_folder.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "3bdc9453-3cd2-4aff-8c0b-11d9b9b223d2", "name": "Log Write Log to Shared Folder", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [480, 608]}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [576, -352], "id": "7c4ed1b9-7c37-41ad-ab0e-dfd9fa376af0", "name": "Loop Over Items", "disabled": true}, {"parameters": {"jsCode": "const allInputs = $input.all();\n// Extract just the JSON data from each input\nconst videoArray = allInputs.map(input => input.json);\n\n// Return the array as items, not as a single json property\nreturn videoArray.map(video => ({ json: video }));"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [400, -352], "id": "be772d20-13d5-463f-8449-fc1ff39d60ee", "name": "Code", "disabled": true}], "pinData": {}, "connections": {"Manual Trigger": {"main": [[{"node": "Load Configuration", "type": "main", "index": 0}]]}, "Load Configuration": {"main": [[{"node": "Log Load Configuration", "type": "main", "index": 0}]]}, "Log Load Configuration": {"main": [[{"node": "Prepare Video Scan", "type": "main", "index": 0}]]}, "Prepare Video Scan": {"main": [[{"node": "Log Prepare Video Scan", "type": "main", "index": 0}]]}, "Log Prepare Video Scan": {"main": [[{"node": "<PERSON>an Folder for Videos", "type": "main", "index": 0}]]}, "Scan Folder for Videos": {"main": [[{"node": "Log Scan Folder for Videos", "type": "main", "index": 0}]]}, "Log Scan Folder for Videos": {"main": [[{"node": "Process File List", "type": "main", "index": 0}]]}, "Process File List": {"main": [[{"node": "Log Process File List", "type": "main", "index": 0}]]}, "Log Process File List": {"main": [[{"node": "Create FFmpeg Request", "type": "main", "index": 0}]]}, "Create FFmpeg Request": {"main": [[{"node": "Save FFmpeg Request", "type": "main", "index": 0}]]}, "Save FFmpeg Request": {"main": [[{"node": "Read FFmpeg Result", "type": "main", "index": 0}]]}, "Read FFmpeg Result": {"main": [[{"node": "Process FFmpeg Result", "type": "main", "index": 0}]]}, "Process FFmpeg Result": {"main": [[{"node": "Log Process Thumbnail", "type": "main", "index": 0}]]}, "Log Process Thumbnail": {"main": [[{"node": "Write Vision Request (File-Based)", "type": "main", "index": 0}]]}, "Log AI Video Analysis": {"main": [[{"node": "Process AI Response", "type": "main", "index": 0}]]}, "Process AI Response": {"main": [[{"node": "Log Process AI Response", "type": "main", "index": 0}]]}, "Log Process AI Response": {"main": [[{"node": "Filter Kung Fu Videos", "type": "main", "index": 0}]]}, "Filter Kung Fu Videos": {"main": [[{"node": "Log Filter Kung Fu Videos", "type": "main", "index": 0}], [{"node": "Log Filter Kung Fu Videos", "type": "main", "index": 0}]]}, "Log Filter Kung Fu Videos": {"main": [[{"node": "Collect Final Results", "type": "main", "index": 0}]]}, "Collect Final Results": {"main": [[{"node": "Log Collect Final Results", "type": "main", "index": 0}]]}, "Log Collect Final Results": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Handle Errors": {"main": [[{"node": "Log Handle Errors", "type": "main", "index": 0}]]}, "Log Handle Errors": {"main": [[{"node": "Create Execution Log", "type": "main", "index": 0}]]}, "Create Execution Log": {"main": [[{"node": "Log Create Execution Log", "type": "main", "index": 0}]]}, "Log Create Execution Log": {"main": [[{"node": "Write Log to Shared Folder", "type": "main", "index": 0}]]}, "Write Log to Shared Folder": {"main": [[{"node": "Log Write Log to Shared Folder", "type": "main", "index": 0}]]}, "Write Vision Request (File-Based)": {"main": [[{"node": "Save Vision Request (File-Based)", "type": "main", "index": 0}]]}, "Save Vision Request (File-Based)": {"main": [[{"node": "Read Vision Result (File-Based)", "type": "main", "index": 0}]]}, "Read Vision Result (File-Based)": {"main": [[{"node": "Parse Vision Result (File-Based)", "type": "main", "index": 0}]]}, "Parse Vision Result (File-Based)": {"main": [[{"node": "Log AI Video Analysis", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], []]}, "Code": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "3d16484c-59cf-4f17-bd9a-a0f139e5fc37", "meta": {"instanceId": "a9e00de748ec35ee88db078f832d6e48181d32e4fa741d36554310dd025f8599"}, "id": "rZPEtuXnSQOdmJGk", "tags": []}