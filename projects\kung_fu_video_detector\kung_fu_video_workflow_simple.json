{"name": "Kung Fu Video Detector - Simple Connected", "nodes": [{"parameters": {"jsCode": "// Embedded configuration - Uses existing Docker share setup\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',  // Using existing Docker share mount\n  recursive_search: true,\n  file_extensions: ['.mp4', '.avi', '.mov', '.mkv'],\n  lm_studio: {\n    endpoint: 'http://host.docker.internal:1234/v1/chat/completions',  // Docker host access\n    model: 'mimo-vl-7b-rl@q8_k_xl',\n    timeout: 30000,\n    temperature: 0.1,\n    max_tokens: 50\n  },\n  ai_prompt: 'Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training. Look for martial arts poses, fighting stances, training equipment, or combat movements. Please respond with only YES if it shows kung fu/martial arts practice, or NO if it does not.',\n  ffmpeg_path: 'ffmpeg',\n  detection_keywords: [\n    'yes', 'kung fu', 'martial arts', 'karate', 'taekwondo',\n    'fighting', 'combat', 'training', 'practice'\n  ]\n};\n\nconsole.log('=== CONFIGURATION LOADED ===');\nconsole.log('Video folder:', config.video_folder);\nconsole.log('Recursive search:', config.recursive_search);\nconsole.log('File extensions:', config.file_extensions);\nconsole.log('LM Studio endpoint:', config.lm_studio.endpoint);\nconsole.log('LM Studio model:', config.lm_studio.model);\n\nreturn [{ json: { config: config } }];"}, "id": "dee3c1e8-483c-4376-a78a-381b062af29f", "name": "Load Configuration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2580, -80]}, {"parameters": {"command": "find \"/home/<USER>/shared/kung_fu_videos\" -type f \\( -iname \"*.mp4\" -o -iname \"*.avi\" -o -iname \"*.mov\" -o -iname \"*.mkv\" \\) 2>/dev/null"}, "id": "05a21cf5-9a6b-4b45-a985-afad83287fac", "name": "<PERSON>an Folder for Videos", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-2140, -80]}, {"parameters": {"jsCode": "// Process the find command output to create file list\nconst input = $input.first().json;\nconst commandOutput = input.stdout || '';\nconst commandError = input.stderr || '';\nconst exitCode = input.exitCode || 0;\n\n// Create default config since executeCommand node doesn't pass it\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',\n  recursive_search: true,\n  file_extensions: ['.mp4', '.avi', '.mov', '.mkv'],\n  lm_studio: {\n    endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n    model: 'mimo-vl-7b-rl@q8_k_xl',\n    timeout: 30000,\n    temperature: 0.1,\n    max_tokens: 50\n  },\n  ai_prompt: 'Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training.',\n  ffmpeg_path: 'ffmpeg',\n  detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n};\n\nconst folderPath = config.video_folder;\n\nconsole.log('=== PROCESSING VIDEO FILE SCAN ===');\nconsole.log('Folder path:', folderPath);\nconsole.log('Recursive search:', config.recursive_search);\nconsole.log('Extensions:', config.file_extensions);\nconsole.log('Command output length:', commandOutput.length);\nconsole.log('Exit code:', exitCode);\n\n// Check for command errors\nif (exitCode !== 0) {\n  console.log('❌ Find command failed');\n  console.log('Error:', commandError);\n  return [{ json: { \n    error: `Cannot access folder: ${folderPath}. Error: ${commandError}`, \n    folderPath,\n    config\n  }}];\n}\n\nif (!commandOutput || commandOutput.trim() === '') {\n  console.log('No video files found in folder');\n  return [{ json: { \n    error: 'No video files found in the specified folder', \n    folderPath,\n    config,\n    note: `Searched for extensions: ${config.file_extensions.join(', ')}`\n  }}];\n}\n\n// Split output into individual files and clean up (Linux paths use forward slashes)\nconst fileLines = commandOutput.trim().split('\\n').filter(line => line.trim() !== '');\nconst videoFiles = [];\n\nfor (const line of fileLines) {\n  const cleanLine = line.trim();\n  if (cleanLine) {\n    // Extract just the filename from full path (Linux path)\n    const filename = cleanLine.split('/').pop();\n    \n    // Check if file has one of the configured extensions\n    const hasValidExtension = config.file_extensions.some(ext => \n      filename.toLowerCase().endsWith(ext.toLowerCase())\n    );\n    \n    if (filename && hasValidExtension) {\n      videoFiles.push({\n        filename: filename,\n        fullPath: cleanLine,\n        folderPath: folderPath,\n        config: config\n      });\n    }\n  }\n}\n\nconsole.log(`Found ${videoFiles.length} video files:`);\nvideoFiles.forEach(file => console.log(`  - ${file.filename}`));\n\nif (videoFiles.length === 0) {\n  return [{ json: { error: 'No valid video files found after processing', folderPath, config } }];\n}\n\n// Return each file as a separate item for processing\nreturn videoFiles.map(file => ({ json: file }));"}, "id": "8d8dfd08-5390-4441-a960-604631bd3a8a", "name": "Process File List", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1920, -80]}, {"parameters": {"command": "ffmpeg -i \"{{ $json.fullPath }}\" -ss 00:00:10 -vframes 1 -f image2pipe -vcodec png -"}, "id": "53e8d6ae-acbc-4f48-ae64-3b7e8e876606", "name": "Extract Video Thumbnail", "type": "n8n-nodes-base.executeCommand", "typeVersion": 2, "position": [-1700, -80]}, {"parameters": {"jsCode": "// Process FFmpeg thumbnail extraction with proper error handling\nconst input = $input.first().json;\nconst rawImageData = input.stdout || '';\nconst filename = input.filename;\nconst fullPath = input.fullPath;\nconst config = input.config;\nconst exitCode = input.exitCode || 0;\nconst stderr = input.stderr || '';\n\nconsole.log('=== PROCESSING FFMPEG THUMBNAIL EXTRACTION ===');\nconsole.log(`File: ${filename}`);\nconsole.log(`FFmpeg exit code: ${exitCode}`);\nconsole.log(`Raw image data length: ${rawImageData.length}`);\nconsole.log(`Stderr: ${stderr}`);\n\n// Check if FFmpeg extraction was successful\nif (exitCode !== 0 || !rawImageData || rawImageData.length < 1000) {\n  console.log('❌ FFmpeg thumbnail extraction FAILED');\n  \n  // Create detailed error log\n  const errorDetails = {\n    timestamp: new Date().toISOString(),\n    operation: 'ffmpeg_thumbnail_extraction',\n    filename: filename,\n    fullPath: fullPath,\n    exitCode: exitCode,\n    stderr: stderr,\n    rawDataLength: rawImageData.length,\n    error: 'FF<PERSON><PERSON> failed to extract video thumbnail'\n  };\n  \n  console.log('📝 Error details:', JSON.stringify(errorDetails, null, 2));\n  \n  // Return error state - NO MOCK FALLBACK\n  return [{\n    json: {\n      filename: filename,\n      fullPath: fullPath,\n      config: config,\n      error: errorDetails,\n      success: false,\n      thumbnailBase64: null,\n      hasValidThumbnail: false\n    }\n  }];\n}\n\n// Convert raw PNG data to base64\nconst thumbnailBase64 = Buffer.from(rawImageData, 'binary').toString('base64');\n\nconsole.log('✅ Real thumbnail extracted successfully!');\nconsole.log(`Thumbnail size: ${Math.round(thumbnailBase64.length / 1024)}KB`);\n\nreturn [{\n  json: {\n    filename: filename,\n    fullPath: fullPath,\n    config: config,\n    thumbnailBase64: thumbnailBase64,\n    hasValidThumbnail: true,\n    success: true,\n    thumbnailSizeKB: Math.round(thumbnailBase64.length / 1024)\n  }\n}];"}, "id": "94f5e89e-58d7-4cfa-a209-77ff9dae38ec", "name": "Process Thumbnail", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1480, -80]}, {"parameters": {"url": "={{ $json.config.lm_studio.endpoint }}", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": []}, "jsonBody": "={{ $json.success && $json.thumbnailBase64 ? {\n  \"model\": $json.config.lm_studio.model,\n  \"messages\": [{\n    \"role\": \"user\",\n    \"content\": [\n      {\n        \"type\": \"text\",\n        \"text\": $json.config.ai_prompt\n      },\n      {\n        \"type\": \"image_url\",\n        \"image_url\": {\n          \"url\": \"data:image/png;base64,\" + $json.thumbnailBase64\n        }\n      }\n    ]\n  }],\n  \"temperature\": $json.config.lm_studio.temperature,\n  \"max_tokens\": $json.config.lm_studio.max_tokens\n} : null }}", "options": {"timeout": 30000}}, "id": "7aca14f2-b38c-45c7-9246-d936ac98523b", "name": "AI Video Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 2, "position": [-1260, -80]}, {"parameters": {"jsCode": "// Extract the AI response and determine if it's kung fu\nconst input = $input.first().json;\nconst aiResponse = input.choices?.[0]?.message?.content || '';\n\n// Get data from previous nodes (HTTP Request doesn't pass all data)\nconst allInputs = $input.all();\nlet filename = 'unknown';\nlet fullPath = 'unknown';\nlet config = null;\n\n// Try to find config and filename from input data\nif (input.filename) {\n  filename = input.filename;\n  fullPath = input.fullPath;\n  config = input.config;\n} else {\n  // HTTP Request node might not pass all data, use default config\n  config = {\n    detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n  };\n}\n\nconsole.log('=== PROCESSING AI RESPONSE ===');\nconsole.log(`File: ${filename}`);\nconsole.log(`AI Response: ${aiResponse}`);\nconsole.log(`Detection keywords: ${config.detection_keywords}`);\n\n// Check if AI response indicates kung fu/martial arts using configured keywords\nconst responseText = aiResponse.toLowerCase();\nconst isKungFu = config.detection_keywords.some(keyword => \n  responseText.includes(keyword.toLowerCase())\n);\n\nconsole.log(`Is Kung Fu: ${isKungFu}`);\nif (isKungFu) {\n  const matchedKeywords = config.detection_keywords.filter(keyword => \n    responseText.includes(keyword.toLowerCase())\n  );\n  console.log(`Matched keywords: ${matchedKeywords.join(', ')}`);\n}\n\nreturn [{\n  json: {\n    filename: filename,\n    fullPath: fullPath,\n    aiResponse: aiResponse,\n    isKungFu: isKungFu,\n    confidence: isKungFu ? 0.8 : 0.2,\n    matchedKeywords: isKungFu ? config.detection_keywords.filter(k => responseText.includes(k.toLowerCase())) : [],\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "ba32d901-fdc2-4123-820b-d6b119b51003", "name": "Process AI Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1040, -80]}], "pinData": {}, "connections": {"Load Configuration": {"main": [[{"node": "<PERSON>an Folder for Videos", "type": "main", "index": 0}]]}, "Scan Folder for Videos": {"main": [[{"node": "Process File List", "type": "main", "index": 0}]]}, "Process File List": {"main": [[{"node": "Extract Video Thumbnail", "type": "main", "index": 0}]]}, "Extract Video Thumbnail": {"main": [[{"node": "Process Thumbnail", "type": "main", "index": 0}]]}, "Process Thumbnail": {"main": [[{"node": "AI Video Analysis", "type": "main", "index": 0}]]}, "AI Video Analysis": {"main": [[{"node": "Process AI Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1"}