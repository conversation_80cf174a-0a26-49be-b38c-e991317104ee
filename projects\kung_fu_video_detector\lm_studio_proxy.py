#!/usr/bin/env python3
"""
Production-Ready LM Studio API Proxy for N8N
Handles vision API calls with enhanced error handling, logging, and monitoring
"""

import json
import requests
import time
import os
from datetime import datetime
from flask import Flask, request, jsonify
import logging
from functools import wraps

# Configure comprehensive logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('lm_studio_proxy.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Configuration
LM_STUDIO_URL = "http://localhost:1234/v1/chat/completions"
DEFAULT_MODEL = "mimo-vl-7b-rl@q8_k_xl"
REQUEST_TIMEOUT = 60  # Increased for vision processing
MAX_RETRIES = 3
RETRY_DELAY = 1  # seconds

# Statistics tracking
stats = {
    'total_requests': 0,
    'successful_requests': 0,
    'failed_requests': 0,
    'start_time': datetime.now()
}

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "service": "LM Studio Proxy"})

@app.route('/vision', methods=['POST'])
def vision_analysis():
    """
    Vision analysis endpoint that forwards requests to LM Studio
    Accepts JSON with: text, base64_image, model (optional)
    """
    try:
        # Get request data
        data = request.get_json()
        
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400
        
        # Extract parameters
        text_prompt = data.get('text', 'Analyze this image')
        base64_image = data.get('base64_image', '')
        model = data.get('model', DEFAULT_MODEL)
        temperature = data.get('temperature', 0.1)
        max_tokens = data.get('max_tokens', 50)
        
        logger.info(f"Vision analysis request: text_len={len(text_prompt)}, image_len={len(base64_image)}")
        
        if not base64_image:
            return jsonify({"error": "base64_image is required"}), 400
        
        # Prepare LM Studio request
        lm_studio_payload = {
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": text_prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": False
        }
        
        logger.info(f"Forwarding request to LM Studio: {LM_STUDIO_URL}")
        
        # Make request to LM Studio
        response = requests.post(
            LM_STUDIO_URL,
            json=lm_studio_payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            logger.info(f"LM Studio response: {content[:100]}...")
            
            return jsonify({
                "success": True,
                "content": content,
                "full_response": result
            })
        else:
            logger.error(f"LM Studio error: {response.status_code} - {response.text}")
            return jsonify({
                "error": f"LM Studio error: {response.status_code}",
                "details": response.text
            }), response.status_code
            
    except requests.exceptions.RequestException as e:
        logger.error(f"Request error: {str(e)}")
        return jsonify({"error": f"Request failed: {str(e)}"}), 500
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return jsonify({"error": f"Internal error: {str(e)}"}), 500

@app.route('/simple', methods=['POST'])
def simple_chat():
    """
    Simple text-only chat endpoint for testing
    """
    try:
        data = request.get_json()
        text = data.get('text', 'Hello')
        model = data.get('model', DEFAULT_MODEL)
        
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": text}],
            "temperature": 0.1,
            "max_tokens": 10
        }
        
        response = requests.post(LM_STUDIO_URL, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            return jsonify({"success": True, "content": content})
        else:
            return jsonify({"error": f"LM Studio error: {response.status_code}"}), response.status_code
            
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    print("🚀 Starting LM Studio Proxy Server")
    print(f"📡 LM Studio URL: {LM_STUDIO_URL}")
    print(f"🤖 Default Model: {DEFAULT_MODEL}")
    print("🌐 Proxy will run on http://localhost:8080")
    print("\nEndpoints:")
    print("  GET  /health - Health check")
    print("  POST /simple - Simple text chat")
    print("  POST /vision - Vision analysis (text + base64 image)")
    
    app.run(host='0.0.0.0', port=8080, debug=True)
