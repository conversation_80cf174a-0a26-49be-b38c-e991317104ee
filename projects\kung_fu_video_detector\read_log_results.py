#!/usr/bin/env python3
"""Read and analyze the workflow log results"""

import json
from pathlib import Path

def read_log_results():
    """Read and analyze both log files."""
    print("🔍 WORKFLOW LOG RESULTS ANALYSIS")
    print("=" * 60)
    
    # Read immediate scan log
    scan_log_path = Path("C:/Docker_Share/N8N/immediate_scan_log.json")
    if scan_log_path.exists():
        print("✅ IMMEDIATE SCAN LOG FOUND")
        print("-" * 30)
        with open(scan_log_path, 'r', encoding='utf-8') as f:
            scan_data = json.load(f)
        
        print(f"Timestamp: {scan_data['timestamp']}")
        print(f"Files found: {len(scan_data['stdout'].strip().split())}")
        print(f"Exit code: {scan_data['raw_data']['exitCode']}")
        print(f"Files:")
        for line in scan_data['stdout'].strip().split('\n'):
            filename = line.split('/')[-1]
            print(f"  - {filename}")
        print()
    else:
        print("❌ Immediate scan log not found")
    
    # Read process file list log
    process_log_path = Path("C:/Docker_Share/N8N/process_file_list_log.json")
    if process_log_path.exists():
        print("✅ PROCESS FILE LIST LOG FOUND")
        print("-" * 30)
        with open(process_log_path, 'r', encoding='utf-8') as f:
            process_data = json.load(f)
        
        print(f"Timestamp: {process_data['timestamp']}")
        print(f"Total inputs: {process_data['total_inputs']}")
        print(f"Processed files:")
        for file_info in process_data['processed_files']:
            print(f"  {file_info['index']}: {file_info['filename']}")
            if file_info['error']:
                print(f"     ERROR: {file_info['error']}")
        print()
    else:
        print("❌ Process file list log not found")
    
    # Analysis
    print("🎯 ANALYSIS")
    print("-" * 30)
    
    if scan_log_path.exists() and process_log_path.exists():
        scan_files = len(scan_data['stdout'].strip().split('\n'))
        process_files = process_data['total_inputs']
        
        print(f"Files found by scan: {scan_files}")
        print(f"Files processed by list: {process_files}")
        
        if scan_files == 5:
            print("✅ Case sensitivity fix WORKED - found all 5 files!")
        elif scan_files == 3:
            print("❌ Case sensitivity fix FAILED - still only finding 3 files")
        
        if process_files > 0:
            print("✅ Process File List is working - receiving data")
        else:
            print("❌ Process File List failed - no data received")
            
        if scan_files == process_files:
            print("✅ Data flow is correct - scan → process working")
        else:
            print("❌ Data flow issue - scan and process counts don't match")

if __name__ == "__main__":
    read_log_results()
