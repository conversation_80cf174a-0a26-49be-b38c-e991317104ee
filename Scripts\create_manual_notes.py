#!/usr/bin/env python3
"""
Manual Notes Creator - Demonstrates Expected Results
Creates the notes files manually from the successful processor results
"""

import os
import json
import re
from datetime import datetime

# Configuration
DOCKER_SHARE = "C:/Docker_Share/N8N"
NOTES_FOLDER = f"{DOCKER_SHARE}/notes"

def read_processor_results(folder_path):
    """Read all JSON results from a processor folder"""
    results = []
    if not os.path.exists(folder_path):
        print(f"Warning: Folder not found: {folder_path}")
        return results
    
    for filename in os.listdir(folder_path):
        if filename.endswith('.json'):
            try:
                with open(os.path.join(folder_path, filename), 'r', encoding='utf-8') as f:
                    result = json.load(f)
                    results.append(result)
            except Exception as e:
                print(f"Warning: Failed to read {filename}: {e}")
    
    return results

def find_result_by_filename(results, target_filename):
    """Find result matching the target filename"""
    for result in results:
        if result.get('filename') == target_filename:
            return result
    return None

def extract_date_from_filename(filename):
    """Extract date from filename"""
    # Try to extract YYYYMMDD from start of filename
    match = re.match(r'^(20\d{6})', filename)
    if match:
        return match.group(1)
    # Use fallback date for M4H files
    return '20120125'

def clean_description(description):
    """Clean AI-generated description"""
    if not description:
        return "No description available"
    
    # Remove <think> tags
    cleaned = re.sub(r'<think>.*?</think>', '', description, flags=re.DOTALL)
    
    # Clean up whitespace
    cleaned = ' '.join(cleaned.split())
    
    # Truncate if too long
    if len(cleaned) > 200:
        cleaned = cleaned[:200] + "..."
    
    return cleaned.strip() or "No description available"

def main():
    print("=" * 80)
    print("MANUAL NOTES CREATOR - DEMONSTRATING EXPECTED RESULTS")
    print("=" * 80)
    print()
    
    # Expected files from the successful processing
    expected_files = [
        '20250406_110016_1.mp4',
        '20250504_113836_1.mp4', 
        '20250622_100122.mp4',
        'M4H01890.MP4',
        'M4H01892.MP4'
    ]
    
    # Read processor results
    print("📁 Reading processor results...")
    ffmpeg_results = read_processor_results(f"{DOCKER_SHARE}/ffmpeg_results")
    vision_results = read_processor_results(f"{DOCKER_SHARE}/vision_results")
    description_results = read_processor_results(f"{DOCKER_SHARE}/description_results")
    
    print(f"   FFmpeg results: {len(ffmpeg_results)}")
    print(f"   Vision results: {len(vision_results)}")
    print(f"   Description results: {len(description_results)}")
    print()
    
    # Process each expected file
    processed_videos = []
    skipped_files = []
    
    print("🎬 Processing video results...")
    for filename in expected_files:
        print(f"   Processing: {filename}")
        
        # Find matching results
        ffmpeg_result = find_result_by_filename(ffmpeg_results, filename)
        vision_result = find_result_by_filename(vision_results, filename)
        description_result = find_result_by_filename(description_results, filename)
        
        if not ffmpeg_result or not ffmpeg_result.get('success'):
            print(f"     ❌ No successful FFmpeg result")
            skipped_files.append(filename)
            continue
            
        if not vision_result or not vision_result.get('success'):
            print(f"     ❌ No successful Vision result")
            skipped_files.append(filename)
            continue
            
        if not description_result or not description_result.get('success'):
            print(f"     ❌ No successful Description result")
            skipped_files.append(filename)
            continue
        
        # Extract information
        date = extract_date_from_filename(filename)
        description = clean_description(description_result.get('description', ''))
        is_kung_fu = vision_result.get('analysis_result') == 'YES'
        
        processed_videos.append({
            'filename': filename,
            'date': date,
            'description': description,
            'is_kung_fu': is_kung_fu
        })
        
        print(f"     ✅ {date} → {description[:50]}...")
    
    print()
    print(f"📊 Results: {len(processed_videos)} processed, {len(skipped_files)} skipped")
    print()
    
    # Group by date
    videos_by_date = {}
    for video in processed_videos:
        date = video['date']
        if date not in videos_by_date:
            videos_by_date[date] = []
        videos_by_date[date].append(video)
    
    # Create notes folder
    os.makedirs(NOTES_FOLDER, exist_ok=True)
    
    # Create notes files
    print("📝 Creating notes files...")
    created_files = []
    
    for date, videos in videos_by_date.items():
        filename = f"{date}_Notes.txt"
        filepath = os.path.join(NOTES_FOLDER, filename)
        
        # Create content
        lines = []
        for video in videos:
            line = f"{video['filename']} - {video['description']}"
            lines.append(line)
        
        content = '\n'.join(lines) + '\n'
        
        # Write file
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        created_files.append({
            'filename': filename,
            'path': filepath,
            'video_count': len(videos),
            'date': date
        })
        
        print(f"   ✅ Created: {filename} ({len(videos)} videos)")
        for video in videos:
            print(f"      {video['filename']} - {video['description']}")
        print()
    
    # Summary
    print("=" * 80)
    print("🎉 MANUAL NOTES CREATION COMPLETE!")
    print("=" * 80)
    print(f"Created {len(created_files)} notes files:")
    for file_info in created_files:
        print(f"  📄 {file_info['filename']} - {file_info['video_count']} videos")
    print()
    print("These files demonstrate the expected output from the enhanced workflow!")
    print("The workflow processed all videos successfully - only the data aggregation needs fixing.")
    print("=" * 80)

if __name__ == "__main__":
    main()
