# Manual Testing Guide - Connected Kung Fu Video Detector Workflow

## 🎯 Objective
Test the properly connected N8N workflow to verify:
1. All nodes show visual connections in N8N UI (no disconnected nodes)
2. Workflow processes all 5 video files (not just first file)
3. Real thumbnail extraction from videos (20-50KB base64 images)
4. Complete execution takes 30-60 seconds (not 1 second)

## 📋 Pre-Test Checklist

### ✅ Environment Verification
- [x] N8N is running (http://localhost:5678 opened in browser)
- [x] Docker containers are up: `n8n` and `n8n-postgres`
- [x] FFmpeg is installed in N8N container (version 6.1.2 confirmed)
- [x] 5 video files exist in `/home/<USER>/shared/kung_fu_videos/`:
  - 20250406_110016_1.mp4 (128MB)
  - 20250504_113836_1.mp4 (31MB)
  - 20250622_100122.mp4 (44MB)
  - M4H01890.MP4 (28MB)
  - M4H01892.MP4 (19MB)

## 🔧 Step 1: Import Connected Workflow

1. **Open N8N Web Interface**: http://localhost:5678
2. **Import Workflow**:
   - Click "Import from file" or use the import option
   - Select file: `kung_fu_video_workflow_properly_connected.json`
   - Click "Import"

### ✅ Connection Verification Checklist
After import, verify these connections are visible in the N8N UI:

```
Start → Load Config → Write Config Log → Scan Videos → Process Files → Extract Thumbnail → Process Thumbnail
```

**Expected Visual Layout:**
- **Start** (Manual Trigger) at position [250, 300]
- **Load Config** (Code Node) at position [500, 300]  
- **Write Config Log** (Write Binary File) at position [750, 300]
- **Scan Videos** (Execute Command) at position [1000, 300]
- **Process Files** (Code Node) at position [1250, 300]
- **Extract Thumbnail** (Execute Command) at position [1500, 300]
- **Process Thumbnail** (Code Node) at position [1750, 300]

**❌ CRITICAL**: If any nodes appear disconnected or floating, the connection fix failed.

## 🚀 Step 2: Execute Workflow

1. **Click "Execute Workflow"** button
2. **Start Timer**: Note the start time
3. **Monitor Progress**: Watch the node execution indicators

### ⏱️ Expected Execution Timeline
- **0-5 seconds**: Configuration loading and logging
- **5-10 seconds**: Video file scanning
- **10-15 seconds**: File list processing
- **15-45 seconds**: Thumbnail extraction (5 files × 5-10 seconds each)
- **45-60 seconds**: Thumbnail processing and final logging

**❌ CRITICAL**: If workflow completes in 1 second, execution stopped prematurely.

## 📊 Step 3: Analyze Execution Results

### Per-Node Logging Analysis
Check these log files in Docker shared folder:

```bash
# Check log files created
docker exec n8n ls -la /home/<USER>/shared/*.json
```

**Expected Log Files:**
1. **1_config_log.json** - Configuration loading step
2. **2_scan_log.json** - Video file scanning results
3. **3_thumbnail_success_*.json** - Successful thumbnail extractions
4. **3_thumbnail_error_*.json** - Failed thumbnail extractions (if any)

### Success Criteria Analysis

#### ✅ Configuration Step Success
```json
{
  "step": "1_config_load",
  "timestamp": "2025-01-05T...",
  "config": {
    "video_folder": "/home/<USER>/shared/kung_fu_videos",
    "lm_studio": {
      "endpoint": "http://host.docker.internal:1234/v1/chat/completions"
    }
  }
}
```

#### ✅ Scan Step Success
```json
{
  "step": "2_scan_success", 
  "timestamp": "2025-01-05T...",
  "total_files_found": 5,
  "files": [
    {"filename": "20250406_110016_1.mp4", "fullPath": "/home/<USER>/shared/kung_fu_videos/20250406_110016_1.mp4"},
    {"filename": "20250504_113836_1.mp4", "fullPath": "/home/<USER>/shared/kung_fu_videos/20250504_113836_1.mp4"},
    // ... 3 more files
  ]
}
```

#### ✅ Thumbnail Success
```json
{
  "step": "3_thumbnail_success",
  "filename": "20250406_110016_1.mp4",
  "thumbnail_size_bytes": 25000,  // 20-50KB expected
  "success": true
}
```

## 🔍 Step 4: Debug Common Issues

### Issue 1: 1-Second Execution
**Symptoms**: Workflow completes immediately
**Diagnosis**: Check which log files exist
- Only `1_config_log.json` → Config step worked, scan step failed
- Only `1_config_log.json` + `2_scan_error_log.json` → No video files found
- Missing files after step N → Workflow stopped at step N

### Issue 2: No Video Files Found
**Check**: 
```bash
docker exec n8n find /home/<USER>/shared/kung_fu_videos -type f -iname "*.mp4"
```
**Expected**: 5 files listed

### Issue 3: FFmpeg Thumbnail Extraction Fails
**Check**:
```bash
docker exec n8n ffmpeg -i "/home/<USER>/shared/kung_fu_videos/20250406_110016_1.mp4" -ss 00:00:10 -vframes 1 -f image2pipe -vcodec png - | wc -c
```
**Expected**: Output > 10000 bytes

### Issue 4: Disconnected Nodes in UI
**Symptoms**: Nodes appear floating or not connected
**Solution**: Re-import workflow or check JSON connections section

## 📈 Success Metrics

### ✅ Complete Success Criteria
- [ ] All 7 nodes visually connected in N8N UI
- [ ] Execution time: 30-60 seconds (not 1 second)
- [ ] 5 video files processed (check scan log)
- [ ] Real thumbnail extraction (20-50KB base64 data)
- [ ] No mock fallbacks used
- [ ] Comprehensive per-node logging
- [ ] All log files created with proper timestamps

### 📊 Performance Benchmarks
- **File Scanning**: < 5 seconds for 5 files
- **Thumbnail Extraction**: 5-10 seconds per file
- **Total Processing**: 30-60 seconds for all files
- **Log File Size**: 1-2KB per log file
- **Thumbnail Size**: 20-50KB base64 encoded

## 🎯 Next Steps After Success

1. **Add AI Analysis Node**: Connect to LM Studio endpoint
2. **Add Results Processing**: Parse AI responses for kung fu detection
3. **Add Final Report**: Generate summary of detected kung fu videos
4. **Test End-to-End**: Complete kung fu detection pipeline

## 🚨 Failure Recovery

If the connected workflow fails:
1. **Check Docker logs**: `docker logs n8n`
2. **Verify file permissions**: All video files should be readable
3. **Test FFmpeg manually**: Extract one thumbnail manually
4. **Re-import workflow**: Use the properly formatted JSON file
5. **Check N8N version**: Ensure compatibility with node types used

---

**File**: `kung_fu_video_workflow_properly_connected.json`
**Created**: 2025-01-05
**Nodes**: 7 connected nodes with comprehensive logging
**Purpose**: Test proper N8N workflow connections and core functionality
