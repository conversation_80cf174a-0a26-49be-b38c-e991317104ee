{"name": "Curl LM Studio Test", "nodes": [{"parameters": {}, "id": "manual-trigger-curl", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"command": "wget -qO- --post-data='{\"model\":\"mimo-vl-7b-rl@q8_k_xl\",\"messages\":[{\"role\":\"user\",\"content\":\"Hello, please respond with just OK\"}],\"temperature\":0.1,\"max_tokens\":5}' --header='Content-Type: application/json' http://host.docker.internal:1234/v1/chat/completions"}, "id": "curl-http-request", "name": "Curl HTTP Request", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [500, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Curl HTTP Request", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}