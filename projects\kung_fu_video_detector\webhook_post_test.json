{"name": "Webhook POST Test", "nodes": [{"parameters": {}, "id": "manual-trigger-webhook", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"jsCode": "// Create data for external HTTP call\nconst requestData = {\n  url: 'http://host.docker.internal:1234/v1/chat/completions',\n  method: 'POST',\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  payload: {\n    model: 'mimo-vl-7b-rl@q8_k_xl',\n    messages: [\n      {\n        role: 'user',\n        content: 'Hello, this is a webhook test. Please respond with just OK.'\n      }\n    ],\n    temperature: 0.1,\n    max_tokens: 5\n  }\n};\n\nconsole.log('=== WEBHOOK POST TEST ===');\nconsole.log('Preparing data for external HTTP call');\nconsole.log('Target URL:', requestData.url);\nconsole.log('Method:', requestData.method);\n\nreturn [{ json: requestData }];"}, "id": "prepare-webhook-data", "name": "Prepare Webhook Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [500, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Prepare Webhook Data", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}