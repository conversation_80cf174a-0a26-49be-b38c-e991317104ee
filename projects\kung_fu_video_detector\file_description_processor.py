#!/usr/bin/env python3
"""
File-Based Description Processor for N8N
Monitors shared folder for descriptive analysis requests and processes them
Provides detailed descriptions for kung fu videos and brief descriptions for others
"""

import json
import requests
import time
import os
import base64
from datetime import datetime
import logging
import uuid

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('file_description_processor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Configuration
SHARED_FOLDER = "C:/Docker_Share/N8N/description_requests"
RESULTS_FOLDER = "C:/Docker_Share/N8N/description_results"
LM_STUDIO_URL = "http://localhost:1234/v1/chat/completions"
DEFAULT_MODEL = "mimo-vl-7b-rl@q8_k_xl"
POLL_INTERVAL = 1  # seconds - fast polling for real-time feel

# Statistics
stats = {
    'total_requests': 0,
    'successful_requests': 0,
    'failed_requests': 0,
    'kung_fu_descriptions': 0,
    'other_descriptions': 0,
    'start_time': datetime.now()
}

# Ensure folders exist
os.makedirs(SHARED_FOLDER, exist_ok=True)
os.makedirs(RESULTS_FOLDER, exist_ok=True)

def process_description_request(request_file):
    """Process a single descriptive analysis request"""
    try:
        # Read request data
        with open(request_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        filename = data.get('filename', 'unknown.mp4')
        thumbnail_base64 = data.get('thumbnailBase64', '')
        is_kung_fu = data.get('isKungFu', False)
        needs_thumbnail = data.get('needs_thumbnail', False)
        custom_prompt = data.get('prompt', '')
        request_id = data.get('request_id', str(uuid.uuid4()))

        logger.info(f"Processing description request: {filename} (ID: {request_id}, Kung Fu: {is_kung_fu})")
        stats['total_requests'] += 1

        # If we need to get the thumbnail from FFmpeg results
        if needs_thumbnail and not thumbnail_base64:
            logger.info(f"Attempting to get thumbnail from FFmpeg results for {filename}")
            try:
                # Look for FFmpeg result files that match this filename
                ffmpeg_results_folder = "C:/Docker_Share/N8N/ffmpeg_results"
                result_files = [f for f in os.listdir(ffmpeg_results_folder) if f.startswith('result_') and f.endswith('.json')]

                for result_file in result_files:
                    result_path = os.path.join(ffmpeg_results_folder, result_file)
                    try:
                        with open(result_path, 'r', encoding='utf-8') as f:
                            ffmpeg_result = json.load(f)

                        if ffmpeg_result.get('filename') == filename and ffmpeg_result.get('success'):
                            thumbnail_base64 = ffmpeg_result.get('thumbnail_base64', '')
                            if thumbnail_base64:
                                logger.info(f"Found thumbnail for {filename} in {result_file}")
                                break
                    except Exception as e:
                        logger.warning(f"Error reading FFmpeg result {result_file}: {e}")
                        continue

            except Exception as e:
                logger.warning(f"Error searching for FFmpeg results: {e}")

        if not thumbnail_base64:
            raise ValueError("thumbnailBase64 is required and could not be obtained from FFmpeg results")
        
        # Choose prompt based on kung fu detection result
        if is_kung_fu:
            default_prompt = """Analyze this video thumbnail from a kung fu training video. Look for text blocks that describe the technique being demonstrated (usually visible by the 10th frame). Extract and return the technique name like 'Dragon Walking Sword' or 'Lightning Kick - Hsing-I'. If you can see martial arts content but no text, describe the technique you observe. Be specific about the style, technique name, or training activity."""
            stats['kung_fu_descriptions'] += 1
        else:
            default_prompt = """This video does not contain kung fu content. Provide a brief 3-10 word description of what you see in the thumbnail, such as 'Field of grass with turtle' or 'Person walking in park'. Keep it concise and descriptive."""
            stats['other_descriptions'] += 1
        
        text_prompt = custom_prompt if custom_prompt else default_prompt
        
        # Validate and prepare base64 image (same logic as vision processor)
        try:
            # Remove data URL prefix if present
            clean_base64 = thumbnail_base64
            if thumbnail_base64.startswith('data:image'):
                clean_base64 = thumbnail_base64.split(',')[1]

            # Fix corrupted base64 data - remove leading invalid characters
            if not clean_base64.startswith('iVBORw0KGgo'):
                logger.warning("Base64 doesn't start with PNG signature, attempting to fix...")
                png_start = clean_base64.find('iVBORw0KGgo')
                if png_start > 0:
                    logger.info(f"Found PNG signature at position {png_start}, removing {png_start} leading characters")
                    clean_base64 = clean_base64[png_start:]
                elif clean_base64.startswith('/'):
                    logger.info("Removing leading slash character")
                    clean_base64 = clean_base64[1:]

            # Test decode to validate
            decoded_data = base64.b64decode(clean_base64)
            
            # Check if it's a valid PNG
            if decoded_data[:8] == b'\x89PNG\r\n\x1a\n':
                logger.info("Valid PNG signature detected")
            else:
                logger.warning(f"Invalid PNG signature. First 8 bytes: {decoded_data[:8].hex()}")
                # Try to find PNG signature in the data
                for i in range(1, min(10, len(decoded_data))):
                    if decoded_data[i:i+8] == b'\x89PNG\r\n\x1a\n':
                        logger.info(f"Found PNG signature at byte offset {i}, adjusting base64")
                        # Re-encode without the leading bytes
                        clean_base64 = base64.b64encode(decoded_data[i:]).decode('utf-8')
                        break
                else:
                    logger.warning("Could not find valid PNG signature, proceeding anyway")

        except Exception as e:
            logger.error(f"Base64 validation error: {e}")
            raise ValueError(f"Invalid base64 image data: {e}")

        # Prepare LM Studio request
        lm_studio_payload = {
            "model": DEFAULT_MODEL,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": text_prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{clean_base64}"
                            }
                        }
                    ]
                }
            ],
            "temperature": 0.4,
            "max_tokens": 150,
            "stream": False
        }
        
        logger.info(f"Sending description request to LM Studio for {filename}")
        
        # Make request to LM Studio
        response = requests.post(
            LM_STUDIO_URL,
            json=lm_studio_payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            # Clean up the description
            description = content.strip()
            if not description:
                description = "No description available"
            
            logger.info(f"Description for {filename}: {description[:100]}...")
            stats['successful_requests'] += 1
            
            # Prepare result
            result_data = {
                "success": True,
                "request_id": request_id,
                "filename": filename,
                "description": description,
                "full_response": content,
                "is_kung_fu": is_kung_fu,
                "prompt_type": "kung_fu" if is_kung_fu else "other",
                "model_used": DEFAULT_MODEL,
                "processed_at": datetime.now().isoformat(),
                "prompt_used": text_prompt[:100] + "..." if len(text_prompt) > 100 else text_prompt
            }
            
        else:
            logger.error(f"LM Studio error: {response.status_code} - {response.text}")
            stats['failed_requests'] += 1
            result_data = {
                "success": False,
                "request_id": request_id,
                "filename": filename,
                "description": "Error: Failed to get description",
                "error": f"LM Studio error: {response.status_code}",
                "error_type": "lm_studio_description_failed",
                "error_step": "description_analysis",
                "status_code": response.status_code,
                "is_kung_fu": is_kung_fu,
                "processed_at": datetime.now().isoformat(),
                "skip_reason": f"LM Studio description API call failed with status {response.status_code}"
            }
        
        # Write result to file
        result_filename = f"result_{request_id}.json"
        result_path = os.path.join(RESULTS_FOLDER, result_filename)
        
        with open(result_path, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Result written to {result_filename}")
        
        # Clean up request file
        try:
            os.remove(request_file)
            logger.info(f"Cleaned up request file: {os.path.basename(request_file)}")
        except Exception as e:
            logger.warning(f"Failed to clean up request file: {e}")
            
    except Exception as e:
        logger.error(f"Error processing description request {request_file}: {e}")
        stats['failed_requests'] += 1
        
        # Write error result if we have enough info
        try:
            error_result = {
                "success": False,
                "request_id": data.get('request_id', 'unknown') if 'data' in locals() else 'unknown',
                "filename": data.get('filename', 'unknown') if 'data' in locals() else 'unknown',
                "description": f"Error: {str(e)}",
                "error": str(e),
                "error_type": "description_processing_exception",
                "error_step": "description_analysis",
                "processed_at": datetime.now().isoformat(),
                "skip_reason": f"Description processing failed with exception: {str(e)}"
            }
            
            error_filename = f"error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            error_path = os.path.join(RESULTS_FOLDER, error_filename)
            
            with open(error_path, 'w', encoding='utf-8') as f:
                json.dump(error_result, f, indent=2)
                
        except Exception as write_error:
            logger.error(f"Failed to write error result: {write_error}")

def monitor_requests():
    """Monitor the shared folder for new description requests"""
    logger.info("Starting File-Based Description Processor")
    logger.info(f"Monitoring: {SHARED_FOLDER}")
    logger.info(f"Results: {RESULTS_FOLDER}")
    logger.info(f"LM Studio: {LM_STUDIO_URL}")
    logger.info(f"Model: {DEFAULT_MODEL}")
    logger.info(f"Poll interval: {POLL_INTERVAL} seconds")
    
    while True:
        try:
            # Get all JSON request files
            request_files = [f for f in os.listdir(SHARED_FOLDER) if f.endswith('.json')]
            
            if request_files:
                logger.info(f"Found {len(request_files)} description request(s)")
                
                for request_file in request_files:
                    request_path = os.path.join(SHARED_FOLDER, request_file)
                    process_description_request(request_path)
                    
                # Log statistics
                uptime = datetime.now() - stats['start_time']
                logger.info(f"Stats - Total: {stats['total_requests']}, Success: {stats['successful_requests']}, "
                          f"Failed: {stats['failed_requests']}, Kung Fu: {stats['kung_fu_descriptions']}, "
                          f"Other: {stats['other_descriptions']}, Uptime: {uptime}")
            
            time.sleep(POLL_INTERVAL)
            
        except KeyboardInterrupt:
            logger.info("Shutting down File-Based Description Processor")
            break
        except Exception as e:
            logger.error(f"Error in monitor loop: {e}")
            time.sleep(POLL_INTERVAL)

if __name__ == "__main__":
    monitor_requests()
