#!/usr/bin/env python3
"""
Simple LM Studio Proxy Test
Minimal proxy to test if N8N can successfully make HTTP requests to a local proxy
"""

import json
import requests
from flask import Flask, request, jsonify
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# LM Studio configuration
LM_STUDIO_URL = "http://localhost:1234/v1/chat/completions"

@app.route('/health', methods=['GET'])
def health_check():
    """Simple health check"""
    return jsonify({
        "status": "healthy", 
        "service": "Simple LM Studio Proxy",
        "timestamp": "2025-01-05T00:00:00Z"
    })

@app.route('/simple-test', methods=['POST'])
def simple_test():
    """
    Simple test endpoint that accepts basic JSON and forwards to LM Studio
    Expected input: {"text": "Hello world"}
    """
    try:
        # Get request data
        data = request.get_json()
        logger.info(f"Received request: {data}")
        
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400
        
        # Extract text
        text = data.get('text', 'Hello, please respond with just OK')
        logger.info(f"Processing text: {text}")
        
        # Prepare simple LM Studio request
        lm_studio_payload = {
            "model": "mimo-vl-7b-rl@q8_k_xl",
            "messages": [
                {
                    "role": "user",
                    "content": text
                }
            ],
            "temperature": 0.1,
            "max_tokens": 10
        }
        
        logger.info(f"Sending to LM Studio: {LM_STUDIO_URL}")
        
        # Make request to LM Studio
        response = requests.post(
            LM_STUDIO_URL,
            json=lm_studio_payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        logger.info(f"LM Studio response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            logger.info(f"LM Studio response content: {content}")
            
            return jsonify({
                "success": True,
                "proxy_status": "working",
                "lm_studio_response": content,
                "original_request": text
            })
        else:
            logger.error(f"LM Studio error: {response.status_code} - {response.text}")
            return jsonify({
                "success": False,
                "error": f"LM Studio error: {response.status_code}",
                "details": response.text
            }), response.status_code
            
    except requests.exceptions.RequestException as e:
        logger.error(f"Request error: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"Request failed: {str(e)}"
        }), 500
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"Internal error: {str(e)}"
        }), 500

if __name__ == '__main__':
    print("🧪 Starting Simple LM Studio Proxy Test")
    print(f"📡 LM Studio URL: {LM_STUDIO_URL}")
    print("🌐 Proxy running on http://localhost:8081")
    print("\nTest Endpoints:")
    print("  GET  /health      - Health check")
    print("  POST /simple-test - Simple text forwarding to LM Studio")
    print("\nTest with:")
    print('  curl -X POST http://localhost:8081/simple-test -H "Content-Type: application/json" -d \'{"text":"Hello test"}\'')
    
    app.run(host='127.0.0.1', port=8081, debug=True)
