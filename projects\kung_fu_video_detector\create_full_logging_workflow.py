#!/usr/bin/env python3
"""Create workflow with logging after every major node"""

import json
import sys
from pathlib import Path

def create_full_logging():
    """Add logging after every major node to trace complete execution flow."""
    print("🔧 Creating Full Per-Node Logging Workflow")
    print("=" * 60)
    
    try:
        # Load current workflow
        workflow_path = Path("kung_fu_video_workflow_fixed_logging.json")
        with open(workflow_path, 'r', encoding='utf-8') as f:
            workflow = json.load(f)
        
        print(f"✅ Current workflow loaded: {len(workflow['nodes'])} nodes")
        
        # Create logging nodes for each major step
        logging_nodes = []
        
        # 1. After Load Configuration
        config_log = {
            "parameters": {
                "jsCode": """const input = $input.first().json;
const timestamp = new Date().toISOString();
const logData = {
  step: '1_load_configuration',
  timestamp: timestamp,
  has_config: !!input.config,
  config_keys: input.config ? Object.keys(input.config) : [],
  video_folder: input.config ? input.config.video_folder : 'missing'
};
const binaryData = {
  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),
  mimeType: 'application/json',
  fileName: '1_config_log.json',
  fileExtension: 'json'
};
return [{json: {filename: '1_config_log.json'}, binary: {data: binaryData}}];"""
            },
            "id": "log-1-config",
            "name": "Log 1 Config",
            "type": "n8n-nodes-base.code",
            "typeVersion": 2,
            "position": [-2300, 200]
        }
        
        # 2. After Scan Folder (updated)
        scan_log = {
            "parameters": {
                "jsCode": """const input = $input.first().json;
const timestamp = new Date().toISOString();
const logData = {
  step: '2_scan_folder',
  timestamp: timestamp,
  exit_code: input.exitCode,
  stdout_length: input.stdout ? input.stdout.length : 0,
  files_found: input.stdout ? input.stdout.trim().split('\\n').filter(f => f.trim()).length : 0,
  stdout_preview: input.stdout ? input.stdout.substring(0, 200) : 'no stdout',
  stderr: input.stderr || 'no stderr',
  error: input.error || null
};
const binaryData = {
  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),
  mimeType: 'application/json',
  fileName: '2_scan_log.json',
  fileExtension: 'json'
};
return [{json: {filename: '2_scan_log.json'}, binary: {data: binaryData}}];"""
            },
            "id": "log-2-scan",
            "name": "Log 2 Scan",
            "type": "n8n-nodes-base.code",
            "typeVersion": 2,
            "position": [-2100, 200]
        }
        
        # 3. After Process File List (updated)
        process_log = {
            "parameters": {
                "jsCode": """const allInputs = $input.all();
const timestamp = new Date().toISOString();
const logData = {
  step: '3_process_file_list',
  timestamp: timestamp,
  total_inputs: allInputs.length,
  files: allInputs.map((input, i) => ({
    index: i,
    filename: input.json.filename || 'no filename',
    fullPath: input.json.fullPath || 'no path',
    hasConfig: !!input.json.config,
    error: input.json.error || null
  }))
};
const binaryData = {
  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),
  mimeType: 'application/json',
  fileName: '3_process_log.json',
  fileExtension: 'json'
};
return [{json: {filename: '3_process_log.json'}, binary: {data: binaryData}}];"""
            },
            "id": "log-3-process",
            "name": "Log 3 Process",
            "type": "n8n-nodes-base.code",
            "typeVersion": 2,
            "position": [-1900, 200]
        }
        
        # 4. After Extract Thumbnail
        thumbnail_log = {
            "parameters": {
                "jsCode": """const allInputs = $input.all();
const timestamp = new Date().toISOString();
const logData = {
  step: '4_extract_thumbnail',
  timestamp: timestamp,
  total_inputs: allInputs.length,
  thumbnails: allInputs.map((input, i) => ({
    index: i,
    filename: input.json.filename || 'no filename',
    hasThumbnail: !!input.json.thumbnailBase64,
    isMock: input.json.isMockThumbnail || false,
    thumbnailLength: input.json.thumbnailBase64 ? input.json.thumbnailBase64.length : 0
  }))
};
const binaryData = {
  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),
  mimeType: 'application/json',
  fileName: '4_thumbnail_log.json',
  fileExtension: 'json'
};
return [{json: {filename: '4_thumbnail_log.json'}, binary: {data: binaryData}}];"""
            },
            "id": "log-4-thumbnail",
            "name": "Log 4 Thumbnail",
            "type": "n8n-nodes-base.code",
            "typeVersion": 2,
            "position": [-1700, 200]
        }
        
        # 5. After AI Video Analysis
        ai_log = {
            "parameters": {
                "jsCode": """const allInputs = $input.all();
const timestamp = new Date().toISOString();
const logData = {
  step: '5_ai_analysis',
  timestamp: timestamp,
  total_inputs: allInputs.length,
  ai_responses: allInputs.map((input, i) => ({
    index: i,
    filename: input.json.filename || 'no filename',
    statusCode: input.json.statusCode || 'no status',
    hasResponse: !!input.json.aiResponse,
    responsePreview: input.json.aiResponse ? input.json.aiResponse.substring(0, 100) : 'no response',
    error: input.json.error || null
  }))
};
const binaryData = {
  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),
  mimeType: 'application/json',
  fileName: '5_ai_log.json',
  fileExtension: 'json'
};
return [{json: {filename: '5_ai_log.json'}, binary: {data: binaryData}}];"""
            },
            "id": "log-5-ai",
            "name": "Log 5 AI",
            "type": "n8n-nodes-base.code",
            "typeVersion": 2,
            "position": [-1500, 200]
        }
        
        # Create corresponding write nodes
        write_nodes = []
        for i, log_node in enumerate([config_log, scan_log, process_log, thumbnail_log, ai_log], 1):
            write_node = {
                "parameters": {
                    "fileName": "={{ '/home/<USER>/shared/' + $json.filename }}",
                    "options": {"overwrite": True}
                },
                "id": f"write-log-{i}",
                "name": f"Write Log {i}",
                "type": "n8n-nodes-base.writeBinaryFile",
                "typeVersion": 1,
                "position": [log_node["position"][0], log_node["position"][1] + 100]
            }
            write_nodes.append(write_node)
        
        # Add all logging nodes
        all_new_nodes = [config_log, scan_log, process_log, thumbnail_log, ai_log] + write_nodes
        workflow['nodes'].extend(all_new_nodes)
        
        # Add connections for logging nodes
        node_connections = [
            ("Load Configuration", "Log 1 Config"),
            ("Scan Folder for Videos", "Log 2 Scan"),
            ("Process File List", "Log 3 Process"),
            ("Extract Video Thumbnail", "Log 4 Thumbnail"),
            ("AI Video Analysis", "Log 5 AI")
        ]
        
        for source, target in node_connections:
            if source not in workflow['connections']:
                workflow['connections'][source] = {"main": [[]]}
            workflow['connections'][source]["main"][0].append({
                "node": target,
                "type": "main",
                "index": 0
            })
        
        # Connect log nodes to write nodes
        log_write_pairs = [
            ("Log 1 Config", "Write Log 1"),
            ("Log 2 Scan", "Write Log 2"),
            ("Log 3 Process", "Write Log 3"),
            ("Log 4 Thumbnail", "Write Log 4"),
            ("Log 5 AI", "Write Log 5")
        ]
        
        for log_node, write_node in log_write_pairs:
            workflow['connections'][log_node] = {
                "main": [[{"node": write_node, "type": "main", "index": 0}]]
            }
        
        # Save enhanced workflow
        output_path = Path("kung_fu_video_workflow_full_logging.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(workflow, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Full logging workflow saved: {output_path}")
        print(f"   Total nodes: {len(workflow['nodes'])}")
        print(f"   Added 10 logging nodes (5 log + 5 write)")
        
        print(f"\n🎯 Complete Execution Tracing:")
        print(f"   1. 1_config_log.json - After Load Configuration")
        print(f"   2. 2_scan_log.json - After Scan Folder")
        print(f"   3. 3_process_log.json - After Process File List")
        print(f"   4. 4_thumbnail_log.json - After Extract Thumbnail")
        print(f"   5. 5_ai_log.json - After AI Video Analysis")
        
        print(f"\n📋 This will show:")
        print(f"   - Exactly which step the workflow reaches")
        print(f"   - What data flows between each step")
        print(f"   - Where the workflow stops (missing log = stopped before that step)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating full logging: {e}")
        return False

if __name__ == "__main__":
    success = create_full_logging()
    sys.exit(0 if success else 1)
