#!/usr/bin/env python3
"""
Diagnostic script to understand why the N8N workflow completes in 1 second
This script analyzes the workflow structure and simulates the data flow
"""

import json
import os

def analyze_workflow_data_flow():
    """Analyze the complete data flow through the workflow"""
    
    workflow_path = "VideoProcessor_B.json"
    
    if not os.path.exists(workflow_path):
        print(f"❌ Workflow file not found: {workflow_path}")
        return
    
    print("🔍 DIAGNOSING WORKFLOW DATA FLOW")
    print("=" * 50)
    
    try:
        with open(workflow_path, 'r', encoding='utf-8') as f:
            workflow = json.load(f)
        
        nodes = workflow.get('nodes', [])
        connections = workflow.get('connections', {})
        
        # Find key nodes
        scan_node = None
        process_list_node = None
        split_node = None
        
        for node in nodes:
            name = node.get('name', '')
            if 'Scan Folder for Videos' in name:
                scan_node = node
            elif 'Log Process File List' in name:
                process_list_node = node
            elif 'Loop Over Items' in name:
                split_node = node
        
        print("📊 KEY NODES ANALYSIS:")
        print(f"   Scan Node: {'✅ Found' if scan_node else '❌ Missing'}")
        print(f"   Process List Node: {'✅ Found' if process_list_node else '❌ Missing'}")
        print(f"   SplitInBatches Node: {'✅ Found' if split_node else '❌ Missing'}")
        
        # Analyze the scan node
        if scan_node:
            print(f"\n🔍 SCAN NODE ANALYSIS:")
            scan_code = scan_node.get('parameters', {}).get('jsCode', '')
            
            # Check if it returns multiple items
            if 'return allInputs' in scan_code or 'return videoFiles' in scan_code:
                print(f"   ✅ Returns multiple items")
            elif 'return [' in scan_code:
                print(f"   ✅ Returns array")
            else:
                print(f"   ⚠️  Return pattern unclear")
                
            # Look for video file patterns
            if '.mp4' in scan_code.lower() or '.avi' in scan_code.lower():
                print(f"   ✅ Processes video files")
            else:
                print(f"   ⚠️  Video file processing unclear")
        
        # Analyze the process list node
        if process_list_node:
            print(f"\n🔍 PROCESS LIST NODE ANALYSIS:")
            process_code = process_list_node.get('parameters', {}).get('jsCode', '')
            
            if 'return allInputs' in process_code:
                print(f"   ✅ Passes through all inputs (correct for SplitInBatches)")
            elif 'return [' in process_code:
                print(f"   ✅ Returns array")
            else:
                print(f"   ❌ May not be passing data correctly")
                
            # Check for logging
            if 'console.log' in process_code:
                print(f"   ✅ Has logging for debugging")
            else:
                print(f"   ⚠️  No logging for debugging")
        
        # Analyze SplitInBatches configuration
        if split_node:
            print(f"\n🔍 SPLITINBATCHES NODE ANALYSIS:")
            params = split_node.get('parameters', {})
            batch_size = params.get('batchSize', 'Not set')
            
            print(f"   batchSize: {batch_size}")
            
            if batch_size == 1:
                print(f"   ✅ Correct: Will process items individually")
            else:
                print(f"   ❌ Incorrect: Should be 1 for individual processing")
        
        # Analyze connections from SplitInBatches
        split_connections = connections.get('Loop Over Items', {})
        main_outputs = split_connections.get('main', [])
        
        print(f"\n🔍 SPLITINBATCHES CONNECTIONS ANALYSIS:")
        print(f"   Total outputs: {len(main_outputs)}")
        
        if len(main_outputs) >= 2:
            # Output 0 analysis
            output_0 = main_outputs[0]
            if output_0 and len(output_0) > 0:
                target_0 = output_0[0].get('node', 'Unknown')
                print(f"   Output 0 (Loop): → {target_0}")
                
                if 'Create FFmpeg Request' in target_0:
                    print(f"     ✅ Correct: Goes to processing chain")
                elif 'Group Videos by Date' in target_0:
                    print(f"     ❌ Wrong: Should go to processing, not final step")
                else:
                    print(f"     ⚠️  Unexpected target")
            else:
                print(f"   Output 0 (Loop): Empty")
                
            # Output 1 analysis
            output_1 = main_outputs[1]
            if output_1 and len(output_1) > 0:
                target_1 = output_1[0].get('node', 'Unknown')
                print(f"   Output 1 (Done): → {target_1}")
                
                if 'Group Videos by Date' in target_1:
                    print(f"     ✅ Correct: Goes to final step when done")
                elif 'Create FFmpeg Request' in target_1:
                    print(f"     ❌ Wrong: Should go to final step, not processing")
                else:
                    print(f"     ⚠️  Unexpected target")
            else:
                print(f"   Output 1 (Done): Empty")
        
        # Check what feeds into SplitInBatches
        feeding_nodes = []
        for node_name, node_connections in connections.items():
            main_conns = node_connections.get('main', [])
            for output_group in main_conns:
                for connection in output_group:
                    if connection.get('node') == 'Loop Over Items':
                        feeding_nodes.append(node_name)
        
        print(f"\n🔍 NODES FEEDING INTO SPLITINBATCHES:")
        for node in feeding_nodes:
            print(f"   📥 {node}")
            
        # Check loop back connection
        loop_back_node = connections.get('Log Write Log to Shared Folder', {})
        loop_back_main = loop_back_node.get('main', [])
        
        print(f"\n🔍 LOOP BACK CONNECTION:")
        if loop_back_main and len(loop_back_main) > 0 and len(loop_back_main[0]) > 0:
            loop_target = loop_back_main[0][0].get('node', 'Unknown')
            print(f"   'Log Write Log to Shared Folder' → {loop_target}")
            
            if loop_target == 'Loop Over Items':
                print(f"   ✅ Correct: Loops back to SplitInBatches")
            else:
                print(f"   ❌ Wrong: Should loop back to SplitInBatches")
        else:
            print(f"   ❌ No loop back connection found")
        
        # Overall diagnosis
        print(f"\n🎯 DIAGNOSIS:")
        
        # Check for common issues
        issues = []
        
        if not scan_node:
            issues.append("Missing scan node")
        if not process_list_node:
            issues.append("Missing process list node")
        if not split_node:
            issues.append("Missing SplitInBatches node")
            
        if split_node and split_node.get('parameters', {}).get('batchSize') != 1:
            issues.append("SplitInBatches batchSize not set to 1")
            
        # Check connections
        if len(main_outputs) >= 2:
            output_0_target = main_outputs[0][0].get('node', '') if main_outputs[0] else ''
            output_1_target = main_outputs[1][0].get('node', '') if main_outputs[1] else ''
            
            if 'Create FFmpeg Request' not in output_0_target:
                issues.append("SplitInBatches Output 0 doesn't go to processing")
            if 'Group Videos by Date' not in output_1_target:
                issues.append("SplitInBatches Output 1 doesn't go to final step")
        
        if issues:
            print(f"   ❌ ISSUES FOUND:")
            for issue in issues:
                print(f"     • {issue}")
        else:
            print(f"   ✅ CONFIGURATION APPEARS CORRECT")
            print(f"   🤔 Issue may be with data content, not structure")
            print(f"   💡 Suggestion: Check if scan node is finding video files")
            print(f"   💡 Suggestion: Add more logging to trace data flow")
        
    except Exception as e:
        print(f"❌ Error analyzing workflow: {e}")

def suggest_debugging_steps():
    """Suggest next debugging steps"""
    
    print(f"\n🛠️  SUGGESTED DEBUGGING STEPS:")
    print(f"1. 📝 Add console.log to 'Scan Folder for Videos' node")
    print(f"2. 📝 Add console.log to 'Log Process File List' node")
    print(f"3. 📝 Check N8N execution logs for actual data being passed")
    print(f"4. 🔍 Verify test_videos folder contains the expected 5 video files")
    print(f"5. 🧪 Create a minimal test workflow with just Manual Trigger → Scan → SplitInBatches")
    print(f"6. 📊 Monitor N8N execution time - if it's truly 1 second, data isn't reaching SplitInBatches")

def main():
    """Main diagnostic function"""
    analyze_workflow_data_flow()
    suggest_debugging_steps()

if __name__ == "__main__":
    main()
