{"name": "Code Node LM Studio Test", "nodes": [{"parameters": {}, "id": "manual-trigger-code", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"jsCode": "// Prepare LM Studio request data\nconst requestData = {\n  model: \"mimo-vl-7b-rl@q8_k_xl\",\n  messages: [\n    {\n      role: \"user\",\n      content: \"Hello, please respond with just OK\"\n    }\n  ],\n  temperature: 0.1,\n  max_tokens: 5\n};\n\nconsole.log('Prepared request data:', JSON.stringify(requestData, null, 2));\n\nreturn [{ json: requestData }];"}, "id": "prepare-request-data", "name": "Prepare Request Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [450, 300]}, {"parameters": {"method": "POST", "url": "http://host.docker.internal:1234/v1/chat/completions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyContentType": "json", "jsonBody": "={{ JSON.stringify($json) }}", "options": {"timeout": 30000}}, "id": "code-http-request", "name": "Code HTTP Request", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [650, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Prepare Request Data", "type": "main", "index": 0}]]}, "Prepare Request Data": {"main": [[{"node": "Code HTTP Request", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}