{"name": "Basic Connectivity Test", "nodes": [{"parameters": {}, "id": "manual-trigger-basic", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"method": "GET", "url": "http://host.docker.internal:1234/v1/models", "options": {"timeout": 10000}}, "id": "basic-http-request", "name": "Basic HTTP Request", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [500, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Basic HTTP Request", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}