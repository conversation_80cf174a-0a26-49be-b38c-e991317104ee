import json
import base64
import os
from pathlib import Path

def extract_thumbnails():
    """Extract thumbnails from FFmpeg results for visual inspection"""
    
    ffmpeg_results_dir = Path("C:/Docker_Share/N8N/ffmpeg_results")
    output_dir = Path("C:/Docker_Share/N8N/extracted_thumbnails")
    
    # Create output directory
    output_dir.mkdir(exist_ok=True)
    
    # Process all result files
    for result_file in ffmpeg_results_dir.glob("result_*.json"):
        try:
            with open(result_file, 'r') as f:
                data = json.load(f)
            
            if data.get('success') and 'thumbnail_base64' in data:
                # Decode base64 thumbnail
                thumbnail_data = base64.b64decode(data['thumbnail_base64'])
                
                # Create output filename
                filename = data.get('filename', 'unknown')
                output_file = output_dir / f"thumbnail_{filename.replace('.', '_')}.png"
                
                # Write thumbnail
                with open(output_file, 'wb') as f:
                    f.write(thumbnail_data)
                
                print(f"Extracted thumbnail for {filename} -> {output_file}")
                
        except Exception as e:
            print(f"Error processing {result_file}: {e}")

if __name__ == "__main__":
    extract_thumbnails()
