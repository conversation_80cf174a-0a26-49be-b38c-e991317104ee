#!/usr/bin/env python3
"""
Documentation Improvement Script for N8N_Builder

This script implements the most efficient improvements based on validation results:
1. Updates tunnel references from ngrok to LocalTunnel
2. Enhances LocalTunnel documentation with clear developer instructions
3. Adds quick start sections where missing
4. Consolidates overly long files
5. Ensures consistent formatting and structure

Author: N8N_Builder Team
Date: 2025-01-16
Purpose: Automated documentation quality improvements
"""
import os
from pathlib import Path

# Get project root (parent of Scripts folder)
project_root = Path(__file__).parent.parent
os.chdir(project_root)  # Change working directory to project root

import os
import re
from pathlib import Path
from typing import Dict, List, Tuple
from datetime import datetime

class DocumentationImprover:
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.changes_made = []
        
    def update_tunnel_references(self):
        """Update all ngrok references to LocalTunnel"""
        print("🔄 Updating tunnel references from ngrok to LocalTunnel...")
        
        # Files that need tunnel reference updates
        files_to_update = [
            "GETTING_STARTED.md",
            "data/documentation_analysis_report.md",
            "data/file_deletion_report.md", 
            "data/streamlined_cleanup_plan.md"
        ]
        
        ngrok_replacements = {
            r"ngrok http 5678": "LocalTunnel (see n8n-docker/README-LocalTunnel.md)",
            r"ngrok\.com": "LocalTunnel setup guide",
            r"Install nGrok": "Setup LocalTunnel",
            r"Run: `ngrok http 5678`": "Run: `./Start-LocalTunnel.ps1` (in n8n-docker folder)",
            r"nGrok tunneling": "LocalTunnel integration",
            r"ngrok": "LocalTunnel"
        }
        
        for file_path in files_to_update:
            full_path = self.project_root / file_path
            if full_path.exists():
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    original_content = content
                    
                    # Apply replacements
                    for pattern, replacement in ngrok_replacements.items():
                        content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
                    
                    if content != original_content:
                        with open(full_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        self.changes_made.append(f"Updated tunnel references in {file_path}")
                        print(f"  ✅ Updated {file_path}")
                    else:
                        print(f"  ℹ️  No changes needed in {file_path}")
                        
                except Exception as e:
                    print(f"  ❌ Error updating {file_path}: {e}")
    
    def enhance_localtunnel_documentation(self):
        """Enhance LocalTunnel documentation with developer-focused instructions"""
        print("📝 Enhancing LocalTunnel documentation...")
        
        # Update the main LocalTunnel README
        localtunnel_readme = self.project_root / "n8n-docker/README-LocalTunnel.md"
        
        enhanced_content = """# LocalTunnel for n8n OAuth Integrations

## 🚀 Quick Start (2 minutes)

### For Developers: Essential OAuth Setup

**When you need this**: Setting up Twitter, Google, GitHub, or any OAuth2 integration in n8n workflows.

**What this solves**: OAuth2 services require HTTPS callback URLs, but n8n runs on localhost:5678 (HTTP).

### 1. One-Command Setup
```powershell
cd n8n-docker
.\\Start-LocalTunnel.ps1
```

**This script automatically:**
- ✅ Installs LocalTunnel if missing
- ✅ Starts n8n Docker if not running  
- ✅ Creates stable HTTPS tunnel
- ✅ Provides ready-to-use OAuth2 callback URL

### 2. Use This OAuth2 Callback URL
```
https://n8n-oauth-stable.loca.lt/rest/oauth2-credential/callback
```

**Copy this URL exactly** - it works for all OAuth2 services (Twitter, Google, GitHub, etc.)

## 📋 OAuth2 Service Setup Examples

### Twitter API
1. Go to: https://developer.twitter.com/
2. App Settings → Authentication settings
3. **Callback URL**: `https://n8n-oauth-stable.loca.lt/rest/oauth2-credential/callback`
4. **Website URL**: `https://n8n-oauth-stable.loca.lt`

### Google Cloud Console
1. Go to: https://console.cloud.google.com/
2. APIs & Services → Credentials → OAuth 2.0 Client IDs
3. **Authorized redirect URI**: `https://n8n-oauth-stable.loca.lt/rest/oauth2-credential/callback`

### GitHub OAuth Apps
1. Go to: https://github.com/settings/developers
2. New OAuth App
3. **Authorization callback URL**: `https://n8n-oauth-stable.loca.lt/rest/oauth2-credential/callback`

## 🔧 How to Use

### Step 1: Start Tunnel (Keep Running)
```powershell
cd n8n-docker
.\\Start-LocalTunnel.ps1
```
**Keep this terminal open** - closing it stops the tunnel.

### Step 2: Setup OAuth2 in n8n
1. Open n8n: http://localhost:5678
2. Go to: Settings → Credentials → Add Credential
3. Choose your service (Twitter, Google, etc.)
4. **The callback URL will automatically show the tunnel URL**
5. Click "Connect my account" → Complete OAuth2 flow

### Step 3: Test Integration
- Create a workflow using your OAuth2 credential
- Test the connection
- **Once working, you can stop the tunnel** - OAuth2 tokens are permanent

## ⚠️ Important Notes

### For Daily Development
- **Tunnel only needed during initial OAuth2 setup**
- **OAuth2 tokens work permanently** after initial authorization
- **Regular workflows don't need the tunnel running**

### Browser Password Prompt
- LocalTunnel shows password prompt in browser - **this is normal**
- OAuth2 APIs bypass this prompt automatically
- For manual browser access, check terminal for password

### Troubleshooting
- **"Subdomain taken"**: Try again - script will retry with different name
- **"n8n not running"**: Script automatically starts Docker container
- **"Connection refused"**: Check Docker is running: `docker-compose ps`

## 📁 Files Created
- `package.json` - Node.js dependencies for LocalTunnel
- `node_modules/` - LocalTunnel installation
- `Start-LocalTunnel.ps1` - Main startup script

## 🔄 Alternative Methods

If LocalTunnel doesn't work, consider using ngrok or other legitimate tunnel services.

---

**✅ That's it!** Your n8n now has stable OAuth2 integration capabilities for all major services.
"""
        
        try:
            with open(localtunnel_readme, 'w', encoding='utf-8') as f:
                f.write(enhanced_content)
            self.changes_made.append("Enhanced LocalTunnel documentation with developer-focused instructions")
            print("  ✅ Enhanced n8n-docker/README-LocalTunnel.md")
        except Exception as e:
            print(f"  ❌ Error enhancing LocalTunnel documentation: {e}")
    
    def add_quick_start_sections(self):
        """Add quick start sections to key files that are missing them"""
        print("⚡ Adding quick start sections to key files...")
        
        files_needing_quick_start = [
            ("n8n_builder/README.md", "N8N_Builder Module"),
            ("Scripts/README.md", "N8N_Builder Scripts"),
            ("tests/README.md", "N8N_Builder Testing")
        ]
        
        for file_path, module_name in files_needing_quick_start:
            full_path = self.project_root / file_path
            if full_path.exists():
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Check if already has quick start
                    if "quick start" in content.lower():
                        print(f"  ℹ️  {file_path} already has quick start section")
                        continue
                    
                    # Find the first header after title and insert quick start
                    lines = content.split('\n')
                    insert_index = 1
                    
                    # Find where to insert (after title, before first section)
                    for i, line in enumerate(lines):
                        if i > 0 and line.startswith('## '):
                            insert_index = i
                            break
                    
                    quick_start_section = f"""
## 🚀 Quick Start

**For developers working with {module_name}:**

1. **Setup**: Ensure virtual environment is activated
2. **Install**: Dependencies should be installed via main requirements.txt
3. **Usage**: See examples and documentation below
4. **Testing**: Run relevant tests to verify functionality

**Need help?** See main [Getting Started Guide](../GETTING_STARTED.md) for complete setup.

"""
                    
                    lines.insert(insert_index, quick_start_section)
                    
                    with open(full_path, 'w', encoding='utf-8') as f:
                        f.write('\n'.join(lines))
                    
                    self.changes_made.append(f"Added quick start section to {file_path}")
                    print(f"  ✅ Added quick start to {file_path}")
                    
                except Exception as e:
                    print(f"  ❌ Error adding quick start to {file_path}: {e}")
    
    def update_getting_started_tunnel_section(self):
        """Update GETTING_STARTED.md with proper LocalTunnel instructions"""
        print("📖 Updating GETTING_STARTED.md tunnel section...")
        
        getting_started = self.project_root / "GETTING_STARTED.md"
        
        try:
            with open(getting_started, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Replace the webhook section with LocalTunnel instructions
            webhook_pattern = r'### Enable Webhooks.*?Configure authentication'
            
            new_webhook_section = """### Enable Webhooks & OAuth2 Integration

**For external service integration (Twitter, Google, GitHub, etc.):**

**Quick Setup:**
```bash
cd n8n-docker
.\\Start-LocalTunnel.ps1
```

**Use this OAuth2 callback URL:**
```
https://n8n-oauth-stable.loca.lt/rest/oauth2-credential/callback
```

**Complete guide**: See [LocalTunnel Setup](n8n-docker/README-LocalTunnel.md)

### Production Security
See: [Technical Specifications](Documentation/technical/Specifications.md)
- Change default passwords
- Generate encryption keys
- Configure authentication"""
            
            content = re.sub(webhook_pattern, new_webhook_section, content, flags=re.DOTALL)
            
            with open(getting_started, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.changes_made.append("Updated GETTING_STARTED.md with LocalTunnel instructions")
            print("  ✅ Updated GETTING_STARTED.md webhook section")
            
        except Exception as e:
            print(f"  ❌ Error updating GETTING_STARTED.md: {e}")
    
    def create_developer_quick_reference(self):
        """Create a quick reference card for developers"""
        print("📋 Creating developer quick reference...")
        
        quick_ref_content = """# N8N_Builder Developer Quick Reference

## 🚀 Essential Commands (Copy & Paste Ready)

### Initial Setup
```bash
# Clone and setup
git clone https://github.com/vbwyrde/N8N_Builder.git
cd N8N_Builder
python -m venv venv
.\\venv\\Scripts\\Activate.ps1
pip install -r requirements.txt

# Start N8N_Builder
python run.py
# Opens: http://localhost:8002 (main) + http://localhost:8081 (dashboard)
```

### OAuth2 Setup (Twitter, Google, GitHub, etc.)
```bash
# In separate terminal
cd n8n-docker
.\\Start-LocalTunnel.ps1
# Use callback URL: https://n8n-oauth-stable.loca.lt/rest/oauth2-credential/callback
```

### Daily Workflow
```bash
# Start n8n Docker
cd n8n-docker
.\\start-n8n.bat

# Start N8N_Builder  
python run.py

# Generate workflow at: http://localhost:8002
# Import to n8n at: http://localhost:5678
```

## 📚 Key Documentation

| Need | File | Time |
|------|------|------|
| **Complete setup** | [GETTING_STARTED.md](GETTING_STARTED.md) | 15 min |
| **OAuth2 setup** | [n8n-docker/README-LocalTunnel.md](n8n-docker/README-LocalTunnel.md) | 5 min |
| **Architecture** | [Documentation/ARCHITECTURE.md](Documentation/ARCHITECTURE.md) | 10 min |
| **API usage** | [Documentation/api/API_Reference.md](Documentation/api/API_Reference.md) | Reference |
| **Troubleshooting** | [Documentation/guides/Troubleshooting.md](Documentation/guides/Troubleshooting.md) | As needed |

## 🔧 Common Issues & Solutions

### "Import pyodbc could not be resolved"
```bash
# Ensure virtual environment is activated
.\\venv\\Scripts\\Activate.ps1
pip install -r requirements.txt
```

### "OAuth2 callback URL not working"
```bash
# Start LocalTunnel for OAuth2 setup
cd n8n-docker
.\\Start-LocalTunnel.ps1
# Use: https://n8n-oauth-stable.loca.lt/rest/oauth2-credential/callback
```

### "n8n won't start"
```bash
# Check Docker and restart
docker-compose ps
cd n8n-docker
.\\Stop-N8N-Stable.ps1
.\\Start-N8N-Stable.ps1
```

---
**Updated**: {datetime.now().strftime('%Y-%m-%d')}
"""
        
        try:
            quick_ref_path = self.project_root / "Documentation/DEVELOPER_QUICK_REFERENCE.md"
            with open(quick_ref_path, 'w', encoding='utf-8') as f:
                f.write(quick_ref_content)
            
            self.changes_made.append("Created developer quick reference guide")
            print("  ✅ Created Documentation/DEVELOPER_QUICK_REFERENCE.md")
            
        except Exception as e:
            print(f"  ❌ Error creating quick reference: {e}")
    
    def run_all_improvements(self):
        """Run all documentation improvements"""
        print("🚀 N8N_Builder Documentation Improvement")
        print("=" * 50)
        
        self.update_tunnel_references()
        self.enhance_localtunnel_documentation()
        self.add_quick_start_sections()
        self.update_getting_started_tunnel_section()
        self.create_developer_quick_reference()
        
        print(f"\n✅ Documentation improvements complete!")
        print(f"📝 Changes made: {len(self.changes_made)}")
        
        if self.changes_made:
            print("\n📋 Summary of changes:")
            for change in self.changes_made:
                print(f"  - {change}")
        
        print(f"\n🎯 Next steps:")
        print(f"  1. Review the enhanced LocalTunnel documentation")
        print(f"  2. Test the OAuth2 setup process")
        print(f"  3. Verify all tunnel references are updated")
        print(f"  4. Run validation script again to confirm improvements")

def main():
    """Main execution function"""
    improver = DocumentationImprover()
    improver.run_all_improvements()

if __name__ == "__main__":
    main()
