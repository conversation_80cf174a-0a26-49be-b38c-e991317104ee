#!/usr/bin/env python3
"""
Test specific video that should contain kung fu content
"""

import json
import base64
import requests
import time
from pathlib import Path

def test_specific_video(target_filename="20250622_100122.mp4"):
    """Test a specific video that should contain kung fu content"""
    
    # Find the FFmpeg result for the target video
    ffmpeg_results_dir = Path("C:/Docker_Share/N8N/ffmpeg_results")
    
    target_result = None
    for result_file in ffmpeg_results_dir.glob("result_*.json"):
        try:
            with open(result_file, 'r') as f:
                data = json.load(f)
            
            if data.get('filename') == target_filename and data.get('success'):
                target_result = data
                break
                
        except Exception as e:
            continue
    
    if not target_result:
        print(f"No FFmpeg result found for {target_filename}")
        return
    
    thumbnail_base64 = target_result.get('thumbnail_base64', '')
    if not thumbnail_base64:
        print(f"No thumbnail data found for {target_filename}")
        return
    
    print(f"Testing vision analysis for: {target_filename}")
    print(f"This video should contain 'Bagua - Dragon Piercing Palm' content")
    
    # Test with the improved prompt
    improved_prompt = """Analyze this video thumbnail for kung fu or martial arts content. Look for:
- Martial arts poses, stances, or movements
- Fighting techniques or combat training
- Traditional Chinese martial arts (kung fu, wushu, tai chi)
- Training equipment (wooden dummies, weapons, mats)
- Text mentioning martial arts terms (kung fu, wushu, bagua, tai chi, etc.)
- People in martial arts uniforms or practicing forms

If you see ANY of these elements, respond with YES. Only respond with NO if there are clearly no martial arts elements present. Be more inclusive rather than restrictive in your analysis."""
    
    payload = {
        "model": "mimo-vl-7b-rl@q8_k_xl",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": improved_prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{thumbnail_base64}"
                        }
                    }
                ]
            }
        ],
        "temperature": 0.4,
        "max_tokens": 200,
        "stream": False
    }
    
    try:
        print("Sending request to LM Studio...")
        response = requests.post(
            "http://localhost:1234/v1/chat/completions",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            # Extract YES/NO
            analysis_result = "YES" if "YES" in content.upper() else "NO"
            
            print(f"\n=== RESULT ===")
            print(f"Analysis: {analysis_result}")
            print(f"Full Response: {content}")
            
            # Save result
            output_file = Path("C:/Docker_Share/N8N/specific_video_test.json")
            with open(output_file, 'w') as f:
                json.dump({
                    "filename": target_filename,
                    "analysis_result": analysis_result,
                    "full_response": content,
                    "test_timestamp": time.time()
                }, f, indent=2)
            
            print(f"\nResult saved to: {output_file}")
            
        else:
            print(f"Error: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_specific_video()
