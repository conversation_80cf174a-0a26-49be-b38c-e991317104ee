{"name": "Mini POST Test", "nodes": [{"parameters": {}, "id": "manual-trigger-mini", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"jsCode": "// Test what HTTP methods are available in N8N Code nodes\nconsole.log('=== N8N CODE NODE HTTP CAPABILITIES TEST ===');\n\n// Check what's available\nconsole.log('typeof fetch:', typeof fetch);\nconsole.log('typeof $http:', typeof $http);\nconsole.log('typeof require:', typeof require);\nconsole.log('typeof XMLHttpRequest:', typeof XMLHttpRequest);\n\n// List all available globals\nconsole.log('Available globals:');\nconst globals = Object.getOwnPropertyNames(globalThis);\nglobals.forEach(name => {\n  if (name.includes('http') || name.includes('Http') || name.includes('request') || name.includes('Request')) {\n    console.log(`  ${name}: ${typeof globalThis[name]}`);\n  }\n});\n\n// Check if we can access any HTTP-related functionality\nconst availableMethods = [];\nif (typeof fetch !== 'undefined') availableMethods.push('fetch');\nif (typeof $http !== 'undefined') availableMethods.push('$http');\nif (typeof XMLHttpRequest !== 'undefined') availableMethods.push('XMLHttpRequest');\n\nconsole.log('Available HTTP methods:', availableMethods);\n\n// Try to find any way to make HTTP requests\ntry {\n  // Check if we can require node modules\n  const http = require('http');\n  console.log('SUCCESS: require(\"http\") works!');\n  availableMethods.push('require-http');\n} catch (e) {\n  console.log('require(\"http\") failed:', e.message);\n}\n\ntry {\n  // Check if we can require https\n  const https = require('https');\n  console.log('SUCCESS: require(\"https\") works!');\n  availableMethods.push('require-https');\n} catch (e) {\n  console.log('require(\"https\") failed:', e.message);\n}\n\nreturn [{ json: { \n  availableMethods: availableMethods,\n  canMakeHttpRequests: availableMethods.length > 0,\n  recommendation: availableMethods.length > 0 ? 'Use available method' : 'Must use HTTP Request node'\n} }];"}, "id": "mini-post-test-code", "name": "Mini POST Test Code", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [500, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Mini POST Test Code", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}