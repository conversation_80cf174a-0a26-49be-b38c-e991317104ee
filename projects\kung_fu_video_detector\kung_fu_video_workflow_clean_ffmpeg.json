{"name": "Kung Fu Video Detector - Clean FFmpeg", "nodes": [{"parameters": {}, "id": "ed85e2e5-dc5e-4575-88eb-afd38180a7c8", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-2800, -80]}, {"parameters": {"jsCode": "// Load configuration\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',\n  recursive_search: true,\n  file_extensions: ['.mp4', '.avi', '.mov', '.mkv'],\n  lm_studio: {\n    endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n    model: 'mimo-vl-7b-rl@q8_k_xl',\n    timeout: 30000,\n    temperature: 0.1,\n    max_tokens: 50\n  },\n  ai_prompt: 'Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training.',\n  ffmpeg_path: 'ffmpeg',\n  detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n};\n\nconsole.log('=== STEP 1: CONFIGURATION LOADED ===');\nconsole.log('Config details:', {\n  video_folder: config.video_folder,\n  lm_studio_endpoint: config.lm_studio.endpoint,\n  keywords_count: config.detection_keywords.length,\n  timestamp: new Date().toISOString()\n});\nreturn [{ json: { config: config } }];"}, "id": "dee3c1e8-483c-4376-a78a-381b062af29f", "name": "Load Configuration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2580, -80]}, {"parameters": {"jsCode": "// Log Step 1: Configuration Loading\nconst input = $input.first().json;\nconst timestamp = new Date().toISOString();\n\nconst stepLog = {\n  step: '1_load_configuration',\n  timestamp: timestamp,\n  config_loaded: !!input.config,\n  video_folder: input.config?.video_folder,\n  lm_studio_endpoint: input.config?.lm_studio?.endpoint,\n  detection_keywords_count: input.config?.detection_keywords?.length || 0,\n  status: 'completed'\n};\n\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(stepLog, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: '1_config_step_log.json',\n  fileExtension: 'json'\n};\n\nconsole.log('=== STEP 1 LOG CREATED ===');\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "log-step-1", "name": "Log Step 1 - Config", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2400, -80]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/' + $json.binary.data.fileName }}", "dataPropertyName": "data", "options": {"overwrite": true}}, "id": "write-step-1-log", "name": "Write Step 1 Log", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [-2400, -200]}, {"parameters": {"command": "find \"/home/<USER>/shared/kung_fu_videos\" -type f \\( -iname \"*.mp4\" -o -iname \"*.avi\" -o -iname \"*.mov\" -o -iname \"*.mkv\" \\) 2>/dev/null"}, "id": "05a21cf5-9a6b-4b45-a985-afad83287fac", "name": "<PERSON>an Folder for Videos", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-2360, -80]}, {"parameters": {"jsCode": "// Process scan results\nconst input = $input.first().json;\nconst commandOutput = input.stdout || '';\nconst exitCode = input.exitCode || 0;\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',\n  file_extensions: ['.mp4', '.avi', '.mov', '.mkv'],\n  lm_studio: {\n    endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n    model: 'mimo-vl-7b-rl@q8_k_xl',\n    timeout: 30000,\n    temperature: 0.1,\n    max_tokens: 50\n  },\n  ai_prompt: 'Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training.',\n  detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n};\n\nconsole.log('=== PROCESSING VIDEO FILE SCAN ===');\nconsole.log('Exit code:', exitCode);\n\nif (exitCode !== 0 || !commandOutput || commandOutput.trim() === '') {\n  console.log('No video files found');\n  return [{ json: { error: 'No video files found', config: config } }];\n}\n\nconst fileLines = commandOutput.trim().split('\\n').filter(line => line.trim() !== '');\nconst videoFiles = [];\n\nfor (const line of fileLines) {\n  const cleanLine = line.trim();\n  if (cleanLine) {\n    const filename = cleanLine.split('/').pop();\n    videoFiles.push({\n      filename: filename,\n      fullPath: cleanLine,\n      config: config\n    });\n  }\n}\n\nconsole.log(`Found ${videoFiles.length} video files`);\nreturn videoFiles.map(file => ({ json: file }));"}, "id": "8d8dfd08-5390-4441-a960-604631bd3a8a", "name": "Process File List", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2140, -80]}, {"parameters": {"jsCode": "// Log Step 2: File Processing\nconst allInputs = $input.all();\nconst timestamp = new Date().toISOString();\n\nconst stepLog = {\n  step: '2_process_files',\n  timestamp: timestamp,\n  total_files_found: allInputs.length,\n  files: allInputs.map((input, index) => ({\n    index: index,\n    filename: input.json.filename,\n    fullPath: input.json.fullPath,\n    has_config: !!input.json.config\n  })),\n  status: 'completed'\n};\n\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(stepLog, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: '2_process_files_log.json',\n  fileExtension: 'json'\n};\n\nconsole.log('=== STEP 2 LOG CREATED ===');\n// Pass through all inputs plus add logging data to first input\nconst results = allInputs.map(input => ({ json: input.json }));\nresults[0].binary = { data: binaryData };\nreturn results;"}, "id": "log-step-2", "name": "Log Step 2 - Process Files", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1980, -80]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/' + $json.binary.data.fileName }}", "dataPropertyName": "data", "options": {"overwrite": true}}, "id": "write-step-2-log", "name": "Write Step 2 Log", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [-1980, -200]}, {"parameters": {"command": "ffmpeg -i \"/home/<USER>/shared/kung_fu_videos/20250406_110016_1.mp4\" -ss 00:00:10 -vframes 1 -vf scale=320:240 -f image2pipe -vcodec png -"}, "id": "53e8d6ae-acbc-4f48-ae64-3b7e8e876606", "name": "Extract Video Thumbnail", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-1920, -80]}, {"parameters": {"jsCode": "// Process real FFmpeg thumbnail extraction result\nconst input = $input.first().json;\nconst rawImageData = input.stdout || '';\nconst exitCode = input.exitCode || 0;\nconst stderr = input.stderr || '';\n\n// Hardcoded values to match our FFmpeg command\nconst filename = '20250406_110016_1.mp4';\nconst fullPath = '/home/<USER>/shared/kung_fu_videos/20250406_110016_1.mp4';\n\nconst config = {\n  lm_studio: {\n    endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n    model: 'mimo-vl-7b-rl@q8_k_xl',\n    timeout: 30000,\n    temperature: 0.1,\n    max_tokens: 50\n  },\n  ai_prompt: 'Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training. Look for martial arts poses, fighting stances, training equipment, or combat movements. Please respond with only YES if it shows kung fu/martial arts practice, or NO if it does not.',\n  detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n};\n\nconsole.log('=== PROCESSING REAL FFMPEG THUMBNAIL ===');\nconsole.log(`File: ${filename}`);\nconsole.log(`Exit code: ${exitCode}`);\nconsole.log(`Raw data length: ${rawImageData.length}`);\n\nif (exitCode !== 0 || !rawImageData || rawImageData.length < 1000) {\n  console.log('ERROR: FFmpeg extraction failed');\n  return [{\n    json: {\n      filename: filename,\n      error: 'FFmpeg extraction failed',\n      success: false,\n      thumbnailBase64: null\n    }\n  }];\n}\n\n// Convert raw PNG data to base64\nconst thumbnailBase64 = Buffer.from(rawImageData, 'binary').toString('base64');\n\nconsole.log('=== STEP 3: THUMBNAIL EXTRACTION SUCCESS ===');\nconsole.log('Thumbnail details:', {\n  filename: filename,\n  ffmpeg_exit_code: exitCode,\n  raw_data_length: rawImageData.length,\n  thumbnail_size_kb: Math.round(thumbnailBase64.length / 1024),\n  timestamp: new Date().toISOString()\n});\n\nreturn [{\n  json: {\n    filename: filename,\n    fullPath: fullPath,\n    config: config,\n    thumbnailBase64: thumbnailBase64,\n    success: true,\n    thumbnailSizeKB: Math.round(thumbnailBase64.length / 1024)\n  }\n}];"}, "id": "94f5e89e-58d7-4cfa-a209-77ff9dae38ec", "name": "Process Thumbnail", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1700, -80]}, {"parameters": {"jsCode": "// Log Step 3: Thumbnail Processing\nconst input = $input.first().json;\nconst timestamp = new Date().toISOString();\n\nconst stepLog = {\n  step: '3_thumbnail_processing',\n  timestamp: timestamp,\n  filename: input.filename,\n  success: input.success,\n  thumbnail_size_kb: input.thumbnailSizeKB || 0,\n  has_thumbnail_base64: !!input.thumbnailBase64,\n  thumbnail_base64_length: input.thumbnailBase64?.length || 0,\n  error: input.error || null,\n  status: input.success ? 'completed' : 'failed'\n};\n\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(stepLog, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: '3_thumbnail_log.json',\n  fileExtension: 'json'\n};\n\nconsole.log('=== STEP 3 LOG CREATED ===');\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "log-step-3", "name": "Log Step 3 - Thumbnail", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1540, -80]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/' + $json.binary.data.fileName }}", "dataPropertyName": "data", "options": {"overwrite": true}}, "id": "write-step-3-log", "name": "Write Step 3 Log", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [-1540, -200]}, {"parameters": {"url": "={{ $json.config.lm_studio.endpoint }}", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "jsonBody": "={{ JSON.stringify({\n  model: $json.config.lm_studio.model,\n  messages: [\n    {\n      role: 'user',\n      content: [\n        {\n          type: 'text',\n          text: $json.config.ai_prompt\n        },\n        {\n          type: 'image_url',\n          image_url: {\n            url: 'data:image/png;base64,' + $json.thumbnailBase64\n          }\n        }\n      ]\n    }\n  ],\n  temperature: $json.config.lm_studio.temperature,\n  max_tokens: $json.config.lm_studio.max_tokens\n}) }}", "options": {"timeout": 30000}}, "id": "7aca14f2-b38c-45c7-9246-d936ac98523b", "name": "AI Video Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 2, "position": [-1480, -80]}, {"parameters": {"jsCode": "// Process AI response and determine kung fu detection\nconst input = $input.first().json;\nconst filename = input.filename || '20250406_110016_1.mp4';\nconst aiResponse = input.choices?.[0]?.message?.content || '';\n\n// Recreate config since it may not be passed through HTTP request\nconst config = {\n  detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n};\n\nconsole.log('=== STEP 4: PROCESSING AI RESPONSE ===');\nconsole.log('AI Analysis details:', {\n  filename: filename,\n  ai_response: aiResponse,\n  ai_response_length: aiResponse.length,\n  timestamp: new Date().toISOString()\n});\n\n// Check if response contains kung fu detection keywords\nconst responseText = aiResponse.toLowerCase();\nconst isKungFu = config.detection_keywords.some(keyword => \n  responseText.includes(keyword.toLowerCase())\n);\n\nconsole.log('Detection results:', {\n  is_kung_fu: isKungFu,\n  keywords_checked: config.detection_keywords,\n  response_text_sample: responseText.substring(0, 100)\n});\n\nreturn [{\n  json: {\n    filename: filename,\n    fullPath: input.fullPath || '/home/<USER>/shared/kung_fu_videos/20250406_110016_1.mp4',\n    config: config,\n    aiResponse: aiResponse,\n    isKungFu: isKungFu,\n    detectionConfidence: isKungFu ? 'high' : 'low',\n    thumbnailBase64: input.thumbnailBase64\n  }\n}];"}, "id": "ba32d901-fdc2-4123-820b-d6b119b51003", "name": "Process AI Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1260, -80]}, {"parameters": {"jsCode": "// Log Step 4: AI Analysis\nconst input = $input.first().json;\nconst timestamp = new Date().toISOString();\n\nconst stepLog = {\n  step: '4_ai_analysis',\n  timestamp: timestamp,\n  filename: input.filename,\n  ai_response: input.aiResponse,\n  ai_response_length: input.aiResponse?.length || 0,\n  is_kung_fu: input.isKungFu,\n  detection_confidence: input.detectionConfidence,\n  keywords_checked: input.config?.detection_keywords || [],\n  status: 'completed'\n};\n\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(stepLog, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: '4_ai_analysis_log.json',\n  fileExtension: 'json'\n};\n\nconsole.log('=== STEP 4 LOG CREATED ===');\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "log-step-4", "name": "Log Step 4 - AI Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1100, -80]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/' + $json.binary.data.fileName }}", "dataPropertyName": "data", "options": {"overwrite": true}}, "id": "write-step-4-log", "name": "Write Step 4 Log", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [-1100, -200]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.isKungFu }}", "value2": true}]}}, "id": "3a057643-21f7-4760-bed5-7cad13855449", "name": "Filter Kung Fu Videos", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1040, -80]}, {"parameters": {"jsCode": "// Collect final results from all processed videos\nconst allInputs = $input.all();\nconst timestamp = new Date().toISOString().replace(/[:.]/g, '-');\n\nconsole.log('=== STEP 5: COLLECTING FINAL RESULTS ===');\nconsole.log('Final collection details:', {\n  total_inputs: allInputs.length,\n  timestamp: new Date().toISOString()\n});\n\nconst kungFuVideos = allInputs.filter(input => input.json.isKungFu);\nconst nonKungFuVideos = allInputs.filter(input => !input.json.isKungFu);\n\nconst finalReport = {\n  workflow_name: 'Kung Fu Video Detector - Complete with Real FFmpeg',\n  execution_timestamp: new Date().toISOString(),\n  total_videos_analyzed: allInputs.length,\n  kung_fu_videos_found: kungFuVideos.length,\n  non_kung_fu_videos: nonKungFuVideos.length,\n  kung_fu_videos: kungFuVideos.map(input => ({\n    filename: input.json.filename,\n    ai_response: input.json.aiResponse,\n    confidence: input.json.detectionConfidence\n  })),\n  all_videos: allInputs.map(input => ({\n    filename: input.json.filename,\n    is_kung_fu: input.json.isKungFu,\n    ai_response: input.json.aiResponse\n  }))\n};\n\nconsole.log(`Results Summary:`);\nconsole.log(`  - Total videos: ${finalReport.total_videos_analyzed}`);\nconsole.log(`  - Kung Fu videos: ${finalReport.kung_fu_videos_found}`);\nconsole.log(`  - Non-Kung Fu videos: ${finalReport.non_kung_fu_videos}`);\n\nreturn [{\n  json: {\n    report: finalReport,\n    filename: `kung_fu_detection_report_${timestamp}.json`\n  }\n}];"}, "id": "5470ad10-2afd-43f4-9e3d-da82f3dfcbf0", "name": "Collect Final Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-820, -80]}, {"parameters": {"jsCode": "// Create comprehensive execution log\nconst allInputs = $input.all();\nconst timestamp = new Date().toISOString().replace(/[:.]/g, '-');\n\nconsole.log('=== CREATING EXECUTION LOG ===');\n\nconst executionLog = {\n  workflow_name: 'Kung Fu Video Detector - Complete with Real FFmpeg',\n  execution_timestamp: new Date().toISOString(),\n  total_inputs: allInputs.length,\n  architecture_notes: {\n    scan_method: 'executeCommand with hardcoded find',\n    extraction_method: 'executeCommand with real FFmpeg (optimized)',\n    ai_analysis: 'HTTP request to LM Studio vision model',\n    filtering: 'IF node for kung fu detection',\n    no_mock_fallbacks: 'Real errors logged, no fake data'\n  },\n  summary: allInputs.length > 0 ? allInputs[0].json.report : {}\n};\n\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(executionLog, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: `execution_log_${timestamp}.json`,\n  fileExtension: 'json'\n};\n\nreturn [{\n  json: {\n    filename: `execution_log_${timestamp}.json`,\n    log: executionLog\n  },\n  binary: { data: binaryData }\n}];"}, "id": "create-execution-log", "name": "Create Execution Log", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-600, -80]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/' + $json.filename }}", "options": {"overwrite": true}}, "id": "write-log-file", "name": "Write Log to Shared Folder", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [-380, -80]}], "connections": {"Manual Trigger": {"main": [[{"node": "Load Configuration", "type": "main", "index": 0}]]}, "Load Configuration": {"main": [[{"node": "<PERSON>an Folder for Videos", "type": "main", "index": 0}]]}, "Scan Folder for Videos": {"main": [[{"node": "Process File List", "type": "main", "index": 0}]]}, "Process File List": {"main": [[{"node": "Extract Video Thumbnail", "type": "main", "index": 0}]]}, "Extract Video Thumbnail": {"main": [[{"node": "Process Thumbnail", "type": "main", "index": 0}]]}, "Process Thumbnail": {"main": [[{"node": "AI Video Analysis", "type": "main", "index": 0}]]}, "AI Video Analysis": {"main": [[{"node": "Process AI Response", "type": "main", "index": 0}]]}, "Process AI Response": {"main": [[{"node": "Filter Kung Fu Videos", "type": "main", "index": 0}]]}, "Filter Kung Fu Videos": {"main": [[{"node": "Collect Final Results", "type": "main", "index": 0}], [{"node": "Collect Final Results", "type": "main", "index": 0}]]}, "Collect Final Results": {"main": [[{"node": "Create Execution Log", "type": "main", "index": 0}]]}, "Create Execution Log": {"main": [[{"node": "Write Log to Shared Folder", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}