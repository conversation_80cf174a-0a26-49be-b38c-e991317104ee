#!/usr/bin/env python3
"""Analyze the workflow structure and create a truly connected version"""

import json
import sys
from pathlib import Path

def analyze_workflow_structure():
    """Analyze the current workflow to understand the disconnections."""
    print("🔍 ANALYZING WORKFLOW STRUCTURE")
    print("=" * 40)
    
    try:
        # Load the complete workflow
        workflow_path = Path("kung_fu_video_workflow_complete.json")
        with open(workflow_path, 'r', encoding='utf-8') as f:
            workflow = json.load(f)
        
        print(f"✅ Workflow loaded: {len(workflow['nodes'])} nodes")
        
        # List all nodes
        print(f"\n📋 ALL NODES:")
        for i, node in enumerate(workflow['nodes'], 1):
            print(f"   {i:2d}. {node['name']} ({node['type']})")
        
        # Analyze connections
        print(f"\n🔗 CURRENT CONNECTIONS:")
        connections = workflow.get('connections', {})
        
        connected_nodes = set()
        for source, targets in connections.items():
            connected_nodes.add(source)
            target_list = []
            for target_group in targets.get('main', []):
                for target in target_group:
                    connected_nodes.add(target['node'])
                    target_list.append(target['node'])
            print(f"   {source} → {', '.join(target_list)}")
        
        # Find disconnected nodes
        all_nodes = {node['name'] for node in workflow['nodes']}
        disconnected = all_nodes - connected_nodes
        
        print(f"\n⚠️  DISCONNECTED NODES ({len(disconnected)}):")
        for node in sorted(disconnected):
            print(f"   - {node}")
        
        return workflow, all_nodes, connected_nodes, disconnected
        
    except Exception as e:
        print(f"❌ Error analyzing workflow: {e}")
        return None, None, None, None

def create_minimal_working_workflow():
    """Create a minimal working workflow with just the essential path."""
    print(f"\n🎯 CREATING MINIMAL WORKING WORKFLOW")
    print("=" * 40)
    
    try:
        # Load the complete workflow to get node definitions
        workflow_path = Path("kung_fu_video_workflow_complete.json")
        with open(workflow_path, 'r', encoding='utf-8') as f:
            full_workflow = json.load(f)
        
        # Define the essential workflow path (no logging complexity)
        essential_path = [
            "Manual Trigger",
            "Load Configuration", 
            "Scan Folder for Videos",
            "Process File List",
            "Extract Video Thumbnail",
            "Process Thumbnail",
            "AI Video Analysis",
            "Process AI Response"
        ]
        
        # Extract only essential nodes
        essential_nodes = []
        node_map = {node['name']: node for node in full_workflow['nodes']}
        
        for node_name in essential_path:
            if node_name in node_map:
                essential_nodes.append(node_map[node_name])
            else:
                print(f"⚠️  Missing essential node: {node_name}")
        
        # Create simple linear connections
        simple_connections = {}
        for i in range(len(essential_path) - 1):
            source = essential_path[i]
            target = essential_path[i + 1]
            
            if source in node_map and target in node_map:
                simple_connections[source] = {
                    "main": [[{"node": target, "type": "main", "index": 0}]]
                }
        
        # Create minimal workflow
        minimal_workflow = {
            "name": "Kung Fu Video Detector - Minimal Working",
            "nodes": essential_nodes,
            "pinData": {},
            "connections": simple_connections,
            "active": False,
            "settings": {},
            "versionId": "1"
        }
        
        # Save minimal workflow
        minimal_path = Path("kung_fu_video_workflow_minimal.json")
        with open(minimal_path, 'w', encoding='utf-8') as f:
            json.dump(minimal_workflow, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Minimal workflow created: {minimal_path}")
        print(f"   Essential nodes: {len(essential_nodes)}")
        print(f"   Linear connections: {len(simple_connections)}")
        
        print(f"\n📋 MINIMAL WORKFLOW PATH:")
        for i, node_name in enumerate(essential_path):
            if i == 0:
                print(f"   {node_name} (START)")
            elif i == len(essential_path) - 1:
                print(f"   ↓")
                print(f"   {node_name} (END)")
            else:
                print(f"   ↓")
                print(f"   {node_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating minimal workflow: {e}")
        return False

def create_single_chain_workflow():
    """Create a workflow where every node is in a single chain - no branches."""
    print(f"\n🔗 CREATING SINGLE CHAIN WORKFLOW")
    print("=" * 35)
    
    try:
        # Load the complete workflow
        workflow_path = Path("kung_fu_video_workflow_complete.json")
        with open(workflow_path, 'r', encoding='utf-8') as f:
            workflow = json.load(f)
        
        # Define a single chain that includes most important nodes
        single_chain = [
            "Manual Trigger",
            "Load Configuration",
            "Scan Folder for Videos", 
            "Process File List",
            "Extract Video Thumbnail",
            "Process Thumbnail",
            "AI Video Analysis",
            "Process AI Response",
            "Filter Kung Fu Videos",
            "Collect Final Results"
        ]
        
        # Create single chain connections (no branches)
        chain_connections = {}
        for i in range(len(single_chain) - 1):
            source = single_chain[i]
            target = single_chain[i + 1]
            chain_connections[source] = {
                "main": [[{"node": target, "type": "main", "index": 0}]]
            }
        
        # Update workflow with single chain connections
        workflow['connections'] = chain_connections
        
        # Save single chain workflow
        chain_path = Path("kung_fu_video_workflow_single_chain.json")
        with open(chain_path, 'w', encoding='utf-8') as f:
            json.dump(workflow, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Single chain workflow created: {chain_path}")
        print(f"   Chain length: {len(single_chain)} nodes")
        print(f"   All other nodes will appear disconnected (by design)")
        
        print(f"\n📋 SINGLE CHAIN PATH:")
        for node in single_chain:
            print(f"   → {node}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating single chain workflow: {e}")
        return False

if __name__ == "__main__":
    print("🎯 WORKFLOW STRUCTURE ANALYSIS & REPAIR")
    print("=" * 45)
    
    # Analyze current structure
    workflow, all_nodes, connected_nodes, disconnected = analyze_workflow_structure()
    
    if workflow:
        print(f"\n📊 SUMMARY:")
        print(f"   Total nodes: {len(all_nodes)}")
        print(f"   Connected nodes: {len(connected_nodes)}")
        print(f"   Disconnected nodes: {len(disconnected)}")
        
        # Create two solutions
        success1 = create_minimal_working_workflow()
        success2 = create_single_chain_workflow()
        
        if success1 and success2:
            print(f"\n🎉 TWO SOLUTIONS CREATED:")
            print(f"   1. kung_fu_video_workflow_minimal.json - Essential nodes only")
            print(f"   2. kung_fu_video_workflow_single_chain.json - Single chain, no branches")
            print(f"\n💡 RECOMMENDATION: Try the minimal workflow first!")
        
    sys.exit(0)
