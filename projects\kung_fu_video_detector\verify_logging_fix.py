#!/usr/bin/env python3
import json

print('🔧 Verifying Fixed Logging Workflows')
print('=' * 50)

# Check immediate logging workflow
with open('kung_fu_video_workflow_immediate_logging.json', 'r', encoding='utf-8') as f:
    immediate_workflow = json.load(f)

# Find the write node
write_node = next((node for node in immediate_workflow['nodes'] if node['name'] == 'Write Immediate Scan Log'), None)
if write_node:
    params = write_node['parameters']
    has_data_prop = 'dataPropertyName' in params
    data_prop_value = params.get('dataPropertyName', 'missing')
    print(f'✅ Immediate logging write node:')
    print(f'   dataPropertyName present: {has_data_prop}')
    print(f'   dataPropertyName value: {data_prop_value}')
else:
    print('❌ Write node not found in immediate logging workflow')

# Check main logging workflow  
with open('kung_fu_video_workflow_with_logging.json', 'r', encoding='utf-8') as f:
    main_workflow = json.load(f)

write_node = next((node for node in main_workflow['nodes'] if node['name'] == 'Write Log to Shared Folder'), None)
if write_node:
    params = write_node['parameters']
    has_data_prop = 'dataPropertyName' in params
    data_prop_value = params.get('dataPropertyName', 'missing')
    print(f'✅ Main logging write node:')
    print(f'   dataPropertyName present: {has_data_prop}')
    print(f'   dataPropertyName value: {data_prop_value}')
else:
    print('❌ Write node not found in main logging workflow')

print('\n🎯 Both workflows should now work with writeBinaryFile!')
print('\n📋 Next Steps:')
print('1. Import kung_fu_video_workflow_immediate_logging.json into N8N')
print('2. Run the workflow')
print('3. Check C:\\Docker_Share\\N8N\\immediate_scan_log.json')
print('4. This will show exactly what the scan step produces!')
