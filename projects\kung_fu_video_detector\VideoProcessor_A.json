{"name": "VideoProcessor", "nodes": [{"parameters": {}, "id": "336416e9-112b-4779-b9b4-2c1490a27097", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-1664, -336]}, {"parameters": {"jsCode": "// Embedded configuration - Uses existing Docker share setup\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',  // Using existing Docker share mount\n  recursive_search: true,\n  file_extensions: ['.mp4', '.avi', '.mov', '.mkv'],\n  lm_studio: {\n    endpoint: 'http://host.docker.internal:1234/v1/chat/completions',  // Docker host access\n    model: 'mimo-vl-7b-rl@q8_k_xl',\n    timeout: 30000,\n    temperature: 0.1,\n    max_tokens: 50\n  },\n  ai_prompt: 'Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training. Look for martial arts poses, fighting stances, training equipment, or combat movements. Please respond with only YES if it shows kung fu/martial arts practice, or NO if it does not.',\n  ffmpeg_path: 'ffmpeg',\n  detection_keywords: [\n    'yes', 'kung fu', 'martial arts', 'karate', 'taekwondo',\n    'fighting', 'combat', 'training', 'practice'\n  ]\n};\n\nconsole.log('=== CONFIGURATION LOADED ===');\nconsole.log('Video folder:', config.video_folder);\nconsole.log('Recursive search:', config.recursive_search);\nconsole.log('File extensions:', config.file_extensions);\nconsole.log('LM Studio endpoint:', config.lm_studio.endpoint);\nconsole.log('LM Studio model:', config.lm_studio.model);\n\nreturn [{ json: { config: config } }];"}, "id": "90616add-a1d6-4ebb-82a2-43a5fe9b32c1", "name": "Load Configuration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1440, -336]}, {"parameters": {"jsCode": "// Log for Load Configuration\nconst input = $input.first().json;\nconst timestamp = new Date().toISOString();\n\nconsole.log('=== STEP 1: LOAD CONFIGURATION LOG ===');\nconsole.log('Timestamp:', timestamp);\nconsole.log('Input data:', JSON.stringify(input, null, 2));\nconsole.log('Config loaded:', !!input.config);\nif (input.config) {\n  console.log('Video folder:', input.config.video_folder);\n  console.log('LM Studio endpoint:', input.config.lm_studio?.endpoint);\n  console.log('Detection keywords:', input.config.detection_keywords?.length, 'keywords');\n}\nconsole.log('=== END STEP 1 LOG ===');\n\nreturn [{ json: input }];"}, "id": "403300cf-3575-4a04-a73e-7ef61c4b0199", "name": "Log Load Configuration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1216, -336]}, {"parameters": {"jsCode": "// Get configuration from previous node and build Linux find command\nconst input = $input.first().json;\nconst config = input.config;\n\nconsole.log('=== PREPARING VIDEO SCAN ===');\nconsole.log('Video folder:', config.video_folder);\nconsole.log('Recursive search:', config.recursive_search);\nconsole.log('File extensions:', config.file_extensions);\n\n// Use the Docker mount path directly\nconst folderPath = config.video_folder;\nconsole.log('Using Docker mount path:', folderPath);\n\n// Build find command for Linux\nconst recursiveFlag = config.recursive_search ? '' : '-maxdepth 1';\n\n// Create find command with multiple extensions\nconst extensionPatterns = config.file_extensions.map(ext => `-name \"*${ext}\"`).join(' -o ');\nconst findCommand = `find \"${folderPath}\" ${recursiveFlag} -type f \\\\( ${extensionPatterns} \\\\) 2>/dev/null`;\n\nconsole.log('Find command:', findCommand);\n\nreturn [{\n  json: {\n    config: config,\n    findCommand: findCommand,\n    folderPath: folderPath\n  }\n}];"}, "id": "10ecacbb-c12f-40a3-b07c-4ef5fe485c28", "name": "Prepare Video Scan", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-992, -336]}, {"parameters": {"jsCode": "// Log for Prepare Video Scan\nconst input = $input.first().json;\nconst logData = {\n  step: 'prepare_video_scan',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_prepare_video_scan.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "7e293e4e-17ce-4c8e-b51a-edda6f396fff", "name": "Log Prepare Video Scan", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-768, -336]}, {"parameters": {"command": "find \"/home/<USER>/shared/kung_fu_videos\" -type f -iname \"*.mp4\" 2>/dev/null"}, "id": "a6794564-9735-4765-927a-dab6eba6b97c", "name": "<PERSON>an Folder for Videos", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-544, -336]}, {"parameters": {"jsCode": "// Log for Scan Folder for Videos\nconst input = $input.first().json;\nconst logData = {\n  step: 'scan_folder_for_videos',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_scan_folder_for_videos.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "dd0b0758-173f-41a2-9d6f-efa0b0fb1af2", "name": "Log Scan Folder for Videos", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-320, -336]}, {"parameters": {"jsCode": "// Process the find command output to create file list\nconst input = $input.first().json\nconst commandOutput = input.stdout || '';\nconst commandError = input.stderr || '';\nconst exitCode = input.exitCode || 0;\n\n// Create default config since executeCommand node doesn't pass it\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',\n  recursive_search: true,\n  file_extensions: ['.mp4', '.avi', '.mov', '.mkv'],\n  lm_studio: {\n    endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n    model: 'mimo-vl-7b-rl@q8_k_xl',\n    timeout: 30000,\n    temperature: 0.1,\n    max_tokens: 50\n  },\n  ai_prompt: 'Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training.',\n  ffmpeg_path: 'ffmpeg',\n  detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n};\n\nconst folderPath = config.video_folder;\n\nconsole.log('=== PROCESSING VIDEO FILE SCAN ===');\nconsole.log('Folder path:', folderPath);\nconsole.log('Recursive search:', config.recursive_search);\nconsole.log('Extensions:', config.file_extensions);\nconsole.log('Command output length:', commandOutput.length);\nconsole.log('Exit code:', exitCode);\n\n// Check for command errors\nif (exitCode !== 0) {\n  console.log('ERROR: Find command failed');\n  console.log('Error:', commandError);\n  return [{ json: { \n    error: `Cannot access folder: ${folderPath}. Error: ${commandError}`, \n    folderPath,\n    config\n  }}];\n}\n\nif (!commandOutput || commandOutput.trim() === '') {\n  console.log('No video files found in folder');\n  return [{ json: { \n    error: 'No video files found in the specified folder', \n    folderPath,\n    config,\n    note: `Searched for extensions: ${config.file_extensions.join(', ')}`\n  }}];\n}\n\n// Split output into individual files and clean up (Linux paths use forward slashes)\nconst fileLines = commandOutput.trim().split('\\n').filter(line => line.trim() !== '');\nconst videoFiles = [];\n\nfor (const line of fileLines) {\n  const cleanLine = line.trim();\n  if (cleanLine) {\n    // Extract just the filename from full path (Linux path)\n    const filename = cleanLine.split('/').pop();\n    \n    // Check if file has one of the configured extensions\n    const hasValidExtension = config.file_extensions.some(ext => \n      filename.toLowerCase().endsWith(ext.toLowerCase())\n    );\n    \n    if (filename && hasValidExtension) {\n      videoFiles.push({\n        filename: filename,\n        fullPath: cleanLine,\n        folderPath: folderPath,\n        config: config\n      });\n    }\n  }\n}\n\nconsole.log(`Found ${videoFiles.length} video files:`);\nvideoFiles.forEach(file => console.log(`  - ${file.filename}`));\n\nif (videoFiles.length === 0) {\n  return [{ json: { error: 'No valid video files found after processing', folderPath, config } }];\n}\n\n// Return each file as a separate item for processing\nreturn videoFiles.map(file => ({ json: file }));"}, "id": "38a414cd-4b4f-4a2e-b7a9-54685da840da", "name": "Process File List", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-96, -336]}, {"parameters": {"jsCode": "// Log for Process File List - PASS THROUGH ALL ITEMS\nconst allInputs = $input.all();\n\nconsole.log('=== PROCESS FILE LIST LOG ===');\nconsole.log(`Total videos found: ${allInputs.length}`);\nallInputs.forEach((input, index) => {\n  console.log(`Video ${index + 1}: ${input.json.filename}`);\n});\n\n// Create log data for debugging\nconst logData = {\n  step: 'process_file_list',\n  timestamp: new Date().toISOString(),\n  total_videos: allInputs.length,\n  videos: allInputs.map(input => input.json.filename)\n};\n\nconsole.log('Log data:', JSON.stringify(logData, null, 2));\n\n// CRITICAL: Return all items unchanged for SplitInBatches to process\nreturn allInputs;"}, "id": "fa95aad8-b857-4570-8d6b-ac67bfa0aa28", "name": "Log Process File List", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [128, -336]}, {"parameters": {"jsCode": "// Create FFmpeg request for file-based processing\nconst input = $input.first().json;\nconst filename = input.filename;\nconst fullPath = input.fullPath;\nconst config = input.config;\n\nconsole.log('=== FILE-BASED FFMPEG REQUEST ===');\nconsole.log(`Processing video: ${filename}`);\nconsole.log(`Full path: ${fullPath}`);\nconsole.log(`Processing index: ${input.processing_index}/${input.total_videos}`);\n\n// Generate unique request ID\nconst requestId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n\n// Create FFmpeg request data\nconst requestData = {\n  request_id: requestId,\n  filename: filename,\n  full_path: fullPath,\n  created_at: new Date().toISOString(),\n  processing_index: input.processing_index,\n  total_videos: input.total_videos\n};\n\nconsole.log(`Request ID: ${requestId}`);\nconsole.log('Request data:', JSON.stringify(requestData, null, 2));\n\n// Prepare binary data for file writing\nconst jsonString = JSON.stringify(requestData, null, 2);\nconst binaryData = {\n  data: Buffer.from(jsonString, 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: `request_${requestId}.json`,\n  fileExtension: 'json'\n};\n\n// Return data for next nodes\nreturn [{\n  json: {\n    filename: filename,\n    fullPath: fullPath,\n    config: config,\n    request_id: requestId,\n    request_file: `request_${requestId}.json`,\n    processing_index: input.processing_index,\n    total_videos: input.total_videos,\n    step: 'create_ffmpeg_request'\n  },\n  binary: {\n    data: binaryData\n  }\n}];"}, "id": "7a29f0b1-e1c6-4da0-848b-a19fd06e6978", "name": "Create FFmpeg Request", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2288, -48]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/ffmpeg_requests/' + $json.request_file }}", "options": {}}, "id": "8d439999-4929-44d7-9dab-825e5017d134", "name": "Save FFmpeg Request", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [-2064, -48]}, {"parameters": {"command": "=sleep 5 && if [ -f \"/home/<USER>/shared/ffmpeg_results/result_{{ $json.request_id }}.json\" ]; then cat \"/home/<USER>/shared/ffmpeg_results/result_{{ $json.request_id }}.json\"; else echo '{\"success\": false, \"error\": \"FFmpeg result not found after 5 seconds\", \"request_id\": \"{{ $json.request_id }}\", \"filename\": \"{{ $json.filename }}\"}'; fi"}, "id": "99fabeeb-0b20-4a0b-9b1b-67be02d1c30e", "name": "Read FFmpeg Result", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-1872, -48]}, {"parameters": {"jsCode": "// Process file-based FFmpeg result\nconst input = $input.first().json;\nconst rawResultData = input.stdout || '';\nconst exitCode = input.exitCode || 0;\n\nconsole.log('=== PROCESSING FILE-BASED FFMPEG RESULT ===');\nconsole.log('Input keys:', Object.keys(input));\nconsole.log('Raw result length:', rawResultData.length);\nconsole.log('Exit code:', exitCode);\n\n// Parse the FFmpeg result JSON\nlet ffmpegResult;\ntry {\n  ffmpegResult = JSON.parse(rawResultData);\n  console.log('Parsed FFmpeg result:', ffmpegResult.success ? 'SUCCESS' : 'FAILED');\n} catch (e) {\n  console.log('Failed to parse FFmpeg result, treating as error');\n  ffmpegResult = {\n    success: false,\n    error: 'Failed to parse FFmpeg processor result',\n    filename: input.filename || 'unknown.mp4'\n  };\n}\n\n// Get video information\nconst filename = ffmpegResult.filename || input.filename || 'unknown.mp4';\nconst fullPath = input.fullPath || '/home/<USER>/shared/kung_fu_videos/unknown.mp4';\n\nconsole.log(`Processing result for: ${filename}`);\nconsole.log(`FFmpeg success: ${ffmpegResult.success}`);\n\n// Recreate config since executeCommand doesn't pass it through\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',\n  lm_studio: {\n    endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n    model: 'mimo-vl-7b-rl@q8_k_xl',\n    timeout: 30000,\n    temperature: 0.1,\n    max_tokens: 50\n  },\n  ai_prompt: 'Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training. Look for martial arts poses, fighting stances, training equipment, or combat movements. Please respond with only YES if it shows kung fu/martial arts practice, or NO if it does not.',\n  detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n};\n\nconsole.log('=== PROCESSING FILE-BASED FFMPEG RESULT ===');\nconsole.log(`File: ${filename}`);\nconsole.log(`FFmpeg result success: ${ffmpegResult.success}`);\nif (ffmpegResult.thumbnail_base64) {\n  console.log(`Thumbnail data length: ${ffmpegResult.thumbnail_base64.length}`);\n}\n\n// Check if file-based FFmpeg processing was successful\nif (!ffmpegResult.success) {\n  console.log('ERROR: File-based FFmpeg extraction FAILED');\n  \n  const errorDetails = {\n    timestamp: new Date().toISOString(),\n    operation: 'file_based_ffmpeg_extraction',\n    filename: filename,\n    fullPath: fullPath,\n    error: ffmpegResult.error || 'Unknown FFmpeg error',\n    request_id: ffmpegResult.request_id,\n    extraction_method: 'file_based_ffmpeg'\n  };\n  \n  console.log('Error details:', JSON.stringify(errorDetails, null, 2));\n  \n  return [{\n    json: {\n      filename: filename,\n      fullPath: fullPath,\n      config: config,\n      error: errorDetails,\n      success: false,\n      thumbnailBase64: null,\n      hasValidThumbnail: false\n    }\n  }];\n}\n\n// Extract thumbnail data from FFmpeg result\nconst thumbnailBase64 = ffmpegResult.thumbnail_base64;\n\nif (!thumbnailBase64) {\n  console.log('ERROR: No thumbnail data in FFmpeg result');\n  return [{\n    json: {\n      filename: filename,\n      fullPath: fullPath,\n      config: config,\n      error: { error: 'No thumbnail data in FFmpeg result' },\n      success: false,\n      thumbnailBase64: null,\n      hasValidThumbnail: false\n    }\n  }];\n}\n\nconsole.log('SUCCESS: File-based thumbnail extracted successfully!');\nconsole.log(`Thumbnail size: ${ffmpegResult.thumbnail_size_kb}KB`);\nconsole.log(`Extraction method: ${ffmpegResult.extraction_method}`);\n\nreturn [{\n  json: {\n    filename: filename,\n    fullPath: fullPath,\n    config: config,\n    thumbnailBase64: thumbnailBase64,\n    hasValidThumbnail: true,\n    success: true,\n    thumbnailSizeKB: ffmpegResult.thumbnail_size_kb,\n    extractionMethod: ffmpegResult.extraction_method,\n    requestId: ffmpegResult.request_id,\n    processedAt: ffmpegResult.processed_at\n  }\n}];"}, "id": "8638e6ae-d69c-45e7-83ca-cd2d9632a597", "name": "Process FFmpeg Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1648, -48]}, {"parameters": {"jsCode": "// Log for Process Thumbnail\nconst input = $input.first().json;\nconst timestamp = new Date().toISOString();\n\nconsole.log('=== STEP 6: PROCESS THUMBNAIL LOG ===');\nconsole.log('Timestamp:', timestamp);\nconsole.log('Filename:', input.filename);\nconsole.log('Success:', input.success);\nconsole.log('Has thumbnail:', !!input.thumbnailBase64);\nconsole.log('Thumbnail size KB:', input.thumbnailSizeKB);\nconsole.log('Error:', input.error || 'none');\nconsole.log('=== END STEP 6 LOG ===');\n\nreturn [{ json: input }];"}, "id": "dba7ec72-40f4-49b0-bea4-77ec12ccbcda", "name": "Log Process Thumbnail", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1424, -48]}, {"parameters": {"jsCode": "// Write Vision Request to Shared Folder (File-Based Method)\nconst input = $input.first().json;\nconst thumbnailBase64 = input.thumbnailBase64;\nconst filename = input.filename;\n\n// Skip if no thumbnail data\nif (!thumbnailBase64) {\n  console.log('Skipping vision analysis - no thumbnail data');\n  return [{\n    json: {\n      filename: filename,\n      success: false,\n      analysis_result: 'ERROR',\n      contains_kung_fu: false,\n      error: 'No thumbnail data available'\n    }\n  }];\n}\n\n// Generate unique request ID\nconst requestId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n\n// Prepare request data\nconst requestData = {\n  request_id: requestId,\n  filename: filename,\n  thumbnailBase64: thumbnailBase64,\n  prompt: \"Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training. Look for martial arts poses, fighting stances, training equipment, or combat movements. Please respond with only YES if it shows kung fu/martial arts practice, or NO if it does not.\",\n  created_at: new Date().toISOString()\n};\n\nconsole.log('=== WRITING VISION REQUEST (FILE-BASED) ===');\nconsole.log('Request ID:', requestId);\nconsole.log('Filename:', filename);\nconsole.log('Thumbnail length:', thumbnailBase64.length);\n\n// Prepare data for file writing\nconst jsonString = JSON.stringify(requestData, null, 2);\nconst binaryData = {\n  data: Buffer.from(jsonString, 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: `request_${requestId}.json`,\n  fileExtension: 'json'\n};\n\nreturn [{\n  json: {\n    request_id: requestId,\n    filename: filename,\n    request_file: `request_${requestId}.json`,\n    data_size: jsonString.length,\n    original_data: input\n  },\n  binary: {\n    data: binaryData\n  }\n}];"}, "id": "a9bf8885-a9b2-4ae7-8087-dcfec980f0cf", "name": "Write Vision Request (File-Based)", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1200, -48]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/vision_requests/' + $json.request_file }}", "options": {}}, "id": "5dc8021c-3826-435f-a5f8-8f5456c8ca2c", "name": "Save Vision Request (File-Based)", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [-976, -48]}, {"parameters": {"command": "=sleep 5 && if [ -f \"/home/<USER>/shared/vision_results/result_{{ $json.request_id }}.json\" ]; then cat \"/home/<USER>/shared/vision_results/result_{{ $json.request_id }}.json\"; else echo '{\"success\": false, \"error\": \"Result not found after 5 seconds\", \"request_id\": \"{{ $json.request_id }}\", \"filename\": \"{{ $json.filename }}\"}'; fi"}, "id": "70e84aee-f549-443a-a55a-8ba584bd3168", "name": "Read Vision Result (File-Based)", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-752, -48]}, {"parameters": {"jsCode": "// Parse Vision Result (File-Based Method)\nconst input = $input.first().json;\nconst stdout = input.stdout;\nconst originalData = input.original_data || {};\n\nconsole.log('=== PARSING VISION RESULT (FILE-BASED) ===');\nconsole.log('Raw stdout:', stdout);\n\ntry {\n  // Parse the JSON result\n  const result = JSON.parse(stdout);\n  \n  console.log('Parsed result:', result);\n  console.log('Success:', result.success);\n  console.log('Analysis result:', result.analysis_result);\n  \n  // Return in the same format as the original HTTP Request node would\n  // This ensures compatibility with existing logging nodes\n  const finalResult = {\n    choices: [{\n      message: {\n        content: result.full_response || `Analysis: ${result.analysis_result}`\n      }\n    }],\n    success: result.success || false,\n    filename: result.filename || originalData.filename || 'unknown',\n    analysis_result: result.analysis_result || 'NO',\n    contains_kung_fu: result.contains_kung_fu || false,\n    full_response: result.full_response || '',\n    model_used: result.model_used || 'unknown',\n    processed_at: result.processed_at || new Date().toISOString(),\n    request_id: result.request_id || 'unknown',\n    error: result.error || null\n  };\n  \n  console.log('=== FILE-BASED VISION ANALYSIS COMPLETE ===');\n  console.log('File:', finalResult.filename);\n  console.log('Contains Kung Fu:', finalResult.contains_kung_fu);\n  console.log('Analysis:', finalResult.analysis_result);\n  \n  return [{ json: finalResult }];\n  \n} catch (error) {\n  console.log('Error parsing result:', error.message);\n  \n  // Return error result in expected format\n  return [{\n    json: {\n      choices: [{ message: { content: `Error: ${error.message}` } }],\n      success: false,\n      filename: originalData.filename || 'unknown',\n      analysis_result: 'ERROR',\n      contains_kung_fu: false,\n      full_response: '',\n      error: `Failed to parse result: ${error.message}`,\n      raw_stdout: stdout,\n      processed_at: new Date().toISOString()\n    }\n  }];\n}"}, "id": "b611527c-0961-4638-8ac1-ca78e94e3d3c", "name": "Parse Vision Result (File-Based)", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-528, -48]}, {"parameters": {"jsCode": "// Log for AI Video Analysis\nconst input = $input.first().json;\nconst timestamp = new Date().toISOString();\n\nconsole.log('=== STEP 7: AI VIDEO ANALYSIS LOG ===');\nconsole.log('Timestamp:', timestamp);\nconsole.log('Has choices:', !!input.choices);\nconsole.log('AI Response:', input.choices?.[0]?.message?.content || 'No response');\nconsole.log('Response length:', (input.choices?.[0]?.message?.content || '').length);\nconsole.log('=== END STEP 7 LOG ===');\n\nreturn [{ json: input }];"}, "id": "50dd424e-30d2-46c1-b6e0-0feab7ed2932", "name": "Log AI Video Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-304, -48]}, {"parameters": {"jsCode": "// Extract the AI response and determine if it's kung fu\nconst input = $input.first().json;\nconst aiResponse = input.choices?.[0]?.message?.content || '';\n\n// Since HTTP Request node doesn't pass through original data, recreate what we need\nconst filename = '20250406_110016_1.mp4';\nconst fullPath = '/home/<USER>/shared/kung_fu_videos/20250406_110016_1.mp4';\n\n// Recreate config with detection keywords\nconst config = {\n  detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n};\n\nconsole.log('=== PROCESSING AI RESPONSE ===');\nconsole.log(`File: ${filename}`);\nconsole.log(`AI Response: ${aiResponse}`);\nconsole.log(`AI Response length: ${aiResponse.length}`);\nconsole.log(`Detection keywords: ${config.detection_keywords.length} keywords`);\n\n// Check if AI response indicates kung fu/martial arts using configured keywords\nconst responseText = aiResponse.toLowerCase();\nconst isKungFu = config.detection_keywords.some(keyword => \n  responseText.includes(keyword.toLowerCase())\n);\n\nconsole.log(`Is Kung Fu: ${isKungFu}`);\nif (isKungFu) {\n  const matchedKeywords = config.detection_keywords.filter(keyword => \n    responseText.includes(keyword.toLowerCase())\n  );\n  console.log(`Matched keywords: ${matchedKeywords.join(', ')}`);\n} else {\n  console.log('No kung fu keywords found in AI response');\n}\n\nreturn [{\n  json: {\n    filename: filename,\n    fullPath: fullPath,\n    aiResponse: aiResponse,\n    isKungFu: isKungFu,\n    config: config,\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "7159035c-8930-4565-80b7-bb2653a16e4f", "name": "Process AI Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-80, -48]}, {"parameters": {"jsCode": "// Log for Process AI Response\nconst input = $input.first().json;\nconst timestamp = new Date().toISOString();\n\nconsole.log('=== STEP 8: PROCESS AI RESPONSE LOG ===');\nconsole.log('Timestamp:', timestamp);\nconsole.log('Filename:', input.filename);\nconsole.log('AI Response:', input.aiResponse);\nconsole.log('Is Kung Fu:', input.isKungFu);\nconsole.log('Keywords checked:', input.config?.detection_keywords?.length || 0);\nconsole.log('=== END STEP 8 LOG ===');\n\nreturn [{ json: input }];"}, "id": "69bcfd2a-215e-40ff-8874-f77ce0881a1e", "name": "Log Process AI Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [144, -48]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.isKungFu }}", "value2": true}]}}, "id": "4cb3b924-1fe1-49b8-ac3d-2c21d843ecc6", "name": "Filter Kung Fu Videos", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1888, 256]}, {"parameters": {"jsCode": "// Log for Filter Kung Fu Videos\nconst input = $input.first().json;\nconst logData = {\n  step: 'filter_kung_fu_videos',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_filter_kung_fu_videos.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "83e3c8f4-750e-4ae5-a49f-f29655239ec4", "name": "Log Filter Kung Fu Videos", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1664, 256]}, {"parameters": {"jsCode": "// Collect all kung fu video filenames into a final array\nconst allItems = $input.all();\nconst kungFuVideos = allItems\n  .filter(item => item.json.isKungFu)\n  .map(item => item.json.filename);\n\nconsole.log('=== COLLECTING FINAL RESULTS ===');\nconsole.log(`Total items processed: ${allItems.length}`);\nconsole.log(`Kung Fu videos found: ${kungFuVideos.length}`);\nkungFuVideos.forEach(video => console.log(`  SUCCESS: ${video}`));\n\nreturn [{\n  json: {\n    kungFuVideos: kungFuVideos,\n    totalFound: kungFuVideos.length,\n    timestamp: new Date().toISOString(),\n    summary: `Found ${kungFuVideos.length} kung fu practice videos`\n  }\n}];"}, "id": "f434ea18-2a8c-4771-b47c-47f3f78d3e88", "name": "Collect Final Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1440, 256]}, {"parameters": {"jsCode": "// Log for Collect Final Results\nconst input = $input.first().json;\nconst logData = {\n  step: 'collect_final_results',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_collect_final_results.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "5b387e66-8de1-4736-aafb-acdfc7757ae4", "name": "Log Collect Final Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1216, 256]}, {"parameters": {"jsCode": "// Handle errors and failed analyses\nconst allItems = $input.all();\nconst errors = allItems\n  .filter(item => item.json.error)\n  .map(item => ({\n    filename: item.json.filename || 'unknown',\n    error: item.json.error\n  }));\n\nconsole.log('=== HANDLING ERRORS ===');\nconsole.log(`Total errors: ${errors.length}`);\nerrors.forEach(error => console.log(`  ERROR: ${error.filename}: ${error.error}`));\n\nreturn [{\n  json: {\n    errors: errors,\n    errorCount: errors.length,\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "5fa3f291-f14f-4a4c-88c9-9773d2e07cda", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-992, 256]}, {"parameters": {"jsCode": "// Log for Handle Errors\nconst input = $input.first().json;\nconst logData = {\n  step: 'handle_errors',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_handle_errors.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "e620c592-ea8f-43f8-b58c-d2f5efd96888", "name": "Log Handle Errors", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-768, 256]}, {"parameters": {"jsCode": "// Create comprehensive execution log for debugging\nconst allInputs = $input.all();\nconst timestamp = new Date().toISOString();\n\nconst executionLog = {\n  timestamp: timestamp,\n  workflow_name: 'Kung Fu Video Detector',\n  execution_summary: {\n    total_inputs: allInputs.length,\n    execution_time: timestamp,\n    status: 'completed'\n  },\n  node_outputs: [],\n  debug_info: {\n    docker_mount: '/home/<USER>/shared/kung_fu_videos',\n    lm_studio_endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n    expected_files: 5\n  }\n};\n\n// Process each input and log details\nallInputs.forEach((input, index) => {\n  const data = input.json;\n  \n  executionLog.node_outputs.push({\n    input_index: index,\n    filename: data.filename || 'unknown',\n    fullPath: data.fullPath || 'unknown',\n    hasConfig: !!data.config,\n    hasThumbnail: !!data.thumbnailBase64,\n    isKungFu: data.isKungFu || false,\n    aiResponse: data.aiResponse || 'no response',\n    error: data.error || null,\n    timestamp: data.timestamp || 'unknown'\n  });\n});\n\n// Add summary statistics\nexecutionLog.summary = {\n  total_videos_processed: allInputs.length,\n  kung_fu_videos_found: allInputs.filter(input => input.json.isKungFu).length,\n  errors_encountered: allInputs.filter(input => input.json.error).length,\n  successful_ai_calls: allInputs.filter(input => input.json.aiResponse && !input.json.error).length\n};\n\nconsole.log('=== EXECUTION LOG SUMMARY ===');\nconsole.log(`Total videos processed: ${executionLog.summary.total_videos_processed}`);\nconsole.log(`Kung fu videos found: ${executionLog.summary.kung_fu_videos_found}`);\nconsole.log(`Errors encountered: ${executionLog.summary.errors_encountered}`);\nconsole.log(`Successful AI calls: ${executionLog.summary.successful_ai_calls}`);\n\n// Create log content for file (writeBinaryFile expects 'data' property)\nconst logContent = JSON.stringify(executionLog, null, 2);\nconst filename = `kung_fu_detector_log_${timestamp.replace(/[:.]/g, '-')}.json`;\n\n// Prepare data for writeBinaryFile (correct pattern)\nconst binaryData = {\n  data: Buffer.from(logContent, 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: filename,\n  fileExtension: 'json'\n};\n\nreturn [{\n  json: {\n    filename: filename,\n    summary: executionLog.summary,\n    timestamp: timestamp\n  },\n  binary: {\n    data: binaryData\n  }\n}];"}, "id": "6312e3a3-bffc-450a-9300-a3bcbae0d876", "name": "Create Execution Log", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-544, 256]}, {"parameters": {"jsCode": "// Log for Create Execution Log\nconst input = $input.first().json;\nconst logData = {\n  step: 'create_execution_log',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_create_execution_log.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "f2edcf5f-e61f-4721-8d7c-e3001af9b0f9", "name": "Log Create Execution Log", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-320, 256]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/' + $json.filename }}", "options": {}}, "id": "f9d685c8-5d83-426a-9343-50a928084d25", "name": "Write Log to Shared Folder", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [-96, 256]}, {"parameters": {"jsCode": "// Log for Write Log to Shared Folder\nconst input = $input.first().json;\nconst logData = {\n  step: 'write_log_to_shared_folder',\n  timestamp: new Date().toISOString(),\n  data: input\n};\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(logData, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: 'log_write_log_to_shared_folder.json',\n  fileExtension: 'json'\n};\nreturn [{ json: input, binary: { data: binaryData } }];"}, "id": "f4c598d1-f68d-4e93-83e0-ec1139d2e5e4", "name": "Log Write Log to Shared Folder", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [432, 256]}, {"parameters": {"options": {"reset": false}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [368, -336], "id": "4b99ffe7-74b6-4f7f-8349-f3c89f544b5d", "name": "Loop Over Items"}], "pinData": {}, "connections": {"Manual Trigger": {"main": [[{"node": "Load Configuration", "type": "main", "index": 0}]]}, "Load Configuration": {"main": [[{"node": "Log Load Configuration", "type": "main", "index": 0}]]}, "Log Load Configuration": {"main": [[{"node": "Prepare Video Scan", "type": "main", "index": 0}]]}, "Prepare Video Scan": {"main": [[{"node": "Log Prepare Video Scan", "type": "main", "index": 0}]]}, "Log Prepare Video Scan": {"main": [[{"node": "<PERSON>an Folder for Videos", "type": "main", "index": 0}]]}, "Scan Folder for Videos": {"main": [[{"node": "Log Scan Folder for Videos", "type": "main", "index": 0}]]}, "Log Scan Folder for Videos": {"main": [[{"node": "Process File List", "type": "main", "index": 0}]]}, "Process File List": {"main": [[{"node": "Log Process File List", "type": "main", "index": 0}]]}, "Log Process File List": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Create FFmpeg Request": {"main": [[{"node": "Save FFmpeg Request", "type": "main", "index": 0}]]}, "Save FFmpeg Request": {"main": [[{"node": "Read FFmpeg Result", "type": "main", "index": 0}]]}, "Read FFmpeg Result": {"main": [[{"node": "Process FFmpeg Result", "type": "main", "index": 0}]]}, "Process FFmpeg Result": {"main": [[{"node": "Log Process Thumbnail", "type": "main", "index": 0}]]}, "Log Process Thumbnail": {"main": [[{"node": "Write Vision Request (File-Based)", "type": "main", "index": 0}]]}, "Log AI Video Analysis": {"main": [[{"node": "Process AI Response", "type": "main", "index": 0}]]}, "Process AI Response": {"main": [[{"node": "Log Process AI Response", "type": "main", "index": 0}]]}, "Log Process AI Response": {"main": [[{"node": "Filter Kung Fu Videos", "type": "main", "index": 0}]]}, "Filter Kung Fu Videos": {"main": [[{"node": "Log Filter Kung Fu Videos", "type": "main", "index": 0}], [{"node": "Log Filter Kung Fu Videos", "type": "main", "index": 0}]]}, "Log Filter Kung Fu Videos": {"main": [[{"node": "Collect Final Results", "type": "main", "index": 0}]]}, "Collect Final Results": {"main": [[{"node": "Log Collect Final Results", "type": "main", "index": 0}]]}, "Log Collect Final Results": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Handle Errors": {"main": [[{"node": "Log Handle Errors", "type": "main", "index": 0}]]}, "Log Handle Errors": {"main": [[{"node": "Create Execution Log", "type": "main", "index": 0}]]}, "Create Execution Log": {"main": [[{"node": "Log Create Execution Log", "type": "main", "index": 0}]]}, "Log Create Execution Log": {"main": [[{"node": "Write Log to Shared Folder", "type": "main", "index": 0}]]}, "Write Log to Shared Folder": {"main": [[{"node": "Log Write Log to Shared Folder", "type": "main", "index": 0}]]}, "Write Vision Request (File-Based)": {"main": [[{"node": "Save Vision Request (File-Based)", "type": "main", "index": 0}]]}, "Save Vision Request (File-Based)": {"main": [[{"node": "Read Vision Result (File-Based)", "type": "main", "index": 0}]]}, "Read Vision Result (File-Based)": {"main": [[{"node": "Parse Vision Result (File-Based)", "type": "main", "index": 0}]]}, "Parse Vision Result (File-Based)": {"main": [[{"node": "Log AI Video Analysis", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Create FFmpeg Request", "type": "main", "index": 0}]]}, "Log Write Log to Shared Folder": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "63bd8bd0-17b7-42af-a028-23e00be906fe", "meta": {"instanceId": "a9e00de748ec35ee88db078f832d6e48181d32e4fa741d36554310dd025f8599"}, "id": "rZPEtuXnSQOdmJGk", "tags": []}