{"version": "1.0", "environment": "development", "description": "Configuration for N8N Video Processor with Date-Based Notes Generation", "last_updated": "2025-09-11", "folders": {"source": {"videos": "/home/<USER>/shared/kung_fu_videos", "config": "/home/<USER>/shared/config"}, "output": {"notes": "/home/<USER>/shared/notes", "logs": "/home/<USER>/shared/logs"}, "target": {"kung_fu": "/home/<USER>/shared/target/kung_fu", "other": "/home/<USER>/shared/target/other"}, "processing": {"ffmpeg_requests": "/home/<USER>/shared/ffmpeg_requests", "ffmpeg_results": "/home/<USER>/shared/ffmpeg_results", "vision_requests": "/home/<USER>/shared/vision_requests", "vision_results": "/home/<USER>/shared/vision_results", "description_requests": "/home/<USER>/shared/description_requests", "description_results": "/home/<USER>/shared/description_results"}}, "processing": {"file_extensions": [".mp4"], "recursive_search": true, "skip_errors": true, "date_extraction": {"filename_pattern": "^20\\d{6}", "filename_date_length": 8, "fallback_to_modification_date": true}, "notes_generation": {"filename_format": "YYYYMMDD_Notes.txt", "entry_format": "{filename} - {description}", "separate_kung_fu_and_other": false}}, "ai_analysis": {"lm_studio": {"endpoint": "http://host.docker.internal:1234/v1/chat/completions", "model": "mimo-vl-7b-rl@q8_k_xl", "timeout": 30000, "temperature": 0.4, "max_tokens": 150}, "detection_keywords": ["yes", "kung fu", "martial arts", "karate", "taekwondo", "fighting", "combat", "training", "practice"], "prompts": {"kung_fu_detection": "Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training. Look for martial arts poses, fighting stances, training equipment, or combat movements. Please respond with only YES if it shows kung fu/martial arts practice, or NO if it does not.", "kung_fu_description": "Analyze this video thumbnail from a kung fu training video. Look for text blocks that describe the technique being demonstrated (usually visible by the 10th frame). Extract and return the technique name like 'Dragon Walking Sword' or 'Lightning Kick - Hsing-I'. If you can see martial arts content but no text, describe the technique you observe.", "other_description": "This video does not contain kung fu content. Provide a brief 3-10 word description of what you see in the thumbnail, such as 'Field of grass with turtle' or 'Person walking in park'."}}, "logging": {"enabled": true, "level": "INFO", "include_skipped_files": true, "execution_log_format": "video_processor_log_{timestamp}.json", "per_node_logging": true}, "deployment": {"notes": ["For live deployment, update the 'environment' field to 'production'", "Update folder paths in 'folders.source.videos' and 'folders.output' sections", "Modify 'folders.target' paths for kung_fu and other video destinations", "Adjust AI model settings in 'ai_analysis.lm_studio' if needed", "Review and update prompts in 'ai_analysis.prompts' for production use"]}}