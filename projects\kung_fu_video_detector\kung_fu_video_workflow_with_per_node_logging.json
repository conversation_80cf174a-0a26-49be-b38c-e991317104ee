{"name": "Kung Fu Video Detector - <PERSON> Lo<PERSON>", "nodes": [{"parameters": {}, "id": "ed85e2e5-dc5e-4575-88eb-afd38180a7c8", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"jsCode": "// Load configuration and create Step 1 log\nconst config = {\n  video_folder: '/home/<USER>/shared/kung_fu_videos',\n  file_extensions: ['.mp4', '.avi', '.mov', '.mkv'],\n  lm_studio: {\n    endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n    model: 'mimo-vl-7b-rl@q8_k_xl',\n    timeout: 30000,\n    temperature: 0.1,\n    max_tokens: 50\n  },\n  ai_prompt: 'Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training.',\n  detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n};\n\nconst stepLog = {\n  step: '1_load_configuration',\n  timestamp: new Date().toISOString(),\n  config_loaded: true,\n  video_folder: config.video_folder,\n  lm_studio_endpoint: config.lm_studio.endpoint,\n  detection_keywords_count: config.detection_keywords.length,\n  status: 'completed'\n};\n\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(stepLog, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: '1_config_log.json',\n  fileExtension: 'json'\n};\n\nconsole.log('=== STEP 1: CONFIGURATION LOADED ===');\nreturn [{ json: { config: config, log_filename: '1_config_log.json' }, binary: { data: binaryData } }];"}, "id": "load-config-with-log", "name": "Load Config + Log", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [500, 300]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/' + $json.log_filename }}", "options": {"overwrite": true}}, "id": "write-step-1-log", "name": "Write Step 1 Log", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [750, 300]}, {"parameters": {"command": "find \"/home/<USER>/shared/kung_fu_videos\" -type f \\( -iname \"*.mp4\" -o -iname \"*.avi\" -o -iname \"*.mov\" -o -iname \"*.mkv\" \\) | head -1"}, "id": "scan-first-video", "name": "<PERSON>an First Video", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1000, 300]}, {"parameters": {"jsCode": "// Process scan result and create Step 2 log\nconst input = $input.first().json;\nconst commandOutput = input.stdout || '';\nconst exitCode = input.exitCode || 0;\n\nconst stepLog = {\n  step: '2_scan_videos',\n  timestamp: new Date().toISOString(),\n  exit_code: exitCode,\n  stdout_length: commandOutput.length,\n  files_found: commandOutput.trim() ? 1 : 0,\n  first_file: commandOutput.trim(),\n  status: exitCode === 0 ? 'completed' : 'failed'\n};\n\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(stepLog, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: '2_scan_log.json',\n  fileExtension: 'json'\n};\n\nif (exitCode !== 0 || !commandOutput || commandOutput.trim() === '') {\n  console.log('ERROR: No video files found');\n  return [{ json: { error: 'No video files found', log_filename: '2_scan_log.json' }, binary: { data: binaryData } }];\n}\n\nconst filePath = commandOutput.trim();\nconst filename = filePath.split('/').pop();\n\nconsole.log('=== STEP 2: VIDEO SCAN COMPLETED ===');\nreturn [{ json: { filename: filename, fullPath: filePath, log_filename: '2_scan_log.json' }, binary: { data: binaryData } }];"}, "id": "process-scan-with-log", "name": "Process Scan + Log", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1250, 300]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/' + $json.log_filename }}", "options": {"overwrite": true}}, "id": "write-step-2-log", "name": "Write Step 2 Log", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [1500, 300]}, {"parameters": {"command": "ffmpeg -i \"/home/<USER>/shared/kung_fu_videos/20250406_110016_1.mp4\" -ss 00:00:10 -vframes 1 -vf scale=320:240 -f image2pipe -vcodec png -"}, "id": "extract-thumbnail", "name": "Extract Thumbnail", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1750, 300]}, {"parameters": {"jsCode": "// Process FFmpeg result and create Step 3 log\nconst input = $input.first().json;\nconst rawImageData = input.stdout || '';\nconst exitCode = input.exitCode || 0;\nconst stderr = input.stderr || '';\n\nconst stepLog = {\n  step: '3_ffmpeg_extraction',\n  timestamp: new Date().toISOString(),\n  exit_code: exitCode,\n  raw_data_length: rawImageData.length,\n  stderr: stderr,\n  success: exitCode === 0 && rawImageData.length > 1000,\n  status: (exitCode === 0 && rawImageData.length > 1000) ? 'completed' : 'failed'\n};\n\nconst binaryData = {\n  data: Buffer.from(JSON.stringify(stepLog, null, 2), 'utf8').toString('base64'),\n  mimeType: 'application/json',\n  fileName: '3_ffmpeg_log.json',\n  fileExtension: 'json'\n};\n\nif (exitCode !== 0 || !rawImageData || rawImageData.length < 1000) {\n  console.log('ERROR: FFmpeg extraction failed');\n  return [{ json: { error: 'FFmpeg failed', log_filename: '3_ffmpeg_log.json' }, binary: { data: binaryData } }];\n}\n\nconst thumbnailBase64 = Buffer.from(rawImageData, 'binary').toString('base64');\nconst config = {\n  lm_studio: {\n    endpoint: 'http://host.docker.internal:1234/v1/chat/completions',\n    model: 'mimo-vl-7b-rl@q8_k_xl',\n    timeout: 30000,\n    temperature: 0.1,\n    max_tokens: 50\n  },\n  ai_prompt: 'Analyze this video thumbnail and determine if it shows kung fu practice or martial arts training.',\n  detection_keywords: ['yes', 'kung fu', 'martial arts', 'karate', 'taekwondo', 'fighting', 'combat', 'training', 'practice']\n};\n\nconsole.log('=== STEP 3: FFMPEG EXTRACTION COMPLETED ===');\nreturn [{ json: { filename: '20250406_110016_1.mp4', thumbnailBase64: thumbnailBase64, config: config, log_filename: '3_ffmpeg_log.json' }, binary: { data: binaryData } }];"}, "id": "process-thumbnail-with-log", "name": "Process Thumbnail + Log", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2000, 300]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/' + $json.log_filename }}", "options": {"overwrite": true}}, "id": "write-step-3-log", "name": "Write Step 3 Log", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [2250, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Load Config + Log", "type": "main", "index": 0}]]}, "Load Config + Log": {"main": [[{"node": "Write Step 1 Log", "type": "main", "index": 0}]]}, "Write Step 1 Log": {"main": [[{"node": "<PERSON>an First Video", "type": "main", "index": 0}]]}, "Scan First Video": {"main": [[{"node": "Process Scan + Log", "type": "main", "index": 0}]]}, "Process Scan + Log": {"main": [[{"node": "Write Step 2 Log", "type": "main", "index": 0}]]}, "Write Step 2 Log": {"main": [[{"node": "Extract Thumbnail", "type": "main", "index": 0}]]}, "Extract Thumbnail": {"main": [[{"node": "Process Thumbnail + Log", "type": "main", "index": 0}]]}, "Process Thumbnail + Log": {"main": [[{"node": "Write Step 3 Log", "type": "main", "index": 0}]]}}, "pinData": {}, "active": false, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-05T00:00:00.000Z", "versionId": "1"}